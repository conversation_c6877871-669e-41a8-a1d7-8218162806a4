#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试矢量GPS监控系统
"""

import requests
import webbrowser
import time

def test_vector_gps_system():
    """测试矢量GPS监控系统"""
    print("🗺️ 矢量GPS监控系统测试")
    print("=" * 60)
    
    # 测试GPS监控系统
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ GPS监控系统正常")
            print(f"📍 中心点: {config['center']}")
            print(f"🔍 初始缩放: {config['initial_zoom']}")
            print(f"📊 缩放范围: {config['min_zoom']}-{config['max_zoom']}")
            print(f"🌐 矢量数据: {config['vector_data_available']}")
        else:
            print(f"❌ GPS监控系统异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GPS监控系统连接失败: {e}")
        return False
    
    # 测试矢量数据区域列表
    try:
        response = requests.get('http://127.0.0.1:5000/api/vector-regions', timeout=5)
        if response.status_code == 200:
            data = response.json()
            regions = data['regions']
            print(f"✅ 矢量数据区域: {len(regions)} 个")
            print(f"📋 可用区域: {', '.join(regions[:5])}...")
        else:
            print(f"❌ 矢量数据区域获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 矢量数据区域获取失败: {e}")
        return False
    
    # 测试北京矢量数据
    try:
        response = requests.get('http://127.0.0.1:5000/api/vector-data/北京', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 北京矢量数据加载成功")
            for data_type, content in data.items():
                print(f"   📄 {data_type}: {len(content)} 字符")
        else:
            print(f"❌ 北京矢量数据加载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 北京矢量数据加载失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！")
    print("=" * 60)
    return True

def show_system_info():
    """显示系统信息"""
    print("\n📱 系统配置:")
    print("=" * 60)
    print("🌐 GPS监控系统: http://127.0.0.1:5000")
    print("🗺️  初始地图: 中国国家地图 (缩放级别: 10)")
    print("📍 中心坐标: [35.0, 105.0] (中国中心)")
    print("🔍 缩放范围: 3-18级")
    print("📊 矢量数据: 34个省市自治区")
    
    print("\n🎯 使用说明:")
    print("=" * 60)
    print("1. 系统启动后显示中国国家地图")
    print("2. 初始缩放级别为10，可以看到全国范围")
    print("3. 输入GPS坐标后点击'定位到GPS坐标'")
    print("4. 系统会自动定位到指定位置并加载该区域矢量数据")
    print("5. 支持机井管理和实时监控功能")
    
    print("\n📍 GPS定位示例:")
    print("=" * 60)
    print("北京天安门: 纬度 39.9042, 经度 116.4074")
    print("上海外滩: 纬度 31.2397, 经度 121.4999")
    print("广州塔: 纬度 23.1090, 经度 113.3245")
    print("成都春熙路: 纬度 30.6624, 经度 104.0633")

if __name__ == "__main__":
    success = test_vector_gps_system()
    show_system_info()
    
    if success:
        print("\n✅ 矢量GPS监控系统测试完成！")
        print("🌐 正在打开系统...")
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    else:
        print("\n❌ 系统测试失败，请检查错误信息！")
