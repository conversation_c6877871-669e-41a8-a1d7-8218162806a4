<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实矢量地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 300px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .control-panel {
            padding: 20px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-size: 14px;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .region-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .region-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .region-item:hover {
            background: #f8f9fa;
        }
        
        .region-item:last-child {
            border-bottom: none;
        }
        
        .region-name {
            font-weight: 500;
            color: #333;
        }
        
        .region-files {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .legend h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 3px;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .legend-text {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 真实矢量地图系统</h1>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="control-panel">
                <div class="control-group">
                    <h3>📍 GPS定位</h3>
                    <div class="form-group">
                        <label>纬度 (Latitude)</label>
                        <input type="number" id="latitude" step="0.000001" placeholder="30.295000">
                    </div>
                    <div class="form-group">
                        <label>经度 (Longitude)</label>
                        <input type="number" id="longitude" step="0.000001" placeholder="109.486000">
                    </div>
                    <button class="btn" onclick="locateByGPS()">GPS定位</button>
                </div>
                
                <div class="control-group">
                    <h3>🗺️ 区域选择</h3>
                    <div id="region-list" class="region-list">
                        <div class="status info">正在加载区域列表...</div>
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>🎛️ 图层控制</h3>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-roads" checked> 显示道路
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-buildings" checked> 显示建筑
                        </ </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-pois" checked> 显示POI
                        </label>
                    </div>
                </div>
                
                <div id="status" class="status info">
                    系统就绪，请选择区域或输入GPS坐标
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            
            <div class="legend">
                <h4>图例</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <div class="legend-text">主要道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff8800;"></div>
                    <div class="legend-text">次要道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <div class="legend-text">住宅道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ffff00;"></div>
                    <div class="legend-text">建筑</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #0066ff;"></div>
                    <div class="legend-text">POI</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let vectorLayers = [];
        let currentRegion = null;
        
        // 初始化地图
        function initMap() {
            map = L.map('map').setView([39.9042, 116.4074], 10);
            
            // 添加在线地图图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // 加载区域列表
            loadRegions();
        }
        
        // 加载区域列表
        async function loadRegions() {
            try {
                const response = await fetch('/api/regions');
                const regions = await response.json();
                
                const regionList = document.getElementById('region-list');
                regionList.innerHTML = '';
                
                if (regions.length === 0) {
                    regionList.innerHTML = '<div class="status error">未找到矢量数据</div>';
                    return;
                }
                
                regions.forEach(region => {
                    const regionItem = document.createElement('div');
                    regionItem.className = 'region-item';
                    regionItem.innerHTML = `
                        <div class="region-name">${region.name}</div>
                        <div class="region-files">${region.files.join(', ')}</div>
                    `;
                    regionItem.onclick = () => loadRegionData(region.name);
                    regionList.appendChild(regionItem);
                });
                
                updateStatus(`找到 ${regions.length} 个区域`, 'success');
                
            } catch (error) {
                console.error('加载区域列表失败:', error);
                updateStatus('加载区域列表失败', 'error');
            }
        }
        
        // 加载区域数据
        async function loadRegionData(regionName) {
            try {
                updateStatus(`正在加载 ${regionName} 数据...`, 'info');
                
                // 清除之前的矢量图层
                clearVectorLayers();
                
                const response = await fetch(`/api/vector-data/${regionName}`);
                const data = await response.json();
                
                if (data.error) {
                    updateStatus(`加载失败: ${data.error}`, 'error');
                    return;
                }
                
                currentRegion = regionName;
                
                // 渲染矢量数据
                renderVectorData(data);
                
                updateStatus(`${regionName} 数据加载完成: ${data.metadata.total_features}个要素`, 'success');
                
            } catch (error) {
                console.error('加载区域数据失败:', error);
                updateStatus('加载区域数据失败', 'error');
            }
        }
        
        // 渲染矢量数据
        function renderVectorData(data) {
            // 渲染道路
            if (data.roads && data.roads.features && document.getElementById('show-roads').checked) {
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#666';
                        let weight = 2;
                        
                        switch(highway) {
                            case 'primary':
                                color = '#ff0000';
                                weight = 4;
                                break;
                            case 'secondary':
                                color = '#ff8800';
                                weight = 3;
                                break;
                            case 'residential':
                                color = '#00ff00';
                                weight = 2;
                                break;
                            default:
                                color = '#0000ff';
                                weight = 2;
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 0.8
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway || '未知'}<br>
                                车道数: ${props.lanes || '未知'}<br>
                                限速: ${props.maxspeed || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(roadsLayer);
            }
            
            // 渲染建筑
            if (data.buildings && data.buildings.features && document.getElementById('show-buildings').checked) {
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: {
                        color: '#ff0000',
                        weight: 2,
                        fillColor: '#ffff00',
                        fillOpacity: 0.6
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building || '未知'}<br>
                                高度: ${props.height || '未知'}米<br>
                                用途: ${props.use || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(buildingsLayer);
            }
            
            // 渲染POI
            if (data.pois && data.pois.features && document.getElementById('show-pois').checked) {
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 6,
                            fillColor: '#0066ff',
                            color: '#ffffff',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.amenity || props.shop || props.tourism || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(poisLayer);
            }
            
            // 调整地图视图到数据范围
            if (vectorLayers.length > 0) {
                const group = new L.featureGroup(vectorLayers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
        }
        
        // 清除矢量图层
        function clearVectorLayers() {
            vectorLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            vectorLayers = [];
        }
        
        // GPS定位
        function locateByGPS() {
            const lat = parseFloat(document.getElementById('latitude').value);
            const lon = parseFloat(document.getElementById('longitude').value);
            
            if (isNaN(lat) || isNaN(lon)) {
                updateStatus('请输入有效的GPS坐标', 'error');
                return;
            }
            
            if (lat < -90 || lat > 90 || lon < -180 || lon > 180) {
                updateStatus('GPS坐标超出有效范围', 'error');
                return;
            }
            
            // 定位到指定坐标
            map.setView([lat, lon], 16);
            
            // 添加标记
            L.marker([lat, lon]).addTo(map)
                .bindPopup(`
                    <div style="font-size: 12px;">
                        <strong>GPS定位点</strong><br>
                        纬度: ${lat.toFixed(6)}<br>
                        经度: ${lon.toFixed(6)}
                    </div>
                `).openPopup();
            
            updateStatus(`已定位到: ${lat.toFixed(6)}, ${lon.toFixed(6)}`, 'success');
        }
        
        // 更新状态
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 图层控制事件
        document.getElementById('show-roads').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        document.getElementById('show-buildings').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        document.getElementById('show-pois').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
    </script>
</body>
</html>
