#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全国瓦片下载工具
先下载小范围瓦片验证功能
"""

from national_tile_downloader import NationalTileDownloader

def test_downloader():
    """测试下载器功能"""
    print("🧪 测试全国瓦片下载工具")
    print("=" * 40)
    
    downloader = NationalTileDownloader()
    
    # 显示可用地图源
    print("可用地图源:")
    for key, config in downloader.tile_sources.items():
        print(f"  {key}: {config['name']} (最大缩放: {config['max_zoom']})")
    
    # 测试不同地图源
    test_sources = ['osm', 'baidu', 'amap']
    
    for source in test_sources:
        print(f"\n🔍 测试地图源: {source}")
        downloader.set_source(source)
        
        # 下载北京天安门附近的小范围瓦片
        print("   下载北京天安门附近18级瓦片...")
        stats = downloader.download_region_tiles(
            center_lat=39.9042,
            center_lon=116.4074,
            zoom=18,
            radius_km=1,  # 1km半径，测试用
            max_workers=4
        )
        
        print(f"   结果: 成功{stats['success']}, 跳过{stats['skipped']}, 失败{stats['failed']}")
        
        # 重置统计
        downloader.download_stats = {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    test_downloader()
