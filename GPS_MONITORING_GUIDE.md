# GPS机井监控地图系统使用指南

## 系统概述

本系统专为野外机井作业监控设计，通过GPS定位、气体检测和送风机控制，实现机井的实时监控和管理。

## 主要功能

### 🎯 GPS定位功能
- **精确GPS定位**: 支持输入GPS坐标进行精确定位
- **当前位置获取**: 自动获取浏览器当前位置
- **附近机井搜索**: 根据GPS坐标搜索指定范围内的机井
- **实时轨迹追踪**: 记录和显示机井GPS历史轨迹

### 🔧 机井监控功能
- **实时状态监控**: 监控机井运行状态（正常/警告/故障）
- **气体浓度检测**: 实时显示气体检测仪数据
- **送风机控制**: 监控送风机开关状态
- **多地图图层**: 支持高德、百度、OSM、卫星图等多种地图源

### 📊 数据管理功能
- **机井信息管理**: 添加、编辑、删除机井信息
- **历史数据记录**: 保存GPS轨迹和传感器数据历史
- **统计信息显示**: 实时显示系统统计信息

## 系统启动

### 1. 启动监控系统
```bash
python start_gps_monitoring.py
```

系统将在 `http://127.0.0.1:5000` 启动，浏览器会自动打开。

### 2. 启动数据模拟器（可选）
```bash
python gps_data_simulator.py
```

用于模拟PLC设备发送GPS和传感器数据，便于测试系统功能。

## 使用说明

### GPS定位操作

1. **手动输入GPS坐标**
   - 在左侧面板的"GPS定位"区域
   - 输入纬度和经度坐标
   - 设置搜索半径（默认1000米）
   - 点击"📍 定位到GPS坐标"

2. **获取当前位置**
   - 点击"🌍 获取当前位置"按钮
   - 浏览器会请求位置权限
   - 自动获取并定位到当前位置

3. **搜索附近机井**
   - 定位后系统会自动搜索指定范围内的机井
   - 显示距离和机井信息
   - 在地图上标记附近机井位置

### 机井管理操作

1. **添加新机井**
   - 填写机井ID、名称
   - 输入GPS坐标（纬度、经度）
   - 点击"➕ 添加机井"

2. **更新机井GPS**
   - 输入机井ID
   - 输入新的GPS坐标
   - 点击"🔄 更新GPS"

3. **搜索机井**
   - 在搜索框输入机井名称或ID
   - 系统会实时显示搜索结果
   - 点击结果可定位到对应机井

### 地图操作

1. **地图图层切换**
   - 使用右上角的图层控制按钮
   - 可选择高德地图、百度地图、OSM、卫星图

2. **机井标记说明**
   - 🟢 绿色：正常状态（气体浓度<50%）
   - 🟡 黄色：警告状态（气体浓度50-80%）
   - 🔴 红色：危险状态（气体浓度>80%）

3. **机井信息查看**
   - 点击地图上的机井标记
   - 弹出窗口显示详细信息
   - 包括坐标、状态、气体浓度、送风机状态等

## API接口说明

### 机井管理接口

- `GET /api/wells` - 获取所有机井信息
- `POST /api/wells` - 添加新机井
- `PUT /api/wells/<well_id>` - 更新机井信息
- `POST /api/wells/<well_id>/gps` - 更新机井GPS坐标

### 定位搜索接口

- `POST /api/locate` - 根据GPS坐标定位
- `POST /api/search-location` - 搜索机井位置
- `GET /api/wells/<well_id>/gps-history` - 获取GPS历史轨迹

### 统计信息接口

- `GET /api/statistics` - 获取系统统计信息

## 数据格式

### 机井数据格式
```json
{
    "id": "WELL001",
    "name": "机井001",
    "lat": 39.9042,
    "lon": 116.4074,
    "status": "active",
    "gas_level": 25,
    "fan_status": "on",
    "last_update": "2024-01-01T12:00:00",
    "description": "机井描述"
}
```

### GPS定位请求格式
```json
{
    "lat": 39.9042,
    "lon": 116.4074,
    "radius": 1000
}
```

## 系统特点

### 🎯 高精度定位
- 支持6位小数精度的GPS坐标
- 实时GPS漂移补偿
- 历史轨迹记录和分析

### 🔄 实时监控
- 30秒自动刷新数据
- 实时状态更新
- 异常状态自动告警

### 🗺️ 多地图支持
- 高德地图（推荐）
- 百度地图
- OpenStreetMap
- 卫星影像图

### 📱 响应式设计
- 支持桌面和移动设备
- 自适应界面布局
- 触摸友好的操作体验

## 故障排除

### 常见问题

1. **GPS定位不准确**
   - 检查坐标格式是否正确
   - 确认坐标系统（WGS84）
   - 检查网络连接

2. **机井数据不更新**
   - 检查服务器连接状态
   - 确认API接口正常
   - 查看浏览器控制台错误信息

3. **地图无法加载**
   - 检查网络连接
   - 尝试切换地图图层
   - 清除浏览器缓存

### 技术支持

如遇到技术问题，请检查：
1. 系统日志输出
2. 浏览器开发者工具
3. 网络连接状态
4. 服务器运行状态

## 系统架构

```
GPS机井监控系统
├── 前端界面 (HTML/CSS/JavaScript)
│   ├── 地图显示 (Leaflet.js)
│   ├── 数据可视化
│   └── 用户交互
├── 后端服务 (Flask)
│   ├── API接口
│   ├── 数据管理
│   └── GPS处理
├── 数据存储
│   ├── 机井信息
│   ├── GPS历史
│   └── 传感器数据
└── 外部接口
    ├── PLC设备通信
    ├── GPS接收器
    └── 传感器数据
```

## 扩展功能

系统支持以下扩展：
- 多用户权限管理
- 数据导出功能
- 报警通知系统
- 移动端APP
- 云端数据同步

---

**注意**: 本系统专为野外机井作业监控设计，请确保在安全环境下使用，并定期备份重要数据。
