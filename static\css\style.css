* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 28px;
    margin-bottom: 10px;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.status-indicator {
    padding: 5px 10px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
}

.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    height: calc(100vh - 140px);
}

.map-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h2, .panel-header h3 {
    color: #495057;
    font-size: 18px;
}

.gps-info {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #6c757d;
}

#map {
    height: calc(100% - 70px);
    width: 100%;
}

.data-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.panel {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.gas-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 20px;
}

.gas-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.gas-item.warning {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.gas-item.danger {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.gas-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.gas-value {
    font-size: 20px;
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.gas-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    background: #d4edda;
    color: #155724;
}

.gas-status.warning {
    background: #fff3cd;
    color: #856404;
}

.gas-status.danger {
    background: #f8d7da;
    color: #721c24;
}

.equipment-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
}

.equipment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.equipment-label {
    font-weight: 500;
    color: #495057;
}

.equipment-status {
    font-weight: bold;
}

.equipment-status.running {
    color: #28a745;
}

.equipment-status.stopped {
    color: #dc3545;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
}

.status-indicator.running {
    background: #28a745;
}

.environment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 20px;
}

.env-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.env-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
}

.env-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 400px 1fr;
    }
    
    .gas-grid {
        grid-template-columns: 1fr;
    }
    
    .environment-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .gps-info {
        flex-direction: column;
        gap: 5px;
    }
}

/* 动画效果 */
.gas-item, .equipment-item, .env-item {
    transition: all 0.3s ease;
}

.gas-item:hover, .equipment-item:hover, .env-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 连接状态指示器 */
.status-indicator.connected {
    background-color: #28a745;
}

.status-indicator.disconnected {
    background-color: #dc3545;
}

.status-indicator.connecting {
    background-color: #ffc107;
}