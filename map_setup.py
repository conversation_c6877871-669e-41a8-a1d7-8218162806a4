#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线地图设置脚本
用于下载和配置OpenMapTiles离线地图
"""

import os
import sys
import json
import requests
import zipfile
import shutil
from pathlib import Path
import subprocess

class OpenMapTilesSetup:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.data_dir = self.base_dir / "map_data"
        self.config_file = self.base_dir / "map_config.json"
        
    def create_directories(self):
        """创建必要的目录"""
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {self.tiles_dir}")
        print(f"✅ 创建目录: {self.data_dir}")
    
    def download_sample_tiles(self):
        """下载示例瓦片数据"""
        print("📥 下载示例地图瓦片...")
        
        # 创建示例瓦片结构 (北京地区)
        sample_tiles = [
            # 缩放级别 10
            {"z": 10, "x": 838, "y": 387},
            {"z": 10, "x": 839, "y": 387},
            {"z": 10, "x": 838, "y": 388},
            {"z": 10, "x": 839, "y": 388},
            
            # 缩放级别 12
            {"z": 12, "x": 3352, "y": 1548},
            {"z": 12, "x": 3353, "y": 1548},
            {"z": 12, "x": 3352, "y": 1549},
            {"z": 12, "x": 3353, "y": 1549},
            
            # 缩放级别 15 (详细级别)
            {"z": 15, "x": 26819, "y": 12392},
            {"z": 15, "x": 26820, "y": 12392},
            {"z": 15, "x": 26819, "y": 12393},
            {"z": 15, "x": 26820, "y": 12393},
        ]
        
        # 使用OpenStreetMap作为示例数据源
        base_url = "https://tile.openstreetmap.org"
        
        downloaded = 0
        for tile in sample_tiles:
            z, x, y = tile["z"], tile["x"], tile["y"]
            tile_dir = self.tiles_dir / str(z) / str(x)
            tile_dir.mkdir(parents=True, exist_ok=True)
            
            tile_file = tile_dir / f"{y}.png"
            
            if not tile_file.exists():
                try:
                    url = f"{base_url}/{z}/{x}/{y}.png"
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        with open(tile_file, 'wb') as f:
                            f.write(response.content)
                        downloaded += 1
                        print(f"  ✅ 下载瓦片: {z}/{x}/{y}.png")
                    else:
                        print(f"  ❌ 下载失败: {z}/{x}/{y}.png")
                except Exception as e:
                    print(f"  ❌ 下载错误: {z}/{x}/{y}.png - {e}")
        
        print(f"📥 完成下载 {downloaded} 个瓦片文件")
        return downloaded > 0
    
    def create_mbtiles_config(self):
        """创建MBTiles配置"""
        config = {
            "name": "野外深机井监控地图",
            "description": "用于野外深机井监控系统的离线地图",
            "version": "1.0.0",
            "minzoom": 1,
            "maxzoom": 18,
            "center": [116.4074, 39.9042],  # 北京坐标
            "bounds": [115.4074, 38.9042, 117.4074, 40.9042],  # 地图边界
            "format": "png",
            "type": "baselayer",
            "attribution": "© OpenMapTiles © OpenStreetMap contributors"
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建地图配置: {self.config_file}")
        return config
    
    def setup_tileserver(self):
        """设置瓦片服务器"""
        print("🌐 设置本地瓦片服务器...")
        
        # 创建简单的瓦片服务器脚本
        tileserver_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的瓦片服务器
用于提供离线地图瓦片
"""

from flask import Flask, send_file, abort
from pathlib import Path
import os

app = Flask(__name__)
TILES_DIR = Path(__file__).parent / "static" / "tiles"

@app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
def serve_tile(z, x, y):
    """提供地图瓦片"""
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.png"
    
    if tile_path.exists():
        return send_file(tile_path, mimetype='image/png')
    else:
        # 返回空白瓦片或404
        abort(404)

@app.route('/tiles/<int:z>/<int:x>/<int:y>')
def serve_tile_no_ext(z, x, y):
    """提供地图瓦片 (无扩展名)"""
    return serve_tile(z, x, y)

if __name__ == '__main__':
    print(f"瓦片目录: {TILES_DIR}")
    print("瓦片服务器启动在: http://localhost:8080")
    app.run(host='0.0.0.0', port=8080, debug=False)
'''
        
        with open(self.base_dir / "tileserver.py", 'w', encoding='utf-8') as f:
            f.write(tileserver_script)
        
        print("✅ 创建瓦片服务器脚本: tileserver.py")
    
    def generate_offline_map_html(self):
        """生成离线地图测试页面"""
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            max-width: 300px;
        }
        .status { margin: 5px 0; }
        .status.success { color: green; }
        .status.error { color: red; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>离线地图测试</h3>
        <div class="status" id="tile-status">瓦片状态: 检查中...</div>
        <div class="status" id="location-status">位置: 北京</div>
        <div class="status" id="zoom-status">缩放级别: 15</div>
        <button onclick="testTileLoad()">测试瓦片加载</button>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map').setView([39.9042, 116.4074], 15);
        
        let tilesLoaded = 0;
        let tilesError = 0;
        
        // 离线瓦片图层
        const offlineLayer = L.tileLayer('/static/tiles/{z}/{x}/{y}.png', {
            attribution: '© OpenMapTiles © OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 1
        });
        
        // 备用在线图层 (仅用于对比测试)
        const onlineLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        });
        
        // 监听瓦片加载事件
        offlineLayer.on('tileload', function(e) {
            tilesLoaded++;
            updateStatus();
        });
        
        offlineLayer.on('tileerror', function(e) {
            tilesError++;
            updateStatus();
            console.warn('瓦片加载失败:', e.tile.src);
        });
        
        // 添加离线图层
        offlineLayer.addTo(map);
        
        // 图层控制
        const layerControl = L.control.layers({
            '离线地图': offlineLayer,
            '在线地图 (测试)': onlineLayer
        }).addTo(map);
        
        // 添加标记
        const marker = L.marker([39.9042, 116.4074])
            .addTo(map)
            .bindPopup('深机井监控点<br>北京市中心')
            .openPopup();
        
        // 更新状态显示
        function updateStatus() {
            const statusEl = document.getElementById('tile-status');
            if (tilesError > 0) {
                statusEl.textContent = `瓦片状态: ${tilesLoaded} 成功, ${tilesError} 失败`;
                statusEl.className = 'status error';
            } else if (tilesLoaded > 0) {
                statusEl.textContent = `瓦片状态: ${tilesLoaded} 个瓦片加载成功`;
                statusEl.className = 'status success';
            }
        }
        
        // 地图事件监听
        map.on('zoomend', function() {
            document.getElementById('zoom-status').textContent = `缩放级别: ${map.getZoom()}`;
        });
        
        map.on('moveend', function() {
            const center = map.getCenter();
            document.getElementById('location-status').textContent = 
                `位置: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
        });
        
        // 测试瓦片加载
        function testTileLoad() {
            tilesLoaded = 0;
            tilesError = 0;
            map.eachLayer(function(layer) {
                if (layer instanceof L.TileLayer) {
                    layer.redraw();
                }
            });
            setTimeout(updateStatus, 2000);
        }
        
        // 初始状态更新
        setTimeout(updateStatus, 3000);
    </script>
</body>
</html>'''
        
        with open(self.base_dir / "map_test.html", 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ 创建地图测试页面: map_test.html")
    
    def update_main_app(self):
        """更新主应用以支持离线地图"""
        print("🔧 更新主应用地图配置...")
        
        # 读取现有的app.js文件
        app_js_path = self.base_dir / "static" / "js" / "app.js"
        
        if app_js_path.exists():
            with open(app_js_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新地图初始化部分
            updated_content = content.replace(
                "L.tileLayer('/static/tiles/{z}/{x}/{y}.png', {",
                """// 优先使用离线瓦片，失败时自动切换到在线地图
        const offlineTileLayer = L.tileLayer('/static/tiles/{z}/{x}/{y}.png', {"""
            )
            
            # 添加瓦片服务器支持
            tile_server_code = '''
        // 如果离线瓦片不可用，尝试使用本地瓦片服务器
        const tileServerLayer = L.tileLayer('http://localhost:8080/tiles/{z}/{x}/{y}.png', {
            attribution: '© OpenMapTiles © OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 1
        });
        
        // 瓦片加载失败处理
        let tileErrorCount = 0;
        offlineTileLayer.on('tileerror', () => {
            tileErrorCount++;
            if (tileErrorCount > 5) {
                console.warn('离线瓦片加载失败次数过多，尝试使用瓦片服务器');
                this.map.removeLayer(offlineTileLayer);
                tileServerLayer.addTo(this.map);
            }
        });
        
        offlineTileLayer'''
            
            updated_content = updated_content.replace(
                "// 使用OpenMapTiles离线瓦片",
                tile_server_code
            )
            
            with open(app_js_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ 更新前端地图配置")
    
    def create_download_script(self):
        """创建OpenMapTiles下载脚本"""
        download_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenMapTiles数据下载脚本
支持从多个源下载地图数据
"""

import requests
import os
import sys
from pathlib import Path

def download_china_tiles():
    """下载中国地区的地图瓦片"""
    print("📥 开始下载中国地区地图瓦片...")
    
    # 中国主要城市坐标
    cities = {
        "北京": {"lat": 39.9042, "lng": 116.4074},
        "上海": {"lat": 31.2304, "lng": 121.4737},
        "广州": {"lat": 23.1291, "lng": 113.2644},
        "深圳": {"lat": 22.5431, "lng": 114.0579},
        "成都": {"lat": 30.5728, "lng": 104.0668},
        "西安": {"lat": 34.3416, "lng": 108.9398}
    }
    
    base_url = "https://tile.openstreetmap.org"
    tiles_dir = Path("static/tiles")
    tiles_dir.mkdir(parents=True, exist_ok=True)
    
    for city_name, coords in cities.items():
        print(f"\\n📍 下载 {city_name} 地区瓦片...")
        lat, lng = coords["lat"], coords["lng"]
        
        # 计算瓦片坐标
        for zoom in [10, 12, 15]:
            x = int((lng + 180.0) / 360.0 * (1 << zoom))
            y = int((1.0 - math.asinh(math.tan(math.radians(lat))) / math.pi) / 2.0 * (1 << zoom))
            
            # 下载周围的瓦片
            for dx in range(-2, 3):
                for dy in range(-2, 3):
                    tile_x, tile_y = x + dx, y + dy
                    download_tile(base_url, zoom, tile_x, tile_y, tiles_dir)

def download_tile(base_url, z, x, y, tiles_dir):
    """下载单个瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    if tile_file.exists():
        return True
    
    try:
        url = f"{base_url}/{z}/{x}/{y}.png"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            with open(tile_file, 'wb') as f:
                f.write(response.content)
            print(f"  ✅ {z}/{x}/{y}.png")
            return True
        else:
            print(f"  ❌ {z}/{x}/{y}.png (HTTP {response.status_code})")
            return False
    except Exception as e:
        print(f"  ❌ {z}/{x}/{y}.png ({e})")
        return False

if __name__ == "__main__":
    import math
    download_china_tiles()
'''
        
        with open(self.base_dir / "download_tiles.py", 'w', encoding='utf-8') as f:
            f.write(download_script)
        
        print("✅ 创建瓦片下载脚本: download_tiles.py")
    
    def run_setup(self):
        """运行完整的设置流程"""
        print("🗺️  OpenMapTiles 离线地图设置")
        print("=" * 50)
        
        try:
            # 1. 创建目录
            self.create_directories()
            
            # 2. 检查是否有OSM数据文件
            osm_file = Path("F:/monitor1/china-latest.osm.pbf")
            if osm_file.exists():
                print(f"✅ 发现OSM数据文件: {osm_file}")
                print("📋 处理选项:")
                print("1. 使用简化工具生成瓦片 (推荐)")
                print("2. 使用示例瓦片")
                
                choice = input("请选择 (1-2): ").strip()
                
                if choice == '1':
                    print("🌐 启动简化瓦片生成...")
                    try:
                        from simple_tile_generator import SimpleTileGenerator
                        generator = SimpleTileGenerator()
                        if generator.generate_china_tiles(priority_filter=2, zoom_levels=[10, 12, 15]):
                            print("✅ 瓦片生成完成")
                        else:
                            print("⚠️  瓦片生成失败，使用示例瓦片")
                            self.download_sample_tiles()
                    except Exception as e:
                        print(f"⚠️  瓦片生成出错: {e}")
                        self.download_sample_tiles()
                else:
                    print("📥 使用示例瓦片...")
                    self.download_sample_tiles()
            else:
                print("📥 未发现OSM数据文件，下载示例瓦片...")
                if self.download_sample_tiles():
                    print("✅ 示例瓦片下载完成")
                else:
                    print("⚠️  示例瓦片下载失败，但系统仍可运行")
            
            # 3. 创建配置
            self.create_mbtiles_config()
            
            # 4. 设置瓦片服务器
            self.setup_tileserver()
            
            # 5. 生成测试页面
            self.generate_offline_map_html()
            
            # 6. 更新主应用
            self.update_main_app()
            
            # 7. 创建下载脚本
            self.create_download_script()
            
            print("\n" + "=" * 50)
            print("🎉 离线地图设置完成!")
            print("\n📋 使用说明:")
            print("1. 测试离线地图: 打开 map_test.html")
            print("2. 启动瓦片服务器: python tileserver.py")
            print("3. 生成更多瓦片: python simple_tile_generator.py")
            print("4. 启动监控系统: python start_system.py test")
            print("\n🌐 访问地址:")
            print("- 监控系统: http://localhost:5000")
            print("- 瓦片服务器: http://localhost:8080")
            print("- 地图测试: 直接打开 map_test.html")
            
            return True
            
        except Exception as e:
            print(f"❌ 设置过程中发生错误: {e}")
            return False

def main():
    setup = OpenMapTilesSetup()
    return setup.run_setup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)