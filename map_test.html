<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            max-width: 300px;
        }
        .status { margin: 5px 0; }
        .status.success { color: green; }
        .status.error { color: red; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>离线地图测试</h3>
        <div class="status" id="tile-status">瓦片状态: 检查中...</div>
        <div class="status" id="location-status">位置: 北京</div>
        <div class="status" id="zoom-status">缩放级别: 15</div>
        <button onclick="testTileLoad()">测试瓦片加载</button>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图 - 调整初始缩放级别避开11级
        const map = L.map('map').setView([39.9042, 116.4074], 12);
        
        let tilesLoaded = 0;
        let tilesError = 0;
        
        // 离线瓦片图层
        const offlineLayer = L.tileLayer('./static/tiles/{z}/{x}/{y}.png', {
            attribution: '© OpenMapTiles © OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 10,  // 恢复原始最小缩放级别，11级瓦片已修复
            errorTileUrl: './images/error-tile.png',
            detectRetina: true
        }).addTo(map);
        
        offlineLayer.on('tileerror', function(e) {
            console.error('瓦片加载失败:', e.url);
            document.getElementById('tile-status').textContent = '瓦片状态: 加载失败';
            document.getElementById('tile-status').className = 'status error';
        });
        
        offlineLayer.on('load', function() {
            document.getElementById('tile-status').textContent = '瓦片状态: 加载成功';
            document.getElementById('tile-status').className = 'status success';
        });
        
        // 备用在线图层 (仅用于对比测试)
        const onlineLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        });
        
        // 监听瓦片加载事件
        offlineLayer.on('tileload', function(e) {
            tilesLoaded++;
            updateStatus();
        });
        
        offlineLayer.on('tileerror', function(e) {
            tilesError++;
            updateStatus();
            console.warn('瓦片加载失败:', e.tile.src);
        });
        
        // 添加离线图层
        offlineLayer.addTo(map);
        
        // 图层控制
        const layerControl = L.control.layers({
            '离线地图': offlineLayer,
            '在线地图 (测试)': onlineLayer
        }).addTo(map);
        
        // 添加标记
        const marker = L.marker([39.9042, 116.4074])
            .addTo(map)
            .bindPopup('深机井监控点<br>北京市中心')
            .openPopup();
        
        // 更新状态显示
        function updateStatus() {
            const statusEl = document.getElementById('tile-status');
            if (tilesError > 0) {
                statusEl.textContent = `瓦片状态: ${tilesLoaded} 成功, ${tilesError} 失败`;
                statusEl.className = 'status error';
            } else if (tilesLoaded > 0) {
                statusEl.textContent = `瓦片状态: ${tilesLoaded} 个瓦片加载成功`;
                statusEl.className = 'status success';
            }
        }
        
        // 地图事件监听
        map.on('zoomend', function() {
            document.getElementById('zoom-status').textContent = `缩放级别: ${map.getZoom()}`;
        });
        
        map.on('moveend', function() {
            const center = map.getCenter();
            document.getElementById('location-status').textContent = 
                `位置: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
        });
        
        // 测试瓦片加载
        function testTileLoad() {
            tilesLoaded = 0;
            tilesError = 0;
            map.eachLayer(function(layer) {
                if (layer instanceof L.TileLayer) {
                    layer.redraw();
                }
            });
            setTimeout(updateStatus, 2000);
        }
        
        // 初始状态更新
        setTimeout(updateStatus, 3000);
    </script>
</body>
</html>