#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试20米精度监控系统
"""

import requests
import json
import time

def test_20m_precision_system():
    """测试20米精度系统功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("🎯 20米精度监控系统测试")
    print("=" * 60)
    
    # 测试1: 系统连接
    print("🔧 测试1: 系统连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 系统连接正常")
        else:
            print(f"❌ 系统连接失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 系统连接失败: {e}")
        return
    
    # 测试2: 获取地图配置
    print("\n🔧 测试2: 获取地图配置...")
    try:
        response = requests.get(f"{base_url}/api/map-config", timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 地图配置获取成功")
            print(f"   - 中心坐标: {config['center']}")
            print(f"   - 缩放级别: {config['zoom']}")
            print(f"   - 最大缩放: {config['max_zoom']}")
            print(f"   - 精度级别: {config['precision_level']}")
        else:
            print(f"❌ 获取地图配置失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取地图配置失败: {e}")
        return
    
    # 测试3: GPS定位
    print("\n🔧 测试3: GPS定位测试...")
    try:
        response = requests.get(f"{base_url}/api/gps-locate?lat=30.295&lon=109.486", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ GPS定位成功")
            print(f"   - 坐标: {data['coordinates']}")
            print(f"   - 区域: {data['region']}")
            print(f"   - 精度: {data['precision']}")
            print(f"   - 状态: {data['status']}")
        else:
            print(f"❌ GPS定位失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ GPS定位失败: {e}")
    
    # 测试4: 获取区域列表
    print("\n🔧 测试4: 获取区域列表...")
    try:
        response = requests.get(f"{base_url}/api/regions", timeout=10)
        if response.status_code == 200:
            regions = response.json()
            print(f"✅ 区域列表获取成功: {len(regions)}个区域")
            for region in regions[:3]:  # 显示前3个区域
                print(f"   - {region['name']}: {len(region['files'])}个文件")
            if len(regions) > 3:
                print(f"   ... 还有 {len(regions) - 3} 个区域")
        else:
            print(f"❌ 获取区域列表失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取区域列表失败: {e}")
        return
    
    # 测试5: 获取超高精度数据
    if regions:
        test_region = regions[0]['name']
        print(f"\n🔧 测试5: 获取 {test_region} 超高精度数据...")
        try:
            response = requests.get(f"{base_url}/api/ultra-precision-data/{test_region}", timeout=15)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {test_region} 超高精度数据获取成功")
                print(f"   - 总要素数: {data['metadata']['total_features']}")
                print(f"   - 道路要素: {len(data['roads']['features'])}")
                print(f"   - 建筑要素: {len(data['buildings']['features'])}")
                print(f"   - POI要素: {len(data['pois']['features'])}")
                print(f"   - 机井要素: {len(data['wells']['features'])}")
                print(f"   - 精度: ±{data['metadata']['accuracy_meters']}米")
                print(f"   - 坐标精度: {data['metadata']['coordinate_precision']}位小数")
                
                # 检查数据质量
                if data['metadata']['total_features'] > 0:
                    print("✅ 数据质量良好，满足20米精度要求")
                else:
                    print("⚠️ 数据为空")
            else:
                print(f"❌ 获取超高精度数据失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 获取超高精度数据失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 20米精度系统测试完成！")
    print("🌐 请在浏览器中访问: http://127.0.0.1:5002")
    print("📱 系统特点:")
    print("  - 20米精度定位")
    print("  - 6位小数坐标精度")
    print("  - 真实OSM矢量数据")
    print("  - 机井监控功能")
    print("  - 野外作业专用")
    print("=" * 60)

if __name__ == "__main__":
    test_20m_precision_system()
