#!/usr/bin/env python3
"""
简化的20级瓦片生成器
基于18级瓦片通过插值和细化生成20级瓦片
"""

import os
import time
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFilter
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class SimpleLevel20Generator:
    """简化的20级瓦片生成器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.level18_dir = self.tiles_dir / "18"
        self.level20_dir = self.tiles_dir / "20"
        
        # 创建20级目录
        self.level20_dir.mkdir(parents=True, exist_ok=True)
    
    def get_level18_tiles(self):
        """获取所有18级瓦片"""
        tiles = []
        if not self.level18_dir.exists():
            return tiles
            
        for x_dir in self.level18_dir.iterdir():
            if x_dir.is_dir() and x_dir.name.isdigit():
                x = int(x_dir.name)
                for tile_file in x_dir.glob("*.png"):
                    if tile_file.stem.isdigit():
                        y = int(tile_file.stem)
                        tiles.append((18, x, y))
        
        return sorted(tiles)
    
    def enhance_tile_for_level20(self, img):
        """增强瓦片以适应20级显示"""
        # 放大到512x512 (20级标准尺寸)
        enhanced = img.resize((512, 512), Image.LANCZOS)
        
        # 应用轻微的锐化滤镜
        enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=120, threshold=3))
        
        # 增强对比度
        from PIL import ImageEnhance
        enhancer = ImageEnhance.Contrast(enhanced)
        enhanced = enhancer.enhance(1.1)
        
        return enhanced
    
    def generate_level20_tile(self, tile_info):
        """生成单个20级瓦片"""
        z18, x18, y18 = tile_info
        
        try:
            # 读取18级瓦片
            source_path = self.level18_dir / str(x18) / f"{y18}.png"
            if not source_path.exists():
                return False, f"源瓦片不存在: {z18}/{x18}/{y18}"
            
            source_img = Image.open(source_path)
            
            # 每个18级瓦片生成16个20级瓦片 (4x4)
            scale_factor = 2 ** (20 - 18)  # = 4
            success_count = 0
            
            for dx in range(scale_factor):
                for dy in range(scale_factor):
                    x20 = x18 * scale_factor + dx
                    y20 = y18 * scale_factor + dy
                    
                    # 检查瓦片是否已存在
                    tile_path = self.level20_dir / str(x20)
                    tile_path.mkdir(parents=True, exist_ok=True)
                    tile_file = tile_path / f"{y20}.png"
                    
                    if tile_file.exists() and tile_file.stat().st_size > 1000:
                        success_count += 1
                        continue
                    
                    # 从18级瓦片中提取对应区域
                    # 每个20级瓦片对应18级瓦片的1/4区域
                    crop_size = 256 // scale_factor  # = 64
                    left = dx * crop_size
                    top = dy * crop_size
                    right = left + crop_size
                    bottom = top + crop_size
                    
                    # 裁剪并放大
                    cropped = source_img.crop((left, top, right, bottom))
                    enhanced = self.enhance_tile_for_level20(cropped)
                    
                    # 保存瓦片
                    enhanced.save(tile_file, 'PNG', optimize=True)
                    success_count += 1
            
            return True, f"成功生成 {success_count}/16 个瓦片: {z18}/{x18}/{y18}"
            
        except Exception as e:
            return False, f"生成失败: {z18}/{x18}/{y18} - {e}"
    
    def generate_all_level20_tiles(self, max_workers=2):
        """生成所有20级瓦片"""
        print("\n🚀 简化20级瓦片生成器")
        print("=" * 50)
        
        # 获取18级瓦片
        level18_tiles = self.get_level18_tiles()
        if not level18_tiles:
            print("❌ 没有找到18级瓦片")
            return False
        
        print(f"📊 基于 {len(level18_tiles)} 个18级瓦片生成20级瓦片")
        print(f"📊 预计生成 {len(level18_tiles) * 16} 个20级瓦片")
        
        # 分批处理
        batch_size = 50
        total_success = 0
        total_failed = 0
        
        for i in range(0, len(level18_tiles), batch_size):
            batch = level18_tiles[i:i+batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(level18_tiles) + batch_size - 1) // batch_size
            
            print(f"\n🔄 批次 {batch_num}/{total_batches} ({len(batch)} 个源瓦片)")
            
            # 使用线程池处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                results = list(executor.map(self.generate_level20_tile, batch))
                
                batch_success = sum(1 for success, _ in results if success)
                batch_failed = len(results) - batch_success
                
                total_success += batch_success
                total_failed += batch_failed
                
                print(f"   ✅ 批次完成: {batch_success}/{len(batch)} 成功")
                
                # 显示一些结果示例
                for success, message in results[:3]:
                    status = "✅" if success else "❌"
                    print(f"   {status} {message}")
                
                if len(results) > 3:
                    print(f"   ... 还有 {len(results) - 3} 个结果")
            
            # 批次间休息
            time.sleep(0.5)
        
        success_rate = (total_success / len(level18_tiles)) * 100 if level18_tiles else 0
        
        print(f"\n🎉 20级瓦片生成完成!")
        print(f"📊 源瓦片: {len(level18_tiles)}")
        print(f"📊 成功: {total_success}")
        print(f"📊 失败: {total_failed}")
        print(f"📊 成功率: {success_rate:.1f}%")
        print(f"📊 预计生成瓦片: {total_success * 16}")
        print(f"\n📁 20级瓦片目录: {self.level20_dir}")
        
        return total_success > 0

def main():
    """主函数"""
    print("🎯 简化20级瓦片生成器")
    print("=" * 60)
    
    generator = SimpleLevel20Generator()
    
    try:
        # 使用适当的并行度
        cpu_count = multiprocessing.cpu_count()
        max_workers = min(3, cpu_count)
        
        success = generator.generate_all_level20_tiles(max_workers=max_workers)
        
        if success:
            print("\n✅ 20级瓦片生成完成！")
            print("🌐 现在可以在地图系统中使用20级瓦片了")
        else:
            print("\n❌ 20级瓦片生成失败")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断生成")
    except Exception as e:
        print(f"\n❌ 生成过程出错: {e}")

if __name__ == "__main__":
    main()
