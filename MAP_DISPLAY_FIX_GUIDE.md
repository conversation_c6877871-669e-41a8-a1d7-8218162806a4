# 地图显示问题修复指南

## 问题描述
GPS机井监控系统的地图无法显示，显示空白区域。

## 问题原因
1. **缩放级别不匹配**: 地图配置的缩放级别(8-15)与现有瓦片文件级别(18)不匹配
2. **瓦片服务器未启动**: 本地瓦片服务器没有运行
3. **地图配置错误**: 前端地图配置与后端API配置不一致

## 解决方案

### 1. 修复地图配置
已修改 `gps_well_monitoring_system.py` 中的地图配置：
```python
'min_zoom': 18,  # 修改为18级，匹配现有瓦片
'max_zoom': 18,  # 修改为18级，匹配现有瓦片
```

### 2. 修复HTML模板
已修改 `templates/gps_well_monitoring.html` 中的默认配置：
```javascript
min_zoom: 18,  // 修改为18级，匹配现有瓦片
max_zoom: 18,  // 修改为18级，匹配现有瓦片
```

### 3. 启动系统
```bash
# 启动GPS监控系统
python start_offline_gps_monitoring.py

# 或分别启动
python tileserver.py      # 瓦片服务器 (端口: 8080)
python start_gps_monitoring.py  # GPS监控系统 (端口: 5000)
```

## 系统状态检查

### 检查GPS监控系统
```bash
curl http://127.0.0.1:5000/api/map-config
```
应该返回：
```json
{
  "offline_mode": true,
  "tile_server_url": "http://127.0.0.1:8080",
  "tile_url_template": "http://127.0.0.1:8080/tiles/{z}/{x}/{y}.png",
  "min_zoom": 18,
  "max_zoom": 18,
  "center": [116.4074, 39.9042]
}
```

### 检查瓦片服务器
```bash
curl http://127.0.0.1:8080/config
```
应该返回：
```json
{
  "status": "ready",
  "tiles_dir": "static\\tiles",
  "min_zoom": 8,
  "max_zoom": 15
}
```

### 检查瓦片文件
```bash
curl -I http://127.0.0.1:8080/tiles/18/215828/99324.png
```
应该返回 HTTP 200 状态码。

## 访问地址

### 主要系统
- **GPS监控系统**: http://127.0.0.1:5000
- **瓦片服务器**: http://127.0.0.1:8080
- **地图测试页**: http://127.0.0.1:8081/test_offline_map.html

### 测试工具
- **系统测试**: `python test_offline_system.py`
- **地图修复**: `python fix_map_display.py`

## 故障排除

### 1. 地图仍不显示
- 清除浏览器缓存
- 刷新页面 (Ctrl+F5)
- 检查浏览器控制台错误
- 确认所有服务都在运行

### 2. 瓦片加载失败
- 检查瓦片服务器是否运行
- 确认瓦片文件存在
- 检查网络连接

### 3. GPS定位不工作
- 检查GPS监控系统API
- 确认机井数据存在
- 验证坐标格式

## 系统特点

### 离线模式
- ✅ 完全离线运行
- ✅ 使用本地瓦片文件
- ✅ 18级高精度缩放
- ✅ 快速加载速度

### GPS功能
- ✅ 精确GPS定位
- ✅ 机井实时监控
- ✅ 气体检测数据
- ✅ 送风机状态监控

### 地图功能
- ✅ 离线瓦片地图
- ✅ 机井标记显示
- ✅ 状态颜色区分
- ✅ 点击查看详情

## 使用说明

1. **启动系统**: 运行 `python start_offline_gps_monitoring.py`
2. **访问界面**: 打开 http://127.0.0.1:5000
3. **GPS定位**: 输入坐标或获取当前位置
4. **机井管理**: 添加、编辑、搜索机井
5. **实时监控**: 查看气体浓度和送风机状态

## 技术细节

### 瓦片文件结构
```
static/tiles/18/
├── 215828/
│   ├── 99324.png
│   ├── 99325.png
│   └── ...
└── 215829/
    ├── 99324.png
    └── ...
```

### 地图配置
- **缩放级别**: 18级 (固定)
- **中心坐标**: 北京 (116.4074, 39.9042)
- **瓦片格式**: PNG
- **坐标系**: WGS84

### API接口
- `GET /api/map-config` - 获取地图配置
- `GET /api/wells` - 获取机井列表
- `POST /api/locate` - GPS定位
- `GET /api/statistics` - 系统统计

---

**注意**: 本系统专为内网环境设计，确保在安全环境下使用，并定期备份重要数据。
