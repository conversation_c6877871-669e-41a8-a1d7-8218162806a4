#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenMapTiles数据下载脚本
支持从多个源下载地图数据
"""

import requests
import os
import sys
from pathlib import Path

def download_china_tiles():
    """下载中国地区的地图瓦片"""
    print("📥 开始下载中国地区地图瓦片...")
    
    # 中国主要城市坐标
    cities = {
        "北京": {"lat": 39.9042, "lng": 116.4074},
        "上海": {"lat": 31.2304, "lng": 121.4737},
        "广州": {"lat": 23.1291, "lng": 113.2644},
        "深圳": {"lat": 22.5431, "lng": 114.0579},
        "成都": {"lat": 30.5728, "lng": 104.0668},
        "西安": {"lat": 34.3416, "lng": 108.9398}
    }
    
    base_url = "https://tile.openstreetmap.org"
    tiles_dir = Path("static/tiles")
    tiles_dir.mkdir(parents=True, exist_ok=True)
    
    for city_name, coords in cities.items():
        print(f"\n📍 下载 {city_name} 地区瓦片...")
        lat, lng = coords["lat"], coords["lng"]
        
        # 计算瓦片坐标
        for zoom in [10, 12, 15]:
            x = int((lng + 180.0) / 360.0 * (1 << zoom))
            y = int((1.0 - math.asinh(math.tan(math.radians(lat))) / math.pi) / 2.0 * (1 << zoom))
            
            # 下载周围的瓦片
            for dx in range(-2, 3):
                for dy in range(-2, 3):
                    tile_x, tile_y = x + dx, y + dy
                    download_tile(base_url, zoom, tile_x, tile_y, tiles_dir)

def download_tile(base_url, z, x, y, tiles_dir):
    """下载单个瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    if tile_file.exists():
        return True
    
    try:
        url = f"{base_url}/{z}/{x}/{y}.png"
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            with open(tile_file, 'wb') as f:
                f.write(response.content)
            print(f"  ✅ {z}/{x}/{y}.png")
            return True
        else:
            print(f"  ❌ {z}/{x}/{y}.png (HTTP {response.status_code})")
            return False
    except Exception as e:
        print(f"  ❌ {z}/{x}/{y}.png ({e})")
        return False

if __name__ == "__main__":
    import math
    download_china_tiles()
