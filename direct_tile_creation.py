#!/usr/bin/env python3

from pathlib import Path
from PIL import Image, ImageFilter, ImageEnhance

# 直接创建测试瓦片
base_dir = Path(".")
level18_dir = base_dir / "static" / "tiles" / "18"
level20_dir = base_dir / "static" / "tiles" / "20"

# 确保20级目录存在
level20_dir.mkdir(parents=True, exist_ok=True)

# 选择一个18级瓦片进行测试
source_path = level18_dir / "215828" / "99332.png"

print(f"检查源瓦片: {source_path}")
print(f"存在: {source_path.exists()}")

if source_path.exists():
    print("读取源瓦片...")
    source_img = Image.open(source_path)
    print(f"源瓦片尺寸: {source_img.size}")
    
    # 18级瓦片 215828/99332 对应的20级瓦片坐标
    x18, y18 = 215828, 99332
    scale_factor = 4  # 2^(20-18) = 4
    
    created_count = 0
    
    for dx in range(scale_factor):
        for dy in range(scale_factor):
            x20 = x18 * scale_factor + dx
            y20 = y18 * scale_factor + dy
            
            print(f"创建瓦片: 20/{x20}/{y20}")
            
            # 创建目录
            tile_dir = level20_dir / str(x20)
            tile_dir.mkdir(parents=True, exist_ok=True)
            tile_file = tile_dir / f"{y20}.png"
            
            # 从18级瓦片中提取对应区域
            crop_size = 256 // scale_factor  # = 64
            left = dx * crop_size
            top = dy * crop_size
            right = left + crop_size
            bottom = top + crop_size
            
            print(f"  裁剪区域: ({left}, {top}, {right}, {bottom})")
            
            # 裁剪并放大
            cropped = source_img.crop((left, top, right, bottom))
            print(f"  裁剪后尺寸: {cropped.size}")
            
            # 放大到512x512 (20级标准尺寸)
            enhanced = cropped.resize((512, 512), Image.LANCZOS)
            
            # 应用轻微的锐化滤镜
            enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=120, threshold=3))
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(enhanced)
            enhanced = enhancer.enhance(1.1)
            
            # 保存瓦片
            enhanced.save(tile_file, 'PNG', optimize=True)
            created_count += 1
            
            print(f"  ✅ 保存到: {tile_file}")
    
    print(f"\n🎉 成功创建 {created_count} 个20级测试瓦片")
else:
    print("❌ 源瓦片不存在")

print("完成！")
