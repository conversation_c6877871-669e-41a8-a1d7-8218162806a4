#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速瓦片下载脚本 - 专门解决14级瓦片空白问题
"""

import os
import requests
import math
from pathlib import Path
import time

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def download_tile(x, y, z, output_dir):
    """下载单个瓦片"""
    # 创建目录
    tile_dir = Path(output_dir) / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    
    tile_path = tile_dir / f"{y}.png"
    
    # 如果文件已存在且大小合理，跳过
    if tile_path.exists() and tile_path.stat().st_size > 2000:
        return True, f"跳过 {z}/{x}/{y}"
    
    # OpenStreetMap瓦片URL
    url = f"https://tile.openstreetmap.org/{z}/{x}/{y}.png"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        if len(response.content) < 1000:
            return False, f"瓦片 {z}/{x}/{y} 内容过小"
        
        with open(tile_path, 'wb') as f:
            f.write(response.content)
        
        return True, f"下载 {z}/{x}/{y} ({len(response.content)} bytes)"
        
    except Exception as e:
        return False, f"下载 {z}/{x}/{y} 失败: {e}"

def download_beijing_zoom14():
    """专门下载北京地区14级瓦片"""
    print("🚀 快速下载北京地区14级瓦片")
    print("=" * 40)
    
    # 北京天安门坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 14级瓦片覆盖范围更大，我们下载北京及周边
    radius_km = 30  # 30km半径
    lat_range = radius_km / 111.0
    lon_range = radius_km / (111.0 * math.cos(math.radians(beijing_lat)))
    
    min_lat = beijing_lat - lat_range
    max_lat = beijing_lat + lat_range
    min_lon = beijing_lon - lon_range
    max_lon = beijing_lon + lon_range
    
    z = 14
    
    # 计算瓦片范围
    min_x, max_y = deg2num(max_lat, min_lon, z)
    max_x, min_y = deg2num(min_lat, max_lon, z)
    
    print(f"边界框: ({min_lat:.4f}, {min_lon:.4f}) 到 ({max_lat:.4f}, {max_lon:.4f})")
    print(f"瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
    
    total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
    print(f"需要下载 {total_tiles} 个瓦片")
    
    output_dir = "static/tiles"
    success_count = 0
    
    for x in range(min_x, max_x + 1):
        for y in range(min_y, max_y + 1):
            success, message = download_tile(x, y, z, output_dir)
            if success:
                success_count += 1
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
            
            # 避免请求过快
            time.sleep(0.1)
    
    print(f"\n🎉 完成! 成功下载 {success_count}/{total_tiles} 个瓦片")
    print(f"瓦片保存在: {output_dir}/{z}/")

def download_multiple_zooms():
    """下载多个缩放级别的瓦片"""
    print("🚀 下载多个缩放级别的瓦片")
    print("=" * 40)
    
    # 北京天安门坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 下载缩放级别 10-15
    zoom_levels = [10, 11, 12, 13, 14, 15]
    radius_km = 20  # 20km半径
    
    lat_range = radius_km / 111.0
    lon_range = radius_km / (111.0 * math.cos(math.radians(beijing_lat)))
    
    min_lat = beijing_lat - lat_range
    max_lat = beijing_lat + lat_range
    min_lon = beijing_lon - lon_range
    max_lon = beijing_lon + lon_range
    
    output_dir = "static/tiles"
    total_success = 0
    total_tiles = 0
    
    for z in zoom_levels:
        print(f"\n📊 下载缩放级别 {z}")
        
        # 计算瓦片范围
        min_x, max_y = deg2num(max_lat, min_lon, z)
        max_x, min_y = deg2num(min_lat, max_lon, z)
        
        # 确保范围正确
        if min_x > max_x:
            min_x, max_x = max_x, min_x
        if min_y > max_y:
            min_y, max_y = max_y, min_y
        
        zoom_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        total_tiles += zoom_tiles
        
        print(f"瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
        print(f"需要下载 {zoom_tiles} 个瓦片")
        
        zoom_success = 0
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                success, message = download_tile(x, y, z, output_dir)
                if success:
                    zoom_success += 1
                    total_success += 1
                
                # 避免请求过快
                time.sleep(0.05)
        
        print(f"✅ 缩放级别 {z} 完成: {zoom_success}/{zoom_tiles}")
    
    print(f"\n🎉 全部完成!")
    print(f"总瓦片数: {total_tiles}")
    print(f"成功下载: {total_success}")
    print(f"成功率: {total_success/total_tiles*100:.1f}%")

def main():
    """主函数"""
    print("🗺️  快速瓦片下载工具")
    print("=" * 50)
    print("1. 只下载14级瓦片 (快速)")
    print("2. 下载多个缩放级别 (10-15)")
    
    choice = input("请选择 (默认: 1): ").strip() or '1'
    
    if choice == '1':
        download_beijing_zoom14()
    else:
        download_multiple_zooms()

if __name__ == "__main__":
    main()
