#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端超高精度数据调用
"""

import requests
import json

def test_ultra_precision_api():
    """测试超高精度数据API"""
    print("🔧 测试超高精度数据API...")
    
    # 测试恩施的超高精度数据
    try:
        response = requests.get('http://127.0.0.1:5000/api/ultra-precision-data/恩施', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 恩施超高精度数据API正常")
            print(f"   - 总要素数: {data.get('metadata', {}).get('total_features', 'N/A')}")
            print(f"   - 坐标精度: {data.get('metadata', {}).get('coordinate_precision', 'N/A')}位小数")
            print(f"   - 精度: ±{data.get('metadata', {}).get('accuracy_meters', 'N/A')}米")
            
            # 检查数据结构
            if 'roads' in data and data['roads']['features']:
                first_road = data['roads']['features'][0]
                coords = first_road['geometry']['coordinates'][0]
                print(f"   - 道路坐标示例: {coords}")
                
            if 'buildings' in data and data['buildings']['features']:
                first_building = data['buildings']['features'][0]
                coords = first_building['geometry']['coordinates'][0]
                print(f"   - 建筑坐标示例: {coords}")
                
            if 'pois' in data and data['pois']['features']:
                first_poi = data['pois']['features'][0]
                coords = first_poi['geometry']['coordinates']
                print(f"   - POI坐标示例: {coords}")
                
            return True
        else:
            print(f"❌ API响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

def test_map_config():
    """测试地图配置"""
    print("\n🔧 测试地图配置...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print(f"✅ 地图配置正常")
            print(f"   - 在线地图精度: {config.get('min_zoom', 'N/A')}-{config.get('max_zoom', 'N/A')}")
            print(f"   - 矢量数据精度: {config.get('vector_max_zoom', 'N/A')}级")
            print(f"   - 矢量数据可用: {config.get('vector_data_available', 'N/A')}")
            return True
        else:
            print(f"❌ 地图配置响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 地图配置调用失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 前端超高精度数据测试")
    print("=" * 50)
    
    # 测试地图配置
    if not test_map_config():
        print("\n❌ 地图配置测试失败")
        return
    
    # 测试超高精度数据API
    if not test_ultra_precision_api():
        print("\n❌ 超高精度数据API测试失败")
        return
    
    print("\n✅ 所有测试通过！")
    print("💡 前端应该能够正确加载和渲染超高精度数据")
    print("💡 如果地图显示仍然粗糙，请检查浏览器控制台是否有JavaScript错误")

if __name__ == "__main__":
    main()
