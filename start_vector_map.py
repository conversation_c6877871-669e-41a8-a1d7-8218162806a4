#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动矢量地图查看器
集成矢量数据下载和地图显示功能
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_vector_data():
    """检查矢量数据是否存在"""
    vector_dir = Path("static/vector_data")
    
    if not vector_dir.exists():
        print("❌ 矢量数据目录不存在")
        return False
    
    # 检查是否有区域数据
    regions = [d for d in vector_dir.iterdir() if d.is_dir()]
    
    if not regions:
        print("❌ 没有找到矢量数据")
        print("   请先运行: python simple_vector_downloader.py")
        return False
    
    print(f"✅ 找到 {len(regions)} 个区域的矢量数据:")
    for region in regions:
        files = list(region.glob("*.xml"))
        print(f"   - {region.name}: {len(files)} 个文件")
    
    return True

def start_vector_map_viewer():
    """启动矢量地图查看器"""
    print("🚀 启动矢量地图查看器...")
    
    try:
        # 启动Flask应用
        subprocess.run([sys.executable, "vector_map_viewer.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 矢量地图查看器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🗺️  矢量地图系统")
    print("="*60)
    print("1. 下载矢量数据")
    print("2. 启动地图查看器")
    print("3. 检查数据状态")
    print("4. 快速测试")
    print("0. 退出")
    print("="*60)

def download_vector_data():
    """下载矢量数据"""
    print("📥 启动矢量数据下载工具...")
    
    try:
        subprocess.run([sys.executable, "simple_vector_downloader.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 下载已取消")
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def quick_test():
    """快速测试"""
    print("🧪 运行快速测试...")
    
    try:
        subprocess.run([sys.executable, "test_simple_vector.py"], check=True)
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🗺️  矢量地图系统启动器")
    print("=" * 50)
    
    while True:
        show_menu()
        choice = input("请选择功能 (0-4): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            download_vector_data()
        elif choice == '2':
            if check_vector_data():
                start_vector_map_viewer()
            else:
                print("\n请先下载矢量数据!")
                input("按回车键继续...")
        elif choice == '3':
            check_vector_data()
            input("按回车键继续...")
        elif choice == '4':
            quick_test()
            input("按回车键继续...")
        else:
            print("❌ 无效选择")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
