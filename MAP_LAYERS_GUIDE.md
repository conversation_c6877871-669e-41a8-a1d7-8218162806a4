# 🗺️ 地图图层选择功能指南

## 📋 问题说明

您提到"未提供提供了多种地图源选择"，这可能是指地图图层选择功能没有正常显示或工作。

## ✅ 已实现的功能

### 1. 多种地图图层
系统已配置了以下地图图层：

- **高德地图** - 中文显示友好，适合中国地区
- **百度地图** - 提供详细的中文标注
- **OpenStreetMap** - 开源地图，全球覆盖
- **卫星图** - 真实地形显示

### 2. 图层控制面板
- 右上角应该显示图层控制按钮
- 点击可以切换不同的地图图层
- 支持18级缩放精度

## 🔧 如何访问图层选择功能

### 方法1: 主系统
1. 启动主系统：`python start_integrated_system.py`
2. 访问：`http://127.0.0.1:5000`
3. 在地图右上角查找图层控制按钮

### 方法2: 测试页面
1. 启动测试服务器：`python test_server.py`
2. 访问：`http://localhost:8080/test_map_layers.html`
3. 这是一个专门用于测试地图图层的页面

## 🎯 图层控制位置

图层控制按钮通常位于地图的右上角，显示为：
- 一个带有图层的图标
- 或者显示"图层"、"Layers"文字
- 点击后会弹出图层选择菜单

## 🔍 故障排除

### 如果看不到图层控制按钮：

1. **检查浏览器兼容性**
   - 使用现代浏览器（Chrome、Firefox、Edge）
   - 确保JavaScript已启用

2. **清除浏览器缓存**
   - 按 Ctrl+F5 强制刷新
   - 或清除浏览器缓存

3. **检查网络连接**
   - 确保能访问地图瓦片服务
   - 检查防火墙设置

4. **查看浏览器控制台**
   - 按 F12 打开开发者工具
   - 查看 Console 标签是否有错误信息

### 如果图层切换不工作：

1. **检查地图服务**
   - 高德地图：`https://webrd0{s}.is.autonavi.com/`
   - 百度地图：`https://maponline{s}.bdimg.com/`
   - OpenStreetMap：`https://{s}.tile.openstreetmap.org/`
   - 卫星图：`https://server.arcgisonline.com/`

2. **尝试不同的图层**
   - 如果某个图层不工作，尝试其他图层
   - 卫星图通常比较稳定

## 🧪 测试步骤

### 1. 基本测试
```bash
# 启动主系统
python start_integrated_system.py

# 在另一个终端启动测试服务器
python test_server.py
```

### 2. 功能验证
1. 打开浏览器访问测试页面
2. 查看右上角是否有图层控制按钮
3. 点击图层控制按钮
4. 尝试切换不同的地图图层
5. 观察地图的变化

### 3. 预期结果
- 能看到4个地图图层选项
- 点击后地图会切换到对应的图层
- 不同图层显示不同的地图样式
- 支持高精度缩放（最大18级）

## 📱 使用建议

### 推荐设置
- **中国地区**：使用高德地图或百度地图
- **全球范围**：使用OpenStreetMap
- **地形查看**：使用卫星图

### 性能优化
- 如果某个图层加载慢，尝试其他图层
- 卫星图数据量较大，可能需要更好的网络连接
- 高德和百度地图在中国地区通常加载更快

## 🆘 如果问题仍然存在

如果按照上述步骤仍然无法看到图层选择功能，请：

1. **检查系统状态**
   ```bash
   python test_map_quality.py
   ```

2. **查看系统日志**
   - 检查终端输出的错误信息
   - 查看浏览器控制台的错误

3. **重新安装依赖**
   ```bash
   pip install flask leaflet
   ```

4. **使用备用方案**
   - 直接访问测试页面：`http://localhost:8080/test_map_layers.html`
   - 这个页面专门用于测试地图图层功能

---

**💡 提示：图层控制功能是Leaflet.js的标准功能，如果正确配置应该能正常显示。如果仍有问题，请提供具体的错误信息或截图。**
