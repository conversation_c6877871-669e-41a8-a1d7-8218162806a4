#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询格式调试工具
专门调试Overpass查询格式问题
"""

import requests
import time

def test_query_format():
    """测试查询格式"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同的查询格式
    test_queries = {
        "minimal": """
[out:xml][timeout:30];
(
  way["highway"="primary"](39.9,116.4,40.0,116.5);
);
out geom;
""",
        "simple_roads": """
[out:xml][timeout:30];
(
  way["highway"~"^(primary|secondary)"](39.9,116.4,40.0,116.5);
);
out geom;
""",
        "simple_buildings": """
[out:xml][timeout:30];
(
  way["building"](39.9,116.4,40.0,116.5);
);
out geom;
""",
        "simple_pois": """
[out:xml][timeout:30];
(
  node["amenity"](39.9,116.4,40.0,116.5);
);
out;
""",
        "complex_roads": """
[out:xml][timeout:30];
(
  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)"](39.9,116.4,40.0,116.5);
);
out geom;
""",
        "complex_buildings": """
[out:xml][timeout:30];
(
  way["building"](39.9,116.4,40.0,116.5);
  way["building:part"](39.9,116.4,40.0,116.5);
  relation["building"](39.9,116.4,40.0,116.5);
  way["amenity"~"^(school|hospital|university|restaurant|hotel|bank|police|park|garden)"](39.9,116.4,40.0,116.5);
  way["shop"](39.9,116.4,40.0,116.5);
  way["office"](39.9,116.4,40.0,116.5);
  way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool)"](39.9,116.4,40.0,116.5);
);
out geom;
"""
    }
    
    print("🔍 测试查询格式...")
    print("=" * 50)
    
    for name, query in test_queries.items():
        print(f"\n📋 测试 {name} 查询:")
        print(f"查询内容:")
        print(query)
        print("-" * 30)
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/xml, text/xml, */*',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 成功: {len(response.content)} 字节")
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)  # 避免请求过快

def test_bbox_formats():
    """测试边界框格式"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同的边界框格式
    bbox_formats = [
        "39.9,116.4,40.0,116.5",  # 标准格式
        "39.9, 116.4, 40.0, 116.5",  # 带空格
        "39.900000,116.400000,40.000000,116.500000",  # 高精度
        "39.9,116.4,40.0,116.5,",  # 多余逗号
        "39.9,116.4,40.0",  # 缺少坐标
        "39.9,116.4,40.0,116.5,116.6",  # 多余坐标
    ]
    
    base_query = """
[out:xml][timeout:30];
(
  way["highway"="primary"]({bbox});
);
out geom;
"""
    
    print("\n🔍 测试边界框格式...")
    print("=" * 50)
    
    for bbox in bbox_formats:
        print(f"\n📋 测试边界框: {bbox}")
        
        query = base_query.format(bbox=bbox)
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 成功: {len(response.content)} 字节")
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(1)

def test_encoding():
    """测试编码问题"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    query = """
[out:xml][timeout:30];
(
  way["highway"="primary"](39.9,116.4,40.0,116.5);
);
out geom;
"""
    
    print("\n🔍 测试编码问题...")
    print("=" * 50)
    
    # 测试不同的编码方式
    encodings = [
        ("utf-8", query.encode('utf-8')),
        ("ascii", query.encode('ascii')),
        ("latin-1", query.encode('latin-1')),
    ]
    
    for encoding_name, encoded_query in encodings:
        print(f"\n📋 测试 {encoding_name} 编码:")
        
        try:
            response = requests.post(
                base_url,
                data={"data": encoded_query},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 成功: {len(response.content)} 字节")
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(1)

def main():
    """主函数"""
    print("🔧 查询格式调试工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择测试类型:")
        print("1. 测试查询格式")
        print("2. 测试边界框格式")
        print("3. 测试编码问题")
        print("4. 全部测试")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            test_query_format()
        
        elif choice == "2":
            test_bbox_formats()
        
        elif choice == "3":
            test_encoding()
        
        elif choice == "4":
            test_query_format()
            test_bbox_formats()
            test_encoding()
        
        elif choice == "5":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
