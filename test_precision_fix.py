#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精度修复效果
"""

import requests
import webbrowser
import time

def test_precision_fix():
    """测试精度修复效果"""
    print("🔧 精度修复测试")
    print("=" * 60)
    
    # 测试GPS监控系统
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ GPS监控系统正常")
            print(f"📍 中心点: {config['center']}")
            print(f"🔍 初始缩放: {config['initial_zoom']}")
            print(f"📊 在线地图范围: {config['min_zoom']}-{config['max_zoom']}")
            print(f"🎯 矢量数据支持: {config['vector_max_zoom']}级")
            print(f"🌐 矢量数据: {config['vector_data_available']}")
            
            # 检查修复效果
            if config['max_zoom'] == 18 and config['vector_max_zoom'] == 20:
                print("✅ 精度修复成功")
                print("   - 在线地图最大精度: 18级")
                print("   - 矢量数据支持: 20级")
                print("   - 智能精度处理: 19-20级自动使用18级+矢量数据")
            else:
                print(f"⚠️  配置异常: max_zoom={config['max_zoom']}, vector_max_zoom={config['vector_max_zoom']}")
                
        else:
            print(f"❌ GPS监控系统异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GPS监控系统连接失败: {e}")
        return False
    
    print("\n🎯 精度级别说明 (修复后):")
    print("=" * 60)
    print("15级 - 城市级别 (约1-2公里精度)")
    print("16级 - 区域级别 (约500米精度)")
    print("17级 - 街道级别 (约100-200米精度)")
    print("18级 - 高精度 (约50-100米精度) - 在线地图最高")
    print("19级 - 超高精度 (约10-20米精度) - 18级地图+矢量数据")
    print("20级 - 最高精度 (约1-5米精度) - 18级地图+矢量数据")
    
    print("\n🔧 修复内容:")
    print("=" * 60)
    print("1. 在线地图最大精度限制为18级")
    print("2. 矢量数据支持20级精度")
    print("3. 19-20级自动使用18级在线地图+矢量数据组合")
    print("4. 智能精度处理，避免地图空白")
    print("5. 用户界面提示精度级别说明")
    
    print("\n📍 测试坐标:")
    print("=" * 60)
    print("恩施市中心: 纬度 30.295, 经度 109.486")
    print("北京天安门: 纬度 39.9042, 经度 116.4074")
    print("上海外滩: 纬度 31.2397, 经度 121.4999")
    
    print("\n🎯 测试步骤:")
    print("=" * 60)
    print("1. 输入GPS坐标")
    print("2. 选择19级或20级精度")
    print("3. 点击'定位到GPS坐标'")
    print("4. 观察地图是否正常显示内容")
    print("5. 点击'最高精度定位'测试一键定位")
    
    return True

def show_technical_details():
    """显示技术细节"""
    print("\n🔧 技术实现细节:")
    print("=" * 60)
    print("问题原因:")
    print("- 在线地图服务在19-20级没有瓦片数据")
    print("- 直接使用20级导致地图背景空白")
    
    print("\n解决方案:")
    print("- 限制在线地图最大精度为18级")
    print("- 19-20级使用18级在线地图作为底图")
    print("- 叠加矢量数据提供高精度细节")
    print("- 智能精度处理，自动降级到可用精度")
    
    print("\n用户体验:")
    print("- 保持20级精度选项")
    print("- 自动处理精度兼容性")
    print("- 显示实际使用的精度级别")
    print("- 提供精度说明和提示")

if __name__ == "__main__":
    success = test_precision_fix()
    show_technical_details()
    
    if success:
        print("\n✅ 精度修复测试完成！")
        print("🌐 正在打开系统...")
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
        print("\n💡 提示: 现在可以正常使用19-20级精度，地图将显示内容！")
    else:
        print("\n❌ 测试失败，请检查错误信息！")
