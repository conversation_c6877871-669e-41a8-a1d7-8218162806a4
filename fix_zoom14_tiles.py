#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复14级瓦片空白问题
直接下载OpenStreetMap的14级瓦片
"""

import os
import requests
import math
from pathlib import Path
import time

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def download_tile(x, y, z, output_dir):
    """下载单个瓦片"""
    # 创建目录
    tile_dir = Path(output_dir) / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    
    tile_path = tile_dir / f"{y}.png"
    
    # 如果文件已存在且大小合理，跳过
    if tile_path.exists() and tile_path.stat().st_size > 2000:
        return True, f"跳过 {z}/{x}/{y}"
    
    # OpenStreetMap瓦片URL
    url = f"https://tile.openstreetmap.org/{z}/{x}/{y}.png"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        if len(response.content) < 1000:
            return False, f"瓦片 {z}/{x}/{y} 内容过小"
        
        with open(tile_path, 'wb') as f:
            f.write(response.content)
        
        return True, f"下载 {z}/{x}/{y} ({len(response.content)} bytes)"
        
    except Exception as e:
        return False, f"下载 {z}/{x}/{y} 失败: {e}"

def main():
    """主函数 - 下载北京地区14级瓦片"""
    print("🚀 修复14级瓦片空白问题")
    print("=" * 40)
    
    # 北京天安门坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 14级瓦片
    z = 14
    
    # 计算北京天安门对应的瓦片坐标
    center_x, center_y = deg2num(beijing_lat, beijing_lon, z)
    
    print(f"北京天安门坐标: ({beijing_lat}, {beijing_lon})")
    print(f"对应的14级瓦片: {z}/{center_x}/{center_y}")
    
    # 下载中心瓦片周围的瓦片 (5x5网格)
    radius = 2  # 下载中心瓦片周围2格的范围
    output_dir = "static/tiles"
    
    success_count = 0
    total_tiles = 0
    
    print(f"\n开始下载 {z} 级瓦片...")
    
    for dx in range(-radius, radius + 1):
        for dy in range(-radius, radius + 1):
            x = center_x + dx
            y = center_y + dy
            
            # 确保瓦片坐标有效
            if x < 0 or y < 0 or x >= 2**z or y >= 2**z:
                continue
            
            total_tiles += 1
            success, message = download_tile(x, y, z, output_dir)
            
            if success:
                success_count += 1
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
            
            # 避免请求过快
            time.sleep(0.1)
    
    print(f"\n🎉 完成!")
    print(f"成功下载: {success_count}/{total_tiles} 个瓦片")
    print(f"瓦片保存在: {output_dir}/{z}/")
    
    # 测试下载的瓦片
    print(f"\n🔍 测试下载的瓦片...")
    test_tile = Path(output_dir) / str(z) / str(center_x) / f"{center_y}.png"
    if test_tile.exists():
        size = test_tile.stat().st_size
        print(f"✅ 测试瓦片存在: {test_tile} ({size} bytes)")
        if size > 2000:
            print("✅ 瓦片大小正常，应该包含有效内容")
        else:
            print("⚠️  瓦片大小偏小，可能内容不完整")
    else:
        print(f"❌ 测试瓦片不存在: {test_tile}")

if __name__ == "__main__":
    main()
