#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证瓦片完整性
检查各个缩放级别的瓦片是否完整
"""

import os
import math
from pathlib import Path
import json

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def lat_lon_to_tile_bbox(lat, lon, radius_km, zoom):
    """计算给定半径内的瓦片边界框"""
    # 简化的近似计算
    lat_delta = radius_km / 111.0  # 1度纬度约111公里
    lon_delta = radius_km / (111.0 * math.cos(math.radians(lat)))  # 经度距离随纬度变化
    
    # 计算边界
    south = lat - lat_delta
    north = lat + lat_delta
    west = lon - lon_delta
    east = lon + lon_delta
    
    # 转换为瓦片坐标
    sw_x, sw_y = deg2num(south, west, zoom)
    ne_x, ne_y = deg2num(north, east, zoom)
    
    return {
        'min_x': min(sw_x, ne_x),
        'max_x': max(sw_x, ne_x),
        'min_y': min(sw_y, ne_y),
        'max_y': max(sw_y, ne_y)
    }

def check_tile_integrity(tiles_dir, center_lat=39.9042, center_lon=116.4074, radius_km=50):
    """检查瓦片完整性"""
    print("🔍 开始检查瓦片完整性...")
    print(f"📍 中心点: {center_lat}, {center_lon}")
    print(f"📏 检查半径: {radius_km} 公里")
    
    if not tiles_dir.exists():
        print(f"❌ 瓦片目录不存在: {tiles_dir}")
        return False
    
    # 统计信息
    total_tiles = 0
    missing_tiles = 0
    zoom_stats = {}
    
    # 检查各缩放级别
    for zoom_dir in tiles_dir.iterdir():
        if zoom_dir.is_dir() and zoom_dir.name.isdigit():
            zoom = int(zoom_dir.name)
            
            # 计算该缩放级别下应该存在的瓦片范围
            bbox = lat_lon_to_tile_bbox(center_lat, center_lon, radius_km, zoom)
            
            zoom_tile_count = 0
            zoom_missing_count = 0
            
            # 检查每个瓦片
            for x in range(bbox['min_x'], bbox['max_x'] + 1):
                for y in range(bbox['min_y'], bbox['max_y'] + 1):
                    tile_path = zoom_dir / str(x) / f"{y}.png"
                    if tile_path.exists():
                        zoom_tile_count += 1
                        total_tiles += 1
                    else:
                        zoom_missing_count += 1
                        missing_tiles += 1
            
            zoom_stats[zoom] = {
                'expected': (bbox['max_x'] - bbox['min_x'] + 1) * (bbox['max_y'] - bbox['min_y'] + 1),
                'actual': zoom_tile_count,
                'missing': zoom_missing_count
            }
    
    # 显示结果
    print(f"\n📊 检查结果:")
    print(f"   总瓦片数: {total_tiles}")
    print(f"   缺失瓦片数: {missing_tiles}")
    
    if total_tiles > 0:
        completeness = (total_tiles / (total_tiles + missing_tiles)) * 100
        print(f"   完整性: {completeness:.1f}%")
    
    print(f"\n📈 各缩放级别统计:")
    for zoom in sorted(zoom_stats.keys()):
        stats = zoom_stats[zoom]
        if stats['expected'] > 0:
            coverage = (stats['actual'] / stats['expected']) * 100
            print(f"   缩放 {zoom}: {stats['actual']}/{stats['expected']} ({coverage:.1f}%)")
        else:
            print(f"   缩放 {zoom}: {stats['actual']} 个瓦片")
    
    # 识别问题缩放级别
    problematic_zooms = []
    for zoom, stats in zoom_stats.items():
        if stats['expected'] > 0:
            coverage = (stats['actual'] / stats['expected']) * 100
            if coverage < 80:  # 完整性低于80%认为有问题
                problematic_zooms.append(zoom)
    
    if problematic_zooms:
        print(f"\n⚠️  以下缩放级别完整性较低: {problematic_zooms}")
        print("   建议运行相应的修复脚本:")
        for zoom in problematic_zooms:
            if zoom == 11:
                print("   - python fix_zoom11_tiles.py")
            else:
                print(f"   - python generate_missing_tiles.py (缩放级别 {zoom})")
    
    return missing_tiles == 0

def list_all_tiles(tiles_dir):
    """列出所有瓦片"""
    print("📋 所有瓦片列表:")
    
    if not tiles_dir.exists():
        print(f"❌ 瓦片目录不存在: {tiles_dir}")
        return
    
    total_count = 0
    zoom_counts = {}
    
    for zoom_dir in tiles_dir.iterdir():
        if zoom_dir.is_dir() and zoom_dir.name.isdigit():
            zoom = int(zoom_dir.name)
            tile_count = 0
            
            for x_dir in zoom_dir.iterdir():
                if x_dir.is_dir() and x_dir.name.isdigit():
                    tiles = list(x_dir.glob("*.png"))
                    tile_count += len(tiles)
            
            zoom_counts[zoom] = tile_count
            total_count += tile_count
    
    print(f"   总计: {total_count} 个瓦片")
    print("   各缩放级别:")
    for zoom in sorted(zoom_counts.keys()):
        print(f"     缩放 {zoom}: {zoom_counts[zoom]} 个瓦片")

def main():
    """主函数"""
    print("🗺️  离线地图瓦片完整性检查工具")
    print("=" * 50)
    
    # 瓦片目录
    tiles_dir = Path(__file__).parent / "static" / "tiles"
    
    # 检查瓦片完整性
    is_complete = check_tile_integrity(tiles_dir)
    
    print(f"\n" + "=" * 50)
    if is_complete:
        print("✅ 所有瓦片完整!")
    else:
        print("❌ 发现缺失的瓦片")
    
    # 列出所有瓦片
    print(f"\n" + "-" * 30)
    list_all_tiles(tiles_dir)
    
    return is_complete

if __name__ == "__main__":
    main()