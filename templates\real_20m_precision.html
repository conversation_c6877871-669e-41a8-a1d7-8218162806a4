<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真正的20米精度离线地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            padding: 20px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .control-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .control-panel h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .status.error {
            background: #ffeaea;
            border-color: #f44336;
        }
        
        .precision-info {
            background: #f0f8ff;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .precision-info h4 {
            margin: 0 0 5px 0;
            color: #1976d2;
        }
        
        .precision-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .precision-info li {
            font-size: 12px;
            margin-bottom: 3px;
        }
        
        .coordinates {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            margin-top: 5px;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 1000;
            display: none;
        }
        
        .layer-control {
            background: white;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .layer-control label {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .layer-control input[type="checkbox"] {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 真正的20米精度离线地图系统</h1>
        <div class="subtitle">基于本地OSM数据的高精度野外监控地图</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="precision-info">
                <h4>🎯 精度规格</h4>
                <ul>
                    <li>坐标精度: 8位小数 (约1.1米)</li>
                    <li>定位精度: ±20米</li>
                    <li>瓦片分辨率: 512x512 (高精度)</li>
                    <li>数据来源: 本地OSM文件</li>
                    <li>缓存机制: SQLite数据库</li>
                </ul>
            </div>
            
            <div class="control-panel">
                <h3>📍 精确定位</h3>
                <div class="input-group">
                    <label>纬度 (8位小数)</label>
                    <input type="number" id="lat-input" step="0.00000001" placeholder="39.90420000">
                </div>
                <div class="input-group">
                    <label>经度 (8位小数)</label>
                    <input type="number" id="lon-input" step="0.00000001" placeholder="116.40740000">
                </div>
                <button class="btn" onclick="gotoLocation()">定位到坐标</button>
                <button class="btn" onclick="getCurrentLocation()">获取当前位置</button>
            </div>
            
            <div class="control-panel">
                <h3>🗺️ 数据生成</h3>
                <div class="input-group">
                    <label>生成半径 (米)</label>
                    <input type="number" id="radius-input" value="1000" min="100" max="5000">
                </div>
                <button class="btn" id="generate-btn" onclick="generateAreaData()">生成区域数据</button>
                <div id="generation-status"></div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 图层控制</h3>
                <div class="layer-control">
                    <label>
                        <input type="checkbox" id="roads-layer" checked>
                        道路网络
                    </label>
                    <label>
                        <input type="checkbox" id="buildings-layer" checked>
                        建筑物
                    </label>
                    <label>
                        <input type="checkbox" id="water-layer" checked>
                        水体
                    </label>
                    <label>
                        <input type="checkbox" id="pois-layer" checked>
                        兴趣点
                    </label>
                </div>
            </div>
            
            <div class="control-panel">
                <h3>📊 当前状态</h3>
                <div id="current-coordinates" class="coordinates">
                    点击地图获取坐标
                </div>
                <div id="zoom-level" class="coordinates">
                    缩放级别: -
                </div>
                <div id="tile-info" class="coordinates">
                    瓦片: -
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="loading" id="loading">
                <div>正在生成高精度数据...</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京天安门
            zoom: 16,
            maxZoom: 20,
            minZoom: 10
        });

        // 添加高精度瓦片图层
        const precisionTileLayer = L.tileLayer('/tiles/{z}/{x}/{y}.png', {
            attribution: '© 真正20米精度系统 | 基于本地OSM数据',
            maxZoom: 20,
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // 当前位置标记
        let currentMarker = null;

        // 地图事件
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(8);
            const lng = e.latlng.lng.toFixed(8);
            
            document.getElementById('current-coordinates').innerHTML = 
                `纬度: ${lat}<br>经度: ${lng}`;
            
            // 更新输入框
            document.getElementById('lat-input').value = lat;
            document.getElementById('lon-input').value = lng;
            
            // 添加标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`精确坐标:<br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
        });

        map.on('zoomend', function() {
            const zoom = map.getZoom();
            document.getElementById('zoom-level').innerHTML = `缩放级别: ${zoom}`;
            
            // 更新瓦片信息
            const center = map.getCenter();
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            document.getElementById('tile-info').innerHTML = 
                `瓦片: ${zoom}/${tileCoord.x}/${tileCoord.y}`;
        });

        // 工具函数
        function getTileCoordinates(lat, lng, zoom) {
            const latRad = lat * Math.PI / 180;
            const n = Math.pow(2, zoom);
            const x = Math.floor((lng + 180) / 360 * n);
            const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
            return { x, y };
        }

        function gotoLocation() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请输入有效的坐标');
                return;
            }
            
            map.setView([lat, lng], 18);
            
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`目标位置:<br>纬度: ${lat.toFixed(8)}<br>经度: ${lng.toFixed(8)}`)
                .openPopup();
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude.toFixed(8);
                    const lng = position.coords.longitude.toFixed(8);
                    
                    document.getElementById('lat-input').value = lat;
                    document.getElementById('lon-input').value = lng;
                    
                    map.setView([lat, lng], 18);
                    
                    if (currentMarker) {
                        map.removeLayer(currentMarker);
                    }
                    currentMarker = L.marker([lat, lng]).addTo(map)
                        .bindPopup(`当前位置:<br>纬度: ${lat}<br>经度: ${lng}<br>精度: ±${position.coords.accuracy}米`)
                        .openPopup();
                }, function(error) {
                    alert('无法获取当前位置: ' + error.message);
                });
            } else {
                alert('浏览器不支持地理定位');
            }
        }

        function generateAreaData() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            const radius = parseInt(document.getElementById('radius-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请先设置坐标');
                return;
            }
            
            const btn = document.getElementById('generate-btn');
            const status = document.getElementById('generation-status');
            const loading = document.getElementById('loading');
            
            btn.disabled = true;
            btn.textContent = '生成中...';
            loading.style.display = 'block';
            
            fetch(`/api/generate-area?lat=${lat}&lon=${lng}&radius=${radius}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        status.innerHTML = `<div class="status">${data.message}</div>`;
                        
                        // 5秒后刷新瓦片
                        setTimeout(() => {
                            precisionTileLayer.redraw();
                            btn.disabled = false;
                            btn.textContent = '生成区域数据';
                            loading.style.display = 'none';
                            status.innerHTML = '<div class="status">数据生成完成，地图已更新</div>';
                        }, 5000);
                    } else {
                        throw new Error(data.error || '生成失败');
                    }
                })
                .catch(error => {
                    status.innerHTML = `<div class="status error">错误: ${error.message}</div>`;
                    btn.disabled = false;
                    btn.textContent = '生成区域数据';
                    loading.style.display = 'none';
                });
        }

        // 初始化显示
        document.getElementById('zoom-level').innerHTML = `缩放级别: ${map.getZoom()}`;
        
        // 图层控制
        document.getElementById('roads-layer').addEventListener('change', function() {
            // 这里可以添加图层控制逻辑
        });
        
        document.getElementById('buildings-layer').addEventListener('change', function() {
            // 这里可以添加图层控制逻辑
        });
        
        document.getElementById('water-layer').addEventListener('change', function() {
            // 这里可以添加图层控制逻辑
        });
        
        document.getElementById('pois-layer').addEventListener('change', function() {
            // 这里可以添加图层控制逻辑
        });
    </script>
</body>
</html>
