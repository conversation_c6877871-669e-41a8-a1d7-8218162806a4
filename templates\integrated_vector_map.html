<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整合矢量地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 100px);
        }
        
        .sidebar {
            width: 350px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .search-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .search-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .search-button {
            width: 100%;
            margin-top: 10px;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .search-button:hover {
            transform: translateY(-2px);
        }
        
        .famous-places-button {
            width: 100%;
            margin-top: 10px;
            padding: 12px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .famous-places-button:hover {
            transform: translateY(-2px);
        }
        
        .results-section {
            padding: 20px;
        }
        
        .results-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .result-item {
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 4px solid #667eea;
        }
        
        .result-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .result-item.famous {
            border-left-color: #f5576c;
        }
        
        .result-item.poi {
            border-left-color: #28a745;
        }
        
        .result-item.region {
            border-left-color: #007bff;
        }
        
        .result-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .result-info {
            font-size: 12px;
            color: #666;
        }
        
        .statistics-section {
            padding: 20px;
            border-top: 1px solid #eee;
        }
        
        .statistics-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-label {
            color: #666;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            color: #155724;
            padding: 10px;
            background: #d4edda;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .region-selector {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .region-selector h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .region-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        
        .region-item {
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .region-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        
        .region-item.complete {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .region-item.incomplete {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .region-item.missing {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .map-quality-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .map-quality-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .quality-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .quality-info p {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .quality-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .quality-info li {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        .quality-info em {
            color: #6c757d;
            font-size: 12px;
        }
        
        /* 自定义标记样式 */
        .custom-marker {
            background: transparent;
            border: none;
        }
        
        .marker-pulse {
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border: 3px solid #fff;
            border-radius: 50%;
            box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
            }
        }
        
        /* 弹出窗口样式 */
        .location-popup {
            text-align: center;
            min-width: 200px;
        }
        
        .location-popup h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
        }
        
        .location-popup p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
        
        /* 搜索结果项悬停效果 */
        .result-item {
            transition: all 0.3s ease;
        }
        
        .result-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ 整合矢量地图系统</h1>
        <p>高精度全国矢量地图数据查看器</p>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="search-section">
                <h3>🔍 搜索地点</h3>
                <input type="text" id="searchInput" class="search-input" placeholder="输入地点名称...">
                <button id="searchButton" class="search-button">搜索</button>
                <button id="famousPlacesButton" class="famous-places-button">著名地标</button>
            </div>
            
            <div class="map-quality-section">
                <h3>🗺️ 地图质量</h3>
                <div class="quality-info">
                    <p><strong>高精度模式已启用</strong></p>
                    <ul>
                        <li>✅ 支持18级缩放精度</li>
                        <li>✅ 多种地图图层选择</li>
                        <li>✅ 智能矢量数据渲染</li>
                        <li>✅ 分类显示道路、建筑、POI</li>
                    </ul>
                    <p><em>提示：使用右上角图层控制切换地图类型</em></p>
                </div>
            </div>
            
            <div class="region-selector">
                <h3>🗺️ 区域选择</h3>
                <div id="regionGrid" class="region-grid">
                    <!-- 区域按钮将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <div class="results-section">
                <h3>📋 搜索结果</h3>
                <div id="searchResults">
                    <div class="loading">请输入关键词进行搜索</div>
                </div>
            </div>
            
            <div class="statistics-section">
                <h3>📊 系统统计</h3>
                <div id="statistics">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            maxZoom: 18,
            minZoom: 3
        }).setView([35.0, 105.0], 5);
        
        // 高精度地图图层选项
        const baseLayers = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }),
            "高德地图": L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                attribution: '© 高德地图',
                subdomains: ['1', '2', '3', '4'],
                maxZoom: 18
            }),
            "百度地图": L.tileLayer('https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20240101', {
                attribution: '© 百度地图',
                subdomains: ['0', '1', '2', '3'],
                maxZoom: 18
            }),
            "卫星图": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri',
                maxZoom: 18
            })
        };
        
        // 添加默认底图
        baseLayers["高德地图"].addTo(map);
        
        // 添加图层控制
        L.control.layers(baseLayers).addTo(map);
        
        // 全局变量
        let regions = {};
        let famousPlaces = {};
        let currentMarkers = [];
        let currentLayers = {};
        
        // 加载系统数据
        async function loadSystemData() {
            try {
                // 加载区域数据
                const regionsResponse = await fetch('/api/regions');
                regions = await regionsResponse.json();
                
                // 加载著名地标
                const famousResponse = await fetch('/api/famous-places');
                famousPlaces = await famousResponse.json();
                
                // 加载统计信息
                const statsResponse = await fetch('/api/statistics');
                const stats = await statsResponse.json();
                
                // 更新界面
                updateRegionGrid();
                updateStatistics(stats);
                
            } catch (error) {
                console.error('加载系统数据失败:', error);
                showError('加载系统数据失败');
            }
        }
        
        // 更新区域网格
        function updateRegionGrid() {
            const grid = document.getElementById('regionGrid');
            grid.innerHTML = '';
            
            for (const [regionName, region] of Object.entries(regions)) {
                const item = document.createElement('div');
                item.className = `region-item ${region.status}`;
                item.textContent = regionName;
                item.title = `${regionName} - ${region.status}`;
                item.onclick = () => showRegion(regionName);
                grid.appendChild(item);
            }
        }
        
        // 更新统计信息
        function updateStatistics(stats) {
            const statsDiv = document.getElementById('statistics');
            statsDiv.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">总区域数</span>
                    <span class="stat-value">${stats.total_regions}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">完整区域</span>
                    <span class="stat-value">${stats.complete_regions}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">不完整区域</span>
                    <span class="stat-value">${stats.incomplete_regions}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">缺失区域</span>
                    <span class="stat-value">${stats.missing_regions}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">完成率</span>
                    <span class="stat-value">${stats.completion_rate}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">著名地标</span>
                    <span class="stat-value">${stats.famous_places_count}</span>
                </div>
            `;
        }
        
        // 显示区域
        async function showRegion(regionName) {
            if (!regions[regionName]) {
                showError('区域不存在');
                return;
            }
            
            const region = regions[regionName];
            
            // 清除当前图层
            clearCurrentLayers();
            
            // 使用flyTo实现平滑动画聚焦到区域
            map.flyTo(region.center, 9, {
                animate: true,
                duration: 2.0, // 2秒动画
                easeLinearity: 0.1
            });
            
            // 添加区域边界
            const bbox = region.bbox;
            const bounds = [[bbox[1], bbox[0]], [bbox[3], bbox[2]]];
            const rectangle = L.rectangle(bounds, {
                color: '#667eea',
                weight: 2,
                fillColor: '#667eea',
                fillOpacity: 0.1
            }).addTo(map);
            currentLayers.region = rectangle;
            
            // 加载区域数据
            if (region.status === 'complete') {
                await loadRegionData(regionName);
            } else {
                showError(`区域 ${regionName} 数据不完整`);
            }
        }
        
        // 加载区域数据
        async function loadRegionData(regionName) {
            try {
                // 加载道路数据
                const roadsResponse = await fetch(`/api/region/${regionName}/roads`);
                const roadsData = await roadsResponse.json();
                if (roadsData.nodes && roadsData.ways) {
                    displayRoads(roadsData);
                }
                
                // 加载建筑物数据
                const buildingsResponse = await fetch(`/api/region/${regionName}/buildings`);
                const buildingsData = await buildingsResponse.json();
                if (buildingsData.nodes && buildingsData.ways) {
                    displayBuildings(buildingsData);
                }
                
                // 加载POI数据
                const poisResponse = await fetch(`/api/region/${regionName}/pois`);
                const poisData = await poisResponse.json();
                if (poisData.nodes) {
                    displayPOIs(poisData);
                }
                
            } catch (error) {
                console.error('加载区域数据失败:', error);
                showError('加载区域数据失败');
            }
        }
        
        // 显示道路
        function displayRoads(data) {
            const roadsLayer = L.layerGroup();
            
            // 创建节点映射
            const nodeMap = {};
            data.nodes.forEach(node => {
                nodeMap[node.id] = [node.lat, node.lon];
            });
            
            // 绘制道路
            data.ways.forEach(way => {
                if (way.nodes && way.nodes.length >= 2) {
                    const coordinates = way.nodes
                        .map(nodeId => nodeMap[nodeId])
                        .filter(coord => coord);
                    
                    if (coordinates.length >= 2) {
                        // 根据道路类型设置不同样式
                        let style = {
                            color: '#2c3e50',
                            weight: 2,
                            opacity: 0.8,
                            lineCap: 'round',
                            lineJoin: 'round'
                        };
                        
                        // 根据道路类型调整样式
                        if (way.tags && way.tags.highway) {
                            const highwayType = way.tags.highway;
                            if (highwayType === 'motorway' || highwayType === 'trunk') {
                                style.color = '#e74c3c';
                                style.weight = 4;
                            } else if (highwayType === 'primary' || highwayType === 'secondary') {
                                style.color = '#f39c12';
                                style.weight = 3;
                            } else if (highwayType === 'residential' || highwayType === 'service') {
                                style.color = '#95a5a6';
                                style.weight = 1.5;
                            }
                        }
                        
                        const polyline = L.polyline(coordinates, style);
                        roadsLayer.addLayer(polyline);
                    }
                }
            });
            
            roadsLayer.addTo(map);
            currentLayers.roads = roadsLayer;
        }
        
        // 显示建筑物
        function displayBuildings(data) {
            const buildingsLayer = L.layerGroup();
            
            // 创建节点映射
            const nodeMap = {};
            data.nodes.forEach(node => {
                nodeMap[node.id] = [node.lat, node.lon];
            });
            
            // 绘制建筑物
            data.ways.forEach(way => {
                if (way.nodes && way.nodes.length >= 3) {
                    const coordinates = way.nodes
                        .map(nodeId => nodeMap[nodeId])
                        .filter(coord => coord);
                    
                    if (coordinates.length >= 3) {
                        // 根据建筑物类型设置不同样式
                        let style = {
                            color: '#e74c3c',
                            weight: 2,
                            opacity: 0.8,
                            fillColor: '#f39c12',
                            fillOpacity: 0.4
                        };
                        
                        // 根据建筑物类型调整样式
                        if (way.tags && way.tags.building) {
                            const buildingType = way.tags.building;
                            if (buildingType === 'commercial' || buildingType === 'retail') {
                                style.color = '#9b59b6';
                                style.fillColor = '#e67e22';
                            } else if (buildingType === 'residential') {
                                style.color = '#27ae60';
                                style.fillColor = '#2ecc71';
                            } else if (buildingType === 'industrial') {
                                style.color = '#34495e';
                                style.fillColor = '#7f8c8d';
                            }
                        }
                        
                        const polygon = L.polygon(coordinates, style);
                        buildingsLayer.addLayer(polygon);
                    }
                }
            });
            
            buildingsLayer.addTo(map);
            currentLayers.buildings = buildingsLayer;
        }
        
        // 显示POI
        function displayPOIs(data) {
            const poisLayer = L.layerGroup();
            
            data.nodes.forEach(node => {
                if (node.lat && node.lon) {
                    // 根据POI类型设置不同样式
                    let style = {
                        radius: 6,
                        fillColor: '#27ae60',
                        color: '#2ecc71',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    };
                    
                    // 根据POI类型调整样式
                    if (node.tags && node.tags.amenity) {
                        const amenityType = node.tags.amenity;
                        if (amenityType === 'restaurant' || amenityType === 'cafe') {
                            style.fillColor = '#e74c3c';
                            style.color = '#c0392b';
                        } else if (amenityType === 'hospital' || amenityType === 'pharmacy') {
                            style.fillColor = '#3498db';
                            style.color = '#2980b9';
                        } else if (amenityType === 'bank' || amenityType === 'atm') {
                            style.fillColor = '#f39c12';
                            style.color = '#e67e22';
                        } else if (amenityType === 'hotel' || amenityType === 'lodging') {
                            style.fillColor = '#9b59b6';
                            style.color = '#8e44ad';
                        }
                    }
                    
                    const marker = L.circleMarker([node.lat, node.lon], style);
                    
                    const popupContent = `
                        <div>
                            <strong>${node.tags.name || '未知地点'}</strong><br>
                            ${node.tags.amenity ? `类型: ${node.tags.amenity}` : ''}
                        </div>
                    `;
                    marker.bindPopup(popupContent);
                    poisLayer.addLayer(marker);
                }
            });
            
            poisLayer.addTo(map);
            currentLayers.pois = poisLayer;
        }
        
        // 清除当前图层
        function clearCurrentLayers() {
            Object.values(currentLayers).forEach(layer => {
                if (layer) {
                    map.removeLayer(layer);
                }
            });
            currentLayers = {};
        }
        
        // 搜索功能
        async function searchLocation() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showError('请输入搜索关键词');
                return;
            }
            
            // 显示加载状态
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
            
            try {
                // 首先尝试快速搜索
                const quickResponse = await fetch(`/api/quick-search?q=${encodeURIComponent(query)}`);
                const quickResults = await quickResponse.json();
                
                if (quickResults.length > 0) {
                    displaySearchResults(quickResults);
                    return;
                }
                
                // 如果快速搜索没有结果，进行完整搜索
                const response = await fetch(`/api/search-poi?q=${encodeURIComponent(query)}`);
                const results = await response.json();
                displaySearchResults(results);
            } catch (error) {
                console.error('搜索失败:', error);
                showError('搜索失败');
            }
        }
        
        // 显示搜索结果
        function displaySearchResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">未找到相关结果</div>';
                return;
            }
            
            resultsDiv.innerHTML = results.map(result => {
                let typeText = '';
                if (result.type === 'famous_place') {
                    typeText = '著名地标';
                } else if (result.type === 'region') {
                    typeText = '区域';
                } else if (result.type === 'poi') {
                    typeText = '兴趣点';
                } else if (result.type === 'city') {
                    typeText = '地级市';
                } else if (result.type === 'county') {
                    typeText = '县级市/区/县';
                } else if (result.type === 'town') {
                    typeText = '乡镇';
                }
                
                return `
                    <div class="result-item ${result.type}" onclick="locateResult('${result.name}', ${result.lat}, ${result.lon})">
                        <div class="result-name">${result.name}</div>
                        <div class="result-info">
                            ${result.region} - ${typeText}
                            ${result.level ? ` (${result.level})` : ''}
                            ${result.capital_name ? ` (省会: ${result.capital_name})` : ''}
                            ${result.county ? ` (所属: ${result.county})` : ''}
                            ${result.amenity ? ` (${result.amenity})` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 定位到搜索结果
        function locateResult(name, lat, lon) {
            // 使用flyTo实现平滑动画聚焦
            map.flyTo([lat, lon], 16, {
                animate: true,
                duration: 1.5, // 1.5秒动画
                easeLinearity: 0.1
            });
            
            // 清除之前的标记
            currentMarkers.forEach(m => map.removeLayer(m));
            currentMarkers = [];
            
            // 添加高亮标记
            const marker = L.marker([lat, lon], {
                icon: L.divIcon({
                    className: 'custom-marker',
                    html: '<div class="marker-pulse"></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                })
            }).addTo(map);
            
            // 添加圆形高亮区域
            const circle = L.circle([lat, lon], {
                color: '#ff6b6b',
                fillColor: '#ff6b6b',
                fillOpacity: 0.2,
                radius: 200
            }).addTo(map);
            
            // 绑定弹出窗口
            marker.bindPopup(`
                <div class="location-popup">
                    <h4>📍 ${name}</h4>
                    <p>坐标: ${lat.toFixed(6)}, ${lon.toFixed(6)}</p>
                </div>
            `).openPopup();
            
            // 保存标记
            currentMarkers = [marker, circle];
            
            // 显示成功消息
            showSuccess(`已定位到: ${name}`);
        }
        
        // 显示著名地标
        async function showFamousPlaces() {
            const results = Object.entries(famousPlaces).map(([name, info]) => ({
                name: name,
                lat: info.lat,
                lon: info.lon,
                region: info.region,
                type: 'famous_place'
            }));
            
            displaySearchResults(results);
        }
        
        // 显示错误信息
        function showError(message) {
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = `<div class="error">${message}</div>`;
        }
        
        // 显示成功信息
        function showSuccess(message) {
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = `<div class="success">${message}</div>`;
        }
        
        // 实时搜索功能
        let searchTimeout;
        function performRealTimeSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (query.length < 2) {
                document.getElementById('searchResults').innerHTML = '<div class="loading">请输入至少2个字符</div>';
                return;
            }
            
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/api/quick-search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();
                    if (results.length > 0) {
                        displaySearchResults(results);
                    } else {
                        document.getElementById('searchResults').innerHTML = '<div class="loading">未找到相关结果</div>';
                    }
                } catch (error) {
                    console.error('实时搜索失败:', error);
                }
            }, 300); // 300ms延迟
        }
        
        // 事件监听
        document.getElementById('searchButton').addEventListener('click', searchLocation);
        document.getElementById('famousPlacesButton').addEventListener('click', showFamousPlaces);
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLocation();
            }
        });
        document.getElementById('searchInput').addEventListener('input', performRealTimeSearch);
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemData();
        });
    </script>
</body>
</html>
