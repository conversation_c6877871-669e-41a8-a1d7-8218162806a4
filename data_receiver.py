import serial
import threading
import time
import pynmea2
from pymodbus.client.serial import ModbusSerialClient
class Endian:
    BIG = '>'
    LITTLE = '<'
from pymodbus.payload import BinaryPayloadDecoder
from config import Config

class DataReceiver:
    def __init__(self):
        self.gps_serial = None
        self.modbus_client = None
        self.running = False
        self.latest_gps = None
        self.latest_plc = None
        
    def start(self):
        """启动数据接收"""
        self.running = True
        
        # 初始化GPS串口
        try:
            self.gps_serial = serial.Serial(
                Config.GPS_PORT, 
                Config.GPS_BAUDRATE, 
                timeout=1
            )
            print(f"GPS串口已连接: {Config.GPS_PORT}")
        except Exception as e:
            print(f"GPS串口连接失败: {e}")
        
        # 初始化MODBUS客户端
        try:
            self.modbus_client = ModbusSerialClient(
                method='rtu',
                port=Config.MODBUS_PORT,
                baudrate=Config.MODBUS_BAUDRATE,
                timeout=3,
                parity='N',
                stopbits=1,
                bytesize=8
            )
            if self.modbus_client.connect():
                print(f"MODBUS客户端已连接: {Config.MODBUS_PORT}")
            else:
                print("MODBUS客户端连接失败")
        except Exception as e:
            print(f"MODBUS客户端初始化失败: {e}")
        
        # 启动数据接收线程
        if self.gps_serial:
            gps_thread = threading.Thread(target=self._gps_reader_thread, daemon=True)
            gps_thread.start()
        
        if self.modbus_client and self.modbus_client.is_socket_open():
            plc_thread = threading.Thread(target=self._plc_reader_thread, daemon=True)
            plc_thread.start()
    
    def _gps_reader_thread(self):
        """GPS数据读取线程"""
        while self.running and self.gps_serial:
            try:
                line = self.gps_serial.readline().decode('ascii', errors='ignore').strip()
                if line.startswith('$GPGGA') or line.startswith('$GPRMC'):
                    msg = pynmea2.parse(line)
                    if hasattr(msg, 'latitude') and hasattr(msg, 'longitude'):
                        if msg.latitude and msg.longitude:
                            self.latest_gps = {
                                'lat': float(msg.latitude),
                                'lng': float(msg.longitude),
                                'altitude': getattr(msg, 'altitude', 0) or 0,
                                'speed': getattr(msg, 'spd_over_grnd', 0) or 0
                            }
            except Exception as e:
                print(f"GPS数据解析错误: {e}")
                time.sleep(1)
    
    def _plc_reader_thread(self):
        """PLC数据读取线程"""
        while self.running and self.modbus_client:
            try:
                # 读取保持寄存器 (气体浓度数据)
                gas_result = self.modbus_client.read_holding_registers(
                    Config.MODBUS_GAS_START_ADDR, 
                    Config.MODBUS_GAS_COUNT, 
                    unit=Config.MODBUS_SLAVE_ID
                )
                
                # 读取线圈状态 (风机状态)
                fan_result = self.modbus_client.read_coils(
                    Config.MODBUS_FAN_START_ADDR, 
                    Config.MODBUS_FAN_COUNT, 
                    unit=Config.MODBUS_SLAVE_ID
                )
                
                # 读取输入寄存器 (温湿度数据)
                env_result = self.modbus_client.read_input_registers(
                    Config.MODBUS_ENV_START_ADDR, 
                    Config.MODBUS_ENV_COUNT, 
                    unit=Config.MODBUS_SLAVE_ID
                )
                
                if not gas_result.isError() and not fan_result.isError() and not env_result.isError():
                    # 解析气体浓度数据 (假设为浮点数)
                    decoder = BinaryPayloadDecoder.fromRegisters(
                        gas_result.registers, 
                        byteorder=Endian.Big
                    )
                    
                    self.latest_plc = {
                        'gas_levels': {
                            'CO': decoder.decode_16bit_uint() / 100.0,  # ppm
                            'H2S': decoder.decode_16bit_uint() / 100.0,  # ppm
                            'CH4': decoder.decode_16bit_uint() / 100.0,  # %LEL
                            'O2': decoder.decode_16bit_uint() / 100.0   # %
                        },
                        'fan_status': {
                            'fan1': fan_result.bits[0],
                            'fan2': fan_result.bits[1] if len(fan_result.bits) > 1 else False
                        },
                        'temperature': env_result.registers[0] / 10.0,  # 摄氏度
                        'humidity': env_result.registers[1] / 10.0      # %RH
                    }
                else:
                    print("MODBUS读取错误")
                    
            except Exception as e:
                print(f"PLC数据读取错误: {e}")
            
            time.sleep(Config.PLC_READ_INTERVAL)
    
    def get_gps_data(self):
        """获取最新GPS数据"""
        return self.latest_gps
    
    def get_plc_data(self):
        """获取最新PLC数据"""
        return self.latest_plc
    
    def stop(self):
        """停止数据接收"""
        self.running = False
        if self.gps_serial:
            self.gps_serial.close()
        if self.modbus_client:
            self.modbus_client.close()