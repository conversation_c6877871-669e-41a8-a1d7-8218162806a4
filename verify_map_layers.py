#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证地图图层选择功能
"""

import requests
import webbrowser
import time

def verify_map_layers():
    """验证地图图层功能"""
    print("🗺️ 验证地图图层选择功能")
    print("=" * 50)
    
    # 检查主系统
    print("1. 检查主系统状态...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/statistics", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 主系统运行正常")
            print(f"   📊 区域数: {stats['total_regions']}, 完成率: {stats['completion_rate']}%")
        else:
            print(f"   ❌ 主系统响应异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 主系统连接失败: {e}")
    
    # 检查测试服务器
    print("\n2. 检查测试服务器状态...")
    try:
        response = requests.get("http://localhost:8080/test_map_layers.html", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ 测试服务器运行正常")
            print(f"   🌐 测试页面可访问")
        else:
            print(f"   ❌ 测试服务器响应异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 测试服务器连接失败: {e}")
    
    # 提供访问链接
    print(f"\n3. 访问链接:")
    print(f"   🏠 主系统: http://127.0.0.1:5000")
    print(f"   🧪 测试页面: http://localhost:8080/test_map_layers.html")
    
    # 自动打开测试页面
    print(f"\n4. 自动打开测试页面...")
    try:
        webbrowser.open('http://localhost:8080/test_map_layers.html')
        print(f"   ✅ 已自动打开测试页面")
    except Exception as e:
        print(f"   ❌ 无法自动打开浏览器: {e}")
        print(f"   💡 请手动访问: http://localhost:8080/test_map_layers.html")
    
    # 使用说明
    print(f"\n📋 使用说明:")
    print(f"   1. 在测试页面中查看地图右上角")
    print(f"   2. 应该能看到图层控制按钮")
    print(f"   3. 点击按钮选择不同的地图图层:")
    print(f"      - 高德地图 (推荐中国地区)")
    print(f"      - 百度地图 (中文标注)")
    print(f"      - OpenStreetMap (全球覆盖)")
    print(f"      - 卫星图 (真实地形)")
    print(f"   4. 尝试不同的缩放级别 (最大18级)")
    
    print(f"\n🔍 如果看不到图层控制按钮:")
    print(f"   - 检查浏览器是否支持JavaScript")
    print(f"   - 尝试按 Ctrl+F5 强制刷新")
    print(f"   - 查看浏览器控制台是否有错误")
    print(f"   - 尝试使用不同的浏览器")

if __name__ == "__main__":
    verify_map_layers()
