#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试搜索功能
"""

from integrated_vector_system import IntegratedVectorSystem

def debug_search():
    """调试搜索功能"""
    print("🔍 调试搜索功能")
    print("=" * 50)
    
    # 创建系统实例
    system = IntegratedVectorSystem()
    
    print(f"📊 系统状态:")
    print(f"  - 区域数: {len(system.regions)}")
    print(f"  - 著名地标数: {len(system.famous_places)}")
    
    print(f"\n🏛️ 著名地标列表:")
    for name, info in system.famous_places.items():
        print(f"  - {name} -> {info['region']}")
    
    print(f"\n🔍 测试搜索 '北京':")
    query = "北京"
    query_lower = query.lower()
    
    # 测试著名地标搜索
    print(f"  搜索著名地标:")
    for name, info in system.famous_places.items():
        if query_lower in name.lower():
            print(f"    ✅ 找到: {name}")
        else:
            print(f"    ❌ 不匹配: {name}")
    
    # 测试区域搜索
    print(f"  搜索区域:")
    for region_name, region in system.regions.items():
        if query_lower in region_name.lower():
            print(f"    ✅ 找到: {region_name}")
        else:
            print(f"    ❌ 不匹配: {region_name}")
    
    # 测试完整搜索
    print(f"\n🔍 完整搜索测试:")
    results = system.search_poi(query)
    print(f"  搜索结果数量: {len(results)}")
    for result in results:
        print(f"    - {result['name']} ({result['type']}) - {result['region']}")

if __name__ == "__main__":
    debug_search()
