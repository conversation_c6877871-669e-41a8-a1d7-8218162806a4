#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的瓦片效果
验证不同缩放级别的瓦片是否正常显示
"""

import os
import sys
from pathlib import Path
from PIL import Image

def test_tile_quality():
    """测试瓦片质量"""
    print("🧪 测试修复后的瓦片质量")
    print("=" * 50)
    
    tiles_dir = Path("F:/monitor1/static/tiles")
    if not tiles_dir.exists():
        print(f"❌ 瓦片目录不存在: {tiles_dir}")
        return False
    
    # 测试不同缩放级别的瓦片
    test_cases = [
        (10, 840, 385, "第10级瓦片"),
        (12, 3363, 1525, "第12级瓦片"),
        (13, 6726, 3050, "第13级瓦片"),
        (14, 13452, 6100, "第14级瓦片"),
        (15, 26904, 12200, "第15级瓦片"),
    ]
    
    results = []
    
    for zoom, x, y, description in test_cases:
        print(f"\n🔍 测试 {description} ({zoom}/{x}/{y}):")
        
        tile_path = tiles_dir / str(zoom) / str(x) / f"{y}.png"
        
        if not tile_path.exists():
            print(f"  ❌ 瓦片文件不存在")
            results.append((zoom, False, 0, "文件不存在"))
            continue
        
        try:
            # 检查文件大小
            file_size = tile_path.stat().st_size
            print(f"  📊 文件大小: {file_size} 字节")
            
            # 检查图像质量
            with Image.open(tile_path) as img:
                width, height = img.size
                mode = img.mode
                
                print(f"  📐 图像尺寸: {width}x{height}")
                print(f"  🎨 颜色模式: {mode}")
                
                # 检查是否为空白图像
                if file_size < 1000:
                    print(f"  ❌ 文件过小，可能是空白瓦片")
                    results.append((zoom, False, file_size, "文件过小"))
                elif file_size > 50000:
                    print(f"  ⚠️  文件较大，可能包含过多细节")
                    results.append((zoom, True, file_size, "文件较大"))
                else:
                    print(f"  ✅ 文件大小正常")
                    results.append((zoom, True, file_size, "正常"))
                    
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
            results.append((zoom, False, 0, f"读取失败: {e}"))
    
    # 统计结果
    print(f"\n📊 测试结果统计:")
    print(f"=" * 50)
    
    total_tests = len(results)
    successful_tests = sum(1 for _, success, _, _ in results if success)
    
    print(f"总测试数: {total_tests}")
    print(f"成功数: {successful_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    print(f"\n详细结果:")
    for zoom, success, size, status in results:
        status_icon = "✅" if success else "❌"
        print(f"  {status_icon} 缩放级别 {zoom}: {size} 字节 - {status}")
    
    return successful_tests >= total_tests * 0.8  # 80%以上成功率

def test_map_display():
    """测试地图显示效果"""
    print(f"\n🗺️  地图显示测试")
    print(f"=" * 50)
    
    # 检查是否有足够的瓦片用于显示
    tiles_dir = Path("F:/monitor1/static/tiles")
    
    zoom_stats = {}
    for zoom_dir in tiles_dir.iterdir():
        if not zoom_dir.is_dir() or not zoom_dir.name.isdigit():
            continue
            
        zoom = int(zoom_dir.name)
        tile_count = 0
        
        for x_dir in zoom_dir.iterdir():
            if x_dir.is_dir():
                tile_count += len(list(x_dir.glob("*.png")))
        
        zoom_stats[zoom] = tile_count
        print(f"缩放级别 {zoom}: {tile_count} 个瓦片")
    
    # 检查关键缩放级别
    key_zooms = [10, 12, 14, 15]
    available_zooms = [zoom for zoom in key_zooms if zoom_stats.get(zoom, 0) > 0]
    
    print(f"\n可用缩放级别: {available_zooms}")
    
    if len(available_zooms) >= 3:
        print(f"✅ 地图显示条件满足")
        return True
    else:
        print(f"❌ 地图显示条件不满足")
        return False

def main():
    """主函数"""
    print("🚀 瓦片修复效果测试")
    print("=" * 50)
    
    try:
        # 测试瓦片质量
        quality_ok = test_tile_quality()
        
        # 测试地图显示
        display_ok = test_map_display()
        
        print(f"\n🎯 最终结果:")
        print(f"=" * 50)
        
        if quality_ok and display_ok:
            print(f"🎉 瓦片修复成功！")
            print(f"✅ 瓦片质量正常")
            print(f"✅ 地图显示正常")
            print(f"\n💡 建议:")
            print(f"   1. 启动系统: python start_system.py")
            print(f"   2. 访问地图: http://localhost:5000")
            print(f"   3. 测试不同缩放级别的地图显示")
        else:
            print(f"⚠️  瓦片修复部分成功")
            if not quality_ok:
                print(f"❌ 瓦片质量有问题")
            if not display_ok:
                print(f"❌ 地图显示有问题")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False
    
    return quality_ok and display_ok

if __name__ == "__main__":
    main()

