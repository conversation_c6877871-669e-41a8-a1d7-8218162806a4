#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单搜索测试
"""

import requests
import json

def simple_search_test():
    """简单搜索测试"""
    print("🔍 简单搜索测试")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试基本连接
    print("1. 测试基本连接...")
    try:
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 系统连接正常")
            print(f"   📊 区域数: {stats['total_regions']}, 著名地标: {stats['famous_places_count']}")
        else:
            print(f"   ❌ 系统响应异常: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 系统连接失败: {e}")
        return
    
    # 测试区域数据
    print("\n2. 测试区域数据...")
    try:
        response = requests.get(f"{base_url}/api/regions")
        if response.status_code == 200:
            regions = response.json()
            print(f"   ✅ 区域数据正常: {len(regions)} 个区域")
            
            # 检查四川
            if "四川" in regions:
                print(f"   ✅ 四川区域存在")
                region = regions["四川"]
                print(f"   - 状态: {region.get('status', 'unknown')}")
                print(f"   - 中心: {region.get('center', 'unknown')}")
            else:
                print(f"   ❌ 四川区域不存在")
                print(f"   - 可用区域: {list(regions.keys())[:5]}...")
        else:
            print(f"   ❌ 区域数据异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 区域数据异常: {e}")
    
    # 测试著名地标
    print("\n3. 测试著名地标...")
    try:
        response = requests.get(f"{base_url}/api/famous-places")
        if response.status_code == 200:
            places = response.json()
            print(f"   ✅ 著名地标正常: {len(places)} 个")
            
            # 检查九寨沟
            if "九寨沟" in places:
                print(f"   ✅ 九寨沟地标存在")
                place = places["九寨沟"]
                print(f"   - 坐标: {place.get('lat', 'unknown')}, {place.get('lon', 'unknown')}")
                print(f"   - 区域: {place.get('region', 'unknown')}")
            else:
                print(f"   ❌ 九寨沟地标不存在")
                print(f"   - 可用地标: {list(places.keys())[:5]}...")
        else:
            print(f"   ❌ 著名地标异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 著名地标异常: {e}")
    
    # 测试搜索功能
    print("\n4. 测试搜索功能...")
    test_queries = ["四川", "九寨沟", "北京", "天安门"]
    
    for query in test_queries:
        print(f"\n   测试查询: '{query}'")
        try:
            response = requests.get(f"{base_url}/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   📊 结果数量: {len(results)}")
                
                if results:
                    for result in results[:2]:  # 显示前2个
                        print(f"     - {result.get('name', 'N/A')} ({result.get('type', 'N/A')})")
                else:
                    print(f"     ❌ 无搜索结果")
            else:
                print(f"   ❌ 搜索失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索异常: {e}")
    
    print(f"\n💡 如果搜索返回空结果，可能的原因:")
    print(f"   1. 搜索函数逻辑有问题")
    print(f"   2. 数据格式不匹配")
    print(f"   3. 字符串编码问题")
    print(f"   4. Flask应用缓存问题")

if __name__ == "__main__":
    simple_search_test()
