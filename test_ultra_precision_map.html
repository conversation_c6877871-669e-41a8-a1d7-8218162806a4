<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超高精度地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 600px; width: 100%; border: 2px solid #333; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        .status { font-weight: bold; color: #0066cc; }
        .error { color: #cc0000; }
        .success { color: #006600; }
    </style>
</head>
<body>
    <h1>🎯 超高精度地图测试</h1>
    
    <div class="info">
        <div class="status">测试坐标: 30.295, 109.486 (恩施)</div>
        <div id="status">正在加载...</div>
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <h3>测试说明:</h3>
        <ul>
            <li>红色粗线: 主要道路 (6px)</li>
            <li>橙色线: 次要道路 (4px)</li>
            <li>绿色线: 巷道 (2px)</li>
            <li>黄色建筑: 带红色边框 (3px)</li>
            <li>绿色圆点: POI兴趣点 (8px)</li>
            <li>蓝色圆点: 机井监控点 (6px)</li>
        </ul>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([30.295, 109.486], 18);
        
        // 添加在线地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 状态显示
        const statusDiv = document.getElementById('status');
        
        // 加载超高精度数据
        async function loadUltraPrecisionData() {
            try {
                statusDiv.innerHTML = '正在加载超高精度数据...';
                
                const response = await fetch('/api/ultra-precision-data/恩施');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('超高精度数据:', data);
                
                statusDiv.innerHTML = `<span class="success">✅ 数据加载成功: ${data.metadata.total_features}个要素</span>`;
                
                // 渲染数据
                renderUltraPrecisionData(data);
                
            } catch (error) {
                console.error('加载失败:', error);
                statusDiv.innerHTML = `<span class="error">❌ 加载失败: ${error.message}</span>`;
            }
        }
        
        // 渲染超高精度数据
        function renderUltraPrecisionData(data) {
            if (!data || !map) return;
            
            let renderedCount = 0;
            
            // 渲染道路数据
            if (data.roads && data.roads.features) {
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#666';
                        let weight = 2;
                        
                        switch(highway) {
                            case 'primary':
                                color = '#ff0000';
                                weight = 6;
                                break;
                            case 'secondary':
                                color = '#ff8800';
                                weight = 4;
                                break;
                            case 'residential':
                                color = '#00ff00';
                                weight = 2;
                                break;
                            default:
                                color = '#0000ff';
                                weight = 2;
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 0.9
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway}<br>
                                车道数: ${props.lanes || '未知'}<br>
                                限速: ${props.maxspeed || '未知'}km/h
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.roads.features.length;
            }
            
            // 渲染建筑数据
            if (data.buildings && data.buildings.features) {
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: function(feature) {
                        return {
                            color: '#ff0000',
                            weight: 3,
                            fillColor: '#ffff00',
                            fillOpacity: 0.8
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building}<br>
                                高度: ${props.height || '未知'}米<br>
                                层数: ${props.levels || '未知'}<br>
                                用途: ${props.use || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.buildings.features.length;
            }
            
            // 渲染POI数据
            if (data.pois && data.pois.features) {
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 8,
                            fillColor: '#00ff00',
                            color: '#000000',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.9
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.type}<br>
                                分类: ${props.category}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.pois.features.length;
            }
            
            // 渲染机井数据
            if (data.wells && data.wells.features) {
                const wellsLayer = L.geoJSON(data.wells, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 6,
                            fillColor: '#0066ff',
                            color: '#ffffff',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.9
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名机井'}</strong><br>
                                状态: ${props.status}<br>
                                气体浓度: ${props.gas_level}%<br>
                                风机状态: ${props.fan_status}<br>
                                更新时间: ${props.last_update}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.wells.features.length;
            }
            
            statusDiv.innerHTML += `<br><span class="success">✅ 渲染完成: ${renderedCount}个要素</span>`;
            console.log(`🎯 超高精度矢量数据渲染完成: ${renderedCount}个要素`);
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            loadUltraPrecisionData();
        });
    </script>
</body>
</html>
