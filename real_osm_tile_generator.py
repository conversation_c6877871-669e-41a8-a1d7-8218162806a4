#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实OSM瓦片生成器
使用 china-latest.osm.pbf 生成真实的地图瓦片
"""

import os
import sys
import math
import json
import time
import requests
import subprocess
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class RealOSMTileGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.config_file = self.base_dir / "map_config.json"
        
        # 北京市中心坐标
        self.center_lat = 39.9042
        self.center_lon = 116.4074
        self.radius_km = 50
        self.min_zoom = 10
        self.max_zoom = 18
        
        self.stats = {"success": 0, "failed": 0, "total": 0}
        
        # 多个瓦片源
        self.tile_sources = [
            "https://tile.openstreetmap.org",
            "https://a.tile.openstreetmap.org", 
            "https://b.tile.openstreetmap.org",
            "https://c.tile.openstreetmap.org",
            "https://tile.openstreetmap.de",
            "https://tiles.wmflabs.org/osm"
        ]
        
        # 创建会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        R = 6371
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def get_tile_bounds(self, zoom):
        """计算瓦片边界"""
        center_x, center_y = self.deg2num(self.center_lat, self.center_lon, zoom)
        
        # 更精确的范围计算
        tile_size_km = 40075.0 / (2 ** zoom)
        tile_radius = int(self.radius_km / tile_size_km) + 2
        
        min_x = max(0, center_x - tile_radius)
        max_x = min(2**zoom - 1, center_x + tile_radius)
        min_y = max(0, center_y - tile_radius)
        max_y = min(2**zoom - 1, center_y + tile_radius)
        
        return min_x, max_x, min_y, max_y
    
    def download_real_tile(self, z, x, y):
        """下载真实的OSM瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 如果瓦片已存在且大小合理，跳过
        if tile_file.exists() and tile_file.stat().st_size > 5000:
            return True
        
        # 检查是否在范围内
        tile_lat, tile_lon = self.num2deg(x, y, z)
        distance = self.calculate_distance(self.center_lat, self.center_lon, tile_lat, tile_lon)
        if distance > self.radius_km:
            return False
        
        # 尝试从多个源下载
        for source in self.tile_sources:
            try:
                url = f"{source}/{z}/{x}/{y}.png"
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200 and len(response.content) > 1000:
                    with open(tile_file, 'wb') as f:
                        f.write(response.content)
                    return True
                
                # 请求间隔
                time.sleep(0.2)
                
            except Exception as e:
                continue
        
        return False
    
    def generate_zoom_level(self, zoom):
        """生成指定缩放级别的瓦片"""
        print(f"\n🔍 下载缩放级别 {zoom} 的真实瓦片...")
        
        min_x, max_x, min_y, max_y = self.get_tile_bounds(zoom)
        
        # 过滤范围内的瓦片
        tiles_to_download = []
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                tile_lat, tile_lon = self.num2deg(x, y, zoom)
                distance = self.calculate_distance(self.center_lat, self.center_lon, tile_lat, tile_lon)
                if distance <= self.radius_km:
                    tiles_to_download.append((zoom, x, y))
        
        print(f"   瓦片范围: X({min_x}-{max_x}) Y({min_y}-{max_y})")
        print(f"   范围内瓦片: {len(tiles_to_download)}")
        
        if not tiles_to_download:
            return 0
        
        success_count = 0
        
        # 多线程下载
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_tile = {
                executor.submit(self.download_real_tile, z, x, y): (z, x, y) 
                for z, x, y in tiles_to_download
            }
            
            for i, future in enumerate(as_completed(future_to_tile)):
                z, x, y = future_to_tile[future]
                try:
                    if future.result():
                        success_count += 1
                        self.stats["success"] += 1
                    else:
                        self.stats["failed"] += 1
                except Exception:
                    self.stats["failed"] += 1
                
                self.stats["total"] += 1
                
                # 显示进度
                if (i + 1) % 20 == 0 or (i + 1) == len(tiles_to_download):
                    progress = (i + 1) / len(tiles_to_download) * 100
                    print(f"   进度: {i+1}/{len(tiles_to_download)} ({progress:.1f}%) - 成功: {success_count}")
        
        print(f"   ✅ 级别 {zoom} 完成: {success_count}/{len(tiles_to_download)} 个瓦片")
        return success_count
    
    def generate_real_beijing_map(self):
        """生成真实的北京地图"""
        print("🗺️  真实OSM瓦片下载器")
        print("=" * 50)
        print(f"📍 中心坐标: {self.center_lat:.6f}, {self.center_lon:.6f}")
        print(f"📏 覆盖半径: {self.radius_km} 公里")
        print(f"🔍 精度范围: {self.min_zoom}-{self.max_zoom} 级")
        print(f"🌐 瓦片源: {len(self.tile_sources)} 个")
        
        print(f"\n🚀 开始下载真实瓦片...")
        start_time = time.time()
        
        # 按级别下载
        total_success = 0
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            level_success = self.generate_zoom_level(zoom)
            total_success += level_success
            
            # 每个级别后稍作休息
            if zoom < self.max_zoom:
                time.sleep(1)
        
        # 完成统计
        elapsed_time = time.time() - start_time
        print(f"\n" + "=" * 50)
        print(f"🎉 真实瓦片下载完成!")
        print(f"📊 统计信息:")
        print(f"   成功下载: {self.stats['success']:,} 个瓦片")
        print(f"   下载失败: {self.stats['failed']:,} 个瓦片")
        print(f"   总计处理: {self.stats['total']:,} 个瓦片")
        if self.stats['total'] > 0:
            print(f"   成功率: {self.stats['success']/self.stats['total']*100:.1f}%")
        print(f"   用时: {elapsed_time:.1f} 秒")
        
        # 更新配置
        self.update_config()
        
        return self.stats['success'] > 0
    
    def update_config(self):
        """更新地图配置"""
        config = {
            "name": "北京地区真实离线地图",
            "description": f"真实OSM瓦片数据的北京市中心{self.radius_km}公里地图",
            "center": [self.center_lon, self.center_lat],
            "zoom": 15,
            "minZoom": self.min_zoom,
            "maxZoom": self.max_zoom,
            "radius_km": self.radius_km,
            "generated_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "attribution": "© OpenStreetMap contributors",
            "data_source": "Real OSM Tiles",
            "tiles_downloaded": self.stats['success'],
            "generation_mode": "real_download"
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已更新: {self.config_file}")

def main():
    generator = RealOSMTileGenerator()
    
    try:
        print("⚠️  注意: 这将下载真实的OSM瓦片数据")
        print("   - 需要稳定的网络连接")
        print("   - 下载过程可能较慢")
        print("   - 请遵守OSM使用政策")
        
        confirm = input("\n确认开始下载? (y/N): ").lower()
        if confirm != 'y':
            print("已取消下载")
            return
        
        success = generator.generate_real_beijing_map()
        
        if success:
            print(f"\n🚀 下一步:")
            print(f"   1. 重启地图服务: python start_fixed_map.py")
            print(f"   2. 访问地图: http://localhost:5000")
            print(f"   3. 现在可以看到真实的高质量北京地图!")
        else:
            print(f"\n❌ 下载失败，请检查网络连接")
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断下载")
    except Exception as e:
        print(f"❌ 下载过程出错: {e}")

if __name__ == "__main__":
    main()