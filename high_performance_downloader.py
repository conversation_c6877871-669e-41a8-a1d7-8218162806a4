#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能矢量数据下载器 - 优化并发性能
支持高并发、智能重试、断点续传
"""

import os
import time
import json
import math
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import xml.etree.ElementTree as ET
import threading
from queue import Queue
import psutil

class HighPerformanceDownloader:
    def __init__(self):
        # 根据系统性能自动调整并发数
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # 智能并发数计算
        if memory_gb >= 16 and cpu_count >= 8:
            self.max_workers = 16  # 高性能系统
        elif memory_gb >= 8 and cpu_count >= 4:
            self.max_workers = 12  # 中等性能系统
        else:
            self.max_workers = 8   # 基础性能系统
        
        # 多个Overpass API服务器，提高并发能力
        self.api_servers = [
            "https://overpass-api.de/api/interpreter",
            "https://lz4.overpass-api.de/api/interpreter", 
            "https://z.overpass-api.de/api/interpreter",
            "https://overpass.openstreetmap.ru/api/interpreter"
        ]
        
        self.timeout = 180  # 增加超时时间到3分钟
        self.max_retries = 5  # 增加重试次数
        self.retry_delay = 3  # 减少重试延迟
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 进度跟踪
        self.progress_file = "high_performance_progress.json"
        self.load_progress()
        
        # 统计信息
        self.stats = {
            "total_downloads": 0,
            "successful_downloads": 0,
            "failed_downloads": 0,
            "start_time": None,
            "end_time": None
        }
        
        # 线程锁
        self.lock = threading.Lock()
        
        print(f"🚀 高性能下载器初始化完成")
        print(f"💻 系统配置: CPU={cpu_count}核, 内存={memory_gb:.1f}GB")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"🌐 API服务器: {len(self.api_servers)}个")
        print(f"⏱️ 超时时间: {self.timeout}秒")
    
    def load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                self.progress = json.load(f)
        else:
            self.progress = {}
    
    def save_progress(self):
        """保存下载进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def get_optimal_server(self):
        """获取最优API服务器"""
        # 简单的轮询策略，实际可以加入健康检查
        return self.api_servers[0]  # 使用第一个服务器
    
    def get_overpass_query(self, bbox, data_type):
        """构建优化的Overpass查询语句"""
        if data_type == "roads":
            return f"""
            [out:xml][timeout:600];
            (
              way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)"]({bbox});
              way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)_link"]({bbox});
            );
            out geom;
            """
        elif data_type == "buildings":
            return f"""
            [out:xml][timeout:600];
            (
              way["building"]({bbox});
              way["building:part"]({bbox});
              relation["building"]({bbox});
              way["amenity"~"^(school|hospital|university|restaurant|hotel|bank|police|park|garden)"]({bbox});
              way["shop"]({bbox});
              way["office"]({bbox});
              way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool)"]({bbox});
            );
            out geom;
            """
        elif data_type == "pois":
            return f"""
            [out:xml][timeout:600];
            (
              node["amenity"]({bbox});
              node["shop"]({bbox});
              node["tourism"]({bbox});
              node["leisure"]({bbox});
              node["office"]({bbox});
              node["historic"]({bbox});
              node["natural"]({bbox});
            );
            out;
            """
        else:
            return f"""
            [out:xml][timeout:600];
            (
              way["highway"]({bbox});
              way["building"]({bbox});
              node["amenity"]({bbox});
            );
            out geom;
            """
    
    def download_with_retry(self, url, data, max_retries=None):
        """高性能重试下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                # 使用会话保持连接
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/xml, text/xml, */*',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Connection': 'keep-alive'
                })
                
                response = session.post(
                    url, 
                    data=data, 
                    timeout=self.timeout,
                    stream=True  # 流式下载
                )
                
                if response.status_code == 200:
                    return response
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                # 指数退避
                delay = self.retry_delay * (2 ** attempt)
                print(f"  等待{delay}秒后重试...")
                time.sleep(delay)
        
        return None
    
    def download_region_data(self, region_name, bbox, data_types):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:  # 文件大小大于1KB
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            # 构建查询
            query = self.get_overpass_query(bbox, data_type)
            
            # 选择最优服务器
            server_url = self.get_optimal_server()
            
            # 下载数据
            response = self.download_with_retry(server_url, {"data": query})
            
            if response and response.status_code == 200:
                # 流式保存数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    for chunk in response.iter_content(chunk_size=8192, decode_unicode=True):
                        if chunk:
                            f.write(chunk)
                
                file_size = file_path.stat().st_size
                print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                results[data_type] = "成功"
                
                # 更新统计
                with self.lock:
                    self.stats["successful_downloads"] += 1
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "completed"
                self.save_progress()
                
            else:
                print(f"  ❌ {data_type} 数据下载失败")
                results[data_type] = "失败"
                
                # 更新统计
                with self.lock:
                    self.stats["failed_downloads"] += 1
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "failed"
                self.save_progress()
            
            # 更新总下载数
            with self.lock:
                self.stats["total_downloads"] += 1
        
        return results
    
    def download_provinces(self, provinces, data_types):
        """高性能下载多个省份数据"""
        print(f"🚀 开始高性能下载 {len(provinces)} 个省份的矢量数据")
        print(f"📊 数据类型: {', '.join(data_types)}")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        
        # 记录开始时间
        self.stats["start_time"] = time.time()
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_region = {}
            for region_name, bbox in provinces.items():
                future = executor.submit(self.download_region_data, region_name, bbox, data_types)
                future_to_region[future] = region_name
            
            # 处理结果
            completed = 0
            total = len(provinces)
            
            for future in as_completed(future_to_region):
                region_name = future_to_region[future]
                try:
                    result = future.result()
                    results[region_name] = result
                    completed += 1
                    
                    # 显示进度
                    progress = (completed / total) * 100
                    print(f"\n📈 进度: {completed}/{total} ({progress:.1f}%)")
                    
                except Exception as e:
                    print(f"❌ {region_name} 下载异常: {e}")
                    results[region_name] = {"error": str(e)}
        
        # 记录结束时间
        self.stats["end_time"] = time.time()
        
        # 显示统计信息
        self.show_statistics()
        
        return results
    
    def show_statistics(self):
        """显示下载统计信息"""
        duration = self.stats["end_time"] - self.stats["start_time"]
        
        print(f"\n📊 下载统计:")
        print(f"⏱️ 总耗时: {duration:.1f}秒")
        print(f"📥 总下载: {self.stats['total_downloads']}")
        print(f"✅ 成功: {self.stats['successful_downloads']}")
        print(f"❌ 失败: {self.stats['failed_downloads']}")
        
        if self.stats['total_downloads'] > 0:
            success_rate = (self.stats['successful_downloads'] / self.stats['total_downloads']) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
        
        if duration > 0:
            speed = self.stats['total_downloads'] / duration
            print(f"⚡ 下载速度: {speed:.2f}个/秒")
    
    def get_download_status(self):
        """获取下载状态"""
        status = {
            "completed": 0,
            "failed": 0,
            "pending": 0,
            "details": {}
        }
        
        for key, value in self.progress.items():
            if value == "completed":
                status["completed"] += 1
            elif value == "failed":
                status["failed"] += 1
            else:
                status["pending"] += 1
            
            status["details"][key] = value
        
        return status
    
    def retry_failed_downloads(self, provinces, data_types):
        """重试失败的下载"""
        print("🔄 重试失败的下载...")
        
        failed_downloads = []
        for key, value in self.progress.items():
            if value == "failed":
                failed_downloads.append(key)
        
        if not failed_downloads:
            print("✅ 没有失败的下载需要重试")
            return
        
        print(f"📋 发现 {len(failed_downloads)} 个失败的下载")
        
        for failed_key in failed_downloads:
            # 解析失败的下载
            parts = failed_key.split('_')
            if len(parts) >= 2:
                region_name = parts[0]
                data_type = parts[1]
                
                if region_name in provinces:
                    print(f"\n🔄 重试 {region_name} 的 {data_type} 数据...")
                    
                    # 删除失败的文件
                    file_path = self.data_dir / region_name / f"{data_type}.osm"
                    if file_path.exists():
                        file_path.unlink()
                    
                    # 重新下载
                    bbox = provinces[region_name]
                    result = self.download_region_data(region_name, bbox, [data_type])
                    
                    if result.get(data_type) == "成功":
                        print(f"✅ {region_name} 的 {data_type} 数据重试成功")
                    else:
                        print(f"❌ {region_name} 的 {data_type} 数据重试失败")

def main():
    """主函数"""
    downloader = HighPerformanceDownloader()
    
    # 全国省份配置
    provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5",
        "天津": "116.8,38.5,118.0,40.2",
        "重庆": "105.0,28.0,110.0,32.0",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0",
        "湖北": "108.0,29.0,117.0,33.0",
        "湖南": "108.0,24.0,114.0,30.0",
        "河北": "113.0,36.0,120.0,42.0",
        "山西": "110.0,34.0,114.0,40.0",
        "辽宁": "118.0,38.0,125.0,43.0",
        "吉林": "121.0,40.0,131.0,46.0",
        "黑龙江": "121.0,43.0,135.0,53.0",
        "安徽": "114.0,29.0,120.0,35.0",
        "福建": "115.0,23.0,120.0,28.0",
        "江西": "113.0,24.0,118.0,30.0",
        "广西": "104.0,20.0,112.0,26.0",
        "海南": "108.0,18.0,111.0,20.0",
        "贵州": "103.0,24.0,109.0,29.0",
        "云南": "97.0,21.0,106.0,29.0",
        "西藏": "78.0,27.0,99.0,36.0",
        "陕西": "105.0,31.0,111.0,39.0",
        "甘肃": "92.0,32.0,109.0,43.0",
        "青海": "89.0,31.0,103.0,39.0",
        "宁夏": "104.0,35.0,107.0,39.0",
        "新疆": "73.0,34.0,96.0,49.0",
        "内蒙古": "97.0,37.0,126.0,53.0",
        "香港": "113.8,22.1,114.4,22.6",
        "澳门": "113.5,22.1,113.6,22.2",
        "台湾": "119.0,21.0,122.0,25.0"
    }
    
    # 数据类型
    data_types = ["roads", "buildings", "pois"]
    
    print("🚀 高性能矢量数据下载器")
    print("=" * 60)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 下载全国数据 (34个省份)")
        print("2. 下载重点城市数据")
        print("3. 查看下载状态")
        print("4. 重试失败下载")
        print("5. 清理进度文件")
        print("6. 系统性能测试")
        print("7. 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == "1":
            print(f"\n🚀 开始下载全国 {len(provinces)} 个省份的数据...")
            print("⚠️ 注意: 这将需要较长时间和大量存储空间")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_provinces(provinces, data_types)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "2":
            # 重点城市
            key_cities = {
                "北京": "116.0,39.4,117.0,40.2",
                "上海": "121.0,31.0,122.0,31.5",
                "广州": "112.8,22.8,113.8,23.5",
                "深圳": "113.7,22.4,114.6,22.9",
                "杭州": "119.8,30.0,120.5,30.5",
                "南京": "118.4,31.8,119.2,32.3",
                "成都": "103.8,30.4,104.3,30.9",
                "武汉": "114.0,30.4,114.6,30.8",
                "西安": "108.7,34.1,109.2,34.5",
                "重庆": "106.3,29.3,106.8,29.8"
            }
            
            print(f"\n🏙️ 开始下载 {len(key_cities)} 个重点城市的数据...")
            results = downloader.download_provinces(key_cities, data_types)
            
            print("\n📊 下载结果汇总:")
            for region, result in results.items():
                print(f"\n{region}:")
                for data_type, status in result.items():
                    if status == "成功":
                        print(f"  ✅ {data_type}: {status}")
                    elif status == "失败":
                        print(f"  ❌ {data_type}: {status}")
                    else:
                        print(f"  ⏭️ {data_type}: {status}")
        
        elif choice == "3":
            status = downloader.get_download_status()
            print(f"\n📊 下载状态:")
            print(f"✅ 已完成: {status['completed']}")
            print(f"❌ 失败: {status['failed']}")
            print(f"⏳ 待处理: {status['pending']}")
            
            if status['details']:
                print(f"\n📋 详细信息:")
                for key, value in status['details'].items():
                    print(f"  {key}: {value}")
        
        elif choice == "4":
            downloader.retry_failed_downloads(provinces, data_types)
        
        elif choice == "5":
            if os.path.exists(downloader.progress_file):
                os.remove(downloader.progress_file)
                print("✅ 进度文件已清理")
            else:
                print("ℹ️ 进度文件不存在")
        
        elif choice == "6":
            print("\n🔍 系统性能测试:")
            print(f"CPU核心数: {psutil.cpu_count()}")
            print(f"内存总量: {psutil.virtual_memory().total / (1024**3):.1f}GB")
            print(f"内存使用率: {psutil.virtual_memory().percent}%")
            print(f"磁盘使用率: {psutil.disk_usage('/').percent}%")
            print(f"推荐并发数: {downloader.max_workers}")
        
        elif choice == "7":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
