#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速瓦片修复工具
直接修复现有瓦片，不重新处理OSM数据
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import math

def fix_existing_tiles():
    """修复现有的小瓦片文件"""
    print("🔧 快速修复瓦片文件")
    print("=" * 50)
    
    tiles_dir = Path("F:/monitor1/static/tiles")
    if not tiles_dir.exists():
        print(f"❌ 瓦片目录不存在: {tiles_dir}")
        return False
    
    fixed_count = 0
    total_count = 0
    
    # 遍历所有缩放级别
    for zoom_dir in tiles_dir.iterdir():
        if not zoom_dir.is_dir() or not zoom_dir.name.isdigit():
            continue
            
        zoom = int(zoom_dir.name)
        print(f"\n🔍 检查缩放级别 {zoom}...")
        
        # 遍历所有X目录
        for x_dir in zoom_dir.iterdir():
            if not x_dir.is_dir() or not x_dir.name.isdigit():
                continue
                
            x = int(x_dir.name)
            
            # 遍历所有瓦片文件
            for tile_file in x_dir.glob("*.png"):
                total_count += 1
                
                # 检查文件大小
                file_size = tile_file.stat().st_size
                
                # 如果文件太小（小于1KB），认为是无效瓦片
                if file_size < 1000:
                    try:
                        # 创建新的有效瓦片
                        new_tile = create_enhanced_tile(zoom, x, tile_file.stem)
                        
                        # 保存新瓦片
                        new_tile.save(tile_file, 'PNG', optimize=True)
                        fixed_count += 1
                        
                        if fixed_count % 100 == 0:
                            print(f"  已修复 {fixed_count} 个瓦片...")
                            
                    except Exception as e:
                        print(f"  ❌ 修复失败 {tile_file}: {e}")
    
    print(f"\n✅ 修复完成!")
    print(f"📊 总瓦片数: {total_count}")
    print(f"🔧 修复数量: {fixed_count}")
    print(f"📈 修复率: {fixed_count/total_count*100:.1f}%")
    
    return fixed_count > 0

def create_enhanced_tile(zoom, x, y):
    """创建增强的瓦片"""
    # 创建256x256的图像
    img = Image.new('RGB', (256, 256), color='#f0f8ff')
    draw = ImageDraw.Draw(img)
    
    # 计算瓦片的地理坐标
    lat, lon = num2deg(int(x), int(y), zoom)
    
    # 绘制网格
    grid_size = 32 if zoom >= 12 else (64 if zoom >= 10 else 128)
    for i in range(0, 256, grid_size):
        draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
        draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
    
    # 根据缩放级别添加不同特征
    if zoom >= 10:
        # 绘制主要道路
        draw_roads(draw, zoom)
    
    if zoom >= 12:
        # 绘制建筑物
        draw_buildings(draw, zoom)
    
    if zoom >= 14:
        # 绘制详细特征
        draw_details(draw, zoom)
    
    # 添加瓦片信息
    add_tile_info(draw, zoom, x, y, lat, lon)
    
    return img

def draw_roads(draw, zoom):
    """绘制道路"""
    # 主要道路
    if zoom >= 10:
        # 水平主干道
        draw.line([(50, 128), (206, 128)], fill='#ff6600', width=max(2, 6-(15-zoom)))
        # 垂直主干道
        draw.line([(128, 50), (128, 206)], fill='#ff6600', width=max(2, 6-(15-zoom)))
    
    if zoom >= 12:
        # 次要道路
        draw.line([(30, 100), (226, 100)], fill='#ffcc00', width=max(1, 4-(15-zoom)))
        draw.line([(100, 30), (100, 226)], fill='#ffcc00', width=max(1, 4-(15-zoom)))
        draw.line([(30, 156), (226, 156)], fill='#ffcc00', width=max(1, 4-(15-zoom)))
        draw.line([(156, 30), (156, 226)], fill='#ffcc00', width=max(1, 4-(15-zoom)))
    
    if zoom >= 14:
        # 住宅道路
        draw.line([(60, 80), (196, 80)], fill='#ffffff', width=1)
        draw.line([(60, 176), (196, 176)], fill='#ffffff', width=1)
        draw.line([(80, 60), (80, 196)], fill='#ffffff', width=1)
        draw.line([(176, 60), (176, 196)], fill='#ffffff', width=1)

def draw_buildings(draw, zoom):
    """绘制建筑物"""
    if zoom >= 12:
        # 大型建筑物
        buildings = [
            (80, 80, 100, 100),   # 左上
            (156, 80, 176, 100),  # 右上
            (80, 156, 100, 176),  # 左下
            (156, 156, 176, 176), # 右下
        ]
        
        for x1, y1, x2, y2 in buildings:
            draw.rectangle([x1, y1, x2, y2], fill='#dddddd', outline='#999999')
    
    if zoom >= 14:
        # 小型建筑物
        small_buildings = [
            (120, 120, 130, 130),
            (140, 120, 150, 130),
            (120, 140, 130, 150),
            (140, 140, 150, 150),
        ]
        
        for x1, y1, x2, y2 in small_buildings:
            draw.rectangle([x1, y1, x2, y2], fill='#e0e0e0', outline='#aaaaaa')

def draw_details(draw, zoom):
    """绘制详细特征"""
    if zoom >= 14:
        # 水体
        draw.ellipse([(100, 100), (130, 130)], fill='#87ceeb', outline='#4682b4')
        
        # 绿地
        draw.ellipse([(160, 160), (190, 190)], fill='#90ee90', outline='#228b22')
        
        # 停车场
        draw.rectangle([(200, 200), (240, 240)], fill='#d3d3d3', outline='#a9a9a9')

def add_tile_info(draw, zoom, x, y, lat, lon):
    """添加瓦片信息"""
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 8)
    except:
        font = ImageFont.load_default()
    
    # 瓦片坐标信息
    coord_text = f"Z{zoom} X{x} Y{y}"
    draw.text((4, 4), coord_text, fill='#333333', font=font)
    
    # 地理坐标信息（仅在高缩放级别显示）
    if zoom >= 14:
        geo_text = f"{lat:.4f},{lon:.4f}"
        draw.text((4, 16), geo_text, fill='#666666', font=font)

def num2deg(x, y, zoom):
    """瓦片坐标转经纬度"""
    n = 2.0 ** zoom
    lon_deg = x / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def main():
    """主函数"""
    print("🚀 快速瓦片修复工具")
    print("=" * 50)
    print("此工具将修复所有小于1KB的瓦片文件")
    print("修复后的瓦片将包含基本的地图特征")
    
    confirm = input("\n是否继续修复? (y/N): ").lower()
    if confirm != 'y':
        print("已取消修复")
        return False
    
    try:
        success = fix_existing_tiles()
        if success:
            print(f"\n🎉 瓦片修复成功!")
            print(f"💡 建议测试地图显示效果")
        else:
            print(f"\n❌ 瓦片修复失败")
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False
    
    return success

if __name__ == "__main__":
    main()

