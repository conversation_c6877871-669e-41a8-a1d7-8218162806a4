#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全国县级市和乡镇数据库
包含全国主要县级市、县、区及其下属乡镇的坐标信息
"""

# 全国县级市和主要乡镇数据库
COUNTY_TOWN_DATABASE = {
    # 四川省
    "四川": {
        "成都市": {
            "lat": 30.5728, "lon": 104.0668,
            "counties": {
                "都江堰市": {"lat": 31.0014, "lon": 103.6189, "towns": ["灌口镇", "幸福镇", "青城山镇"]},
                "彭州市": {"lat": 30.9889, "lon": 103.9581, "towns": ["天彭镇", "濛阳镇", "丽春镇"]},
                "邛崃市": {"lat": 30.4153, "lon": 103.4642, "towns": ["临邛镇", "平乐镇", "夹关镇"]},
                "崇州市": {"lat": 30.6306, "lon": 103.6731, "towns": ["崇阳镇", "怀远镇", "元通镇"]},
                "金堂县": {"lat": 30.8619, "lon": 104.4156, "towns": ["赵镇", "淮口镇", "竹篙镇"]},
                "双流区": {"lat": 30.5742, "lon": 103.9236, "towns": ["东升镇", "华阳镇", "西航港街道"]},
                "郫都区": {"lat": 30.8081, "lon": 103.8881, "towns": ["郫筒镇", "犀浦镇", "红光镇"]},
                "新都区": {"lat": 30.8236, "lon": 104.1581, "towns": ["新都镇", "大丰镇", "石板滩镇"]},
                "温江区": {"lat": 30.6906, "lon": 103.8369, "towns": ["柳城街道", "万春镇", "永宁镇"]},
                "龙泉驿区": {"lat": 30.5569, "lon": 104.2681, "towns": ["龙泉街道", "大面镇", "十陵镇"]}
            }
        },
        "绵阳市": {
            "lat": 31.4678, "lon": 104.6791,
            "counties": {
                "江油市": {"lat": 31.7781, "lon": 104.7442, "towns": ["中坝镇", "武都镇", "青莲镇"]},
                "三台县": {"lat": 31.0956, "lon": 105.0956, "towns": ["潼川镇", "芦溪镇", "西平镇"]},
                "盐亭县": {"lat": 31.2236, "lon": 105.3881, "towns": ["云溪镇", "富驿镇", "玉龙镇"]},
                "安县": {"lat": 31.5356, "lon": 104.5656, "towns": ["花荄镇", "桑枣镇", "秀水镇"]},
                "梓潼县": {"lat": 31.6356, "lon": 105.1631, "towns": ["文昌镇", "许州镇", "黎雅镇"]},
                "北川羌族自治县": {"lat": 31.8236, "lon": 104.4656, "towns": ["永昌镇", "曲山镇", "擂鼓镇"]},
                "平武县": {"lat": 32.4081, "lon": 104.5231, "towns": ["龙安镇", "古城镇", "响岩镇"]}
            }
        },
        "德阳市": {
            "lat": 31.1269, "lon": 104.3981,
            "counties": {
                "什邡市": {"lat": 31.1269, "lon": 104.1631, "towns": ["方亭街道", "蓥华镇", "洛水镇"]},
                "广汉市": {"lat": 30.9769, "lon": 104.2819, "towns": ["雒城镇", "新丰镇", "连山镇"]},
                "绵竹市": {"lat": 31.3431, "lon": 104.1981, "towns": ["剑南镇", "汉旺镇", "九龙镇"]},
                "中江县": {"lat": 31.0369, "lon": 104.6781, "towns": ["凯江镇", "仓山镇", "龙台镇"]},
                "罗江县": {"lat": 31.3169, "lon": 104.5081, "towns": ["万安镇", "金山镇", "略坪镇"]}
            }
        },
        "南充市": {
            "lat": 30.8378, "lon": 106.1106,
            "counties": {
                "阆中市": {"lat": 31.5581, "lon": 106.0056, "towns": ["保宁街道", "七里街道", "江南街道"]},
                "南部县": {"lat": 31.3531, "lon": 106.0631, "towns": ["南隆镇", "建兴镇", "伏虎镇"]},
                "营山县": {"lat": 31.0769, "lon": 106.5656, "towns": ["朗池镇", "小桥镇", "新店镇"]},
                "蓬安县": {"lat": 31.0281, "lon": 106.4131, "towns": ["相如镇", "河舒镇", "徐家镇"]},
                "仪陇县": {"lat": 31.2719, "lon": 106.3031, "towns": ["新政镇", "金城镇", "马鞍镇"]},
                "西充县": {"lat": 31.0031, "lon": 105.8881, "towns": ["晋城镇", "太平镇", "多扶镇"]}
            }
        }
    },
    
    # 广东省
    "广东": {
        "广州市": {
            "lat": 23.1291, "lon": 113.2644,
            "counties": {
                "从化区": {"lat": 23.5456, "lon": 113.5869, "towns": ["街口街道", "江埔街道", "城郊街道"]},
                "增城区": {"lat": 23.2906, "lon": 113.8281, "towns": ["荔城街道", "新塘镇", "石滩镇"]},
                "花都区": {"lat": 23.3769, "lon": 113.2206, "towns": ["新华街道", "花山镇", "炭步镇"]},
                "番禺区": {"lat": 22.9381, "lon": 113.3831, "towns": ["市桥街道", "大石街道", "洛浦街道"]},
                "南沙区": {"lat": 22.8019, "lon": 113.5256, "towns": ["南沙街道", "黄阁镇", "横沥镇"]},
                "白云区": {"lat": 23.1581, "lon": 113.2631, "towns": ["三元里街道", "同和街道", "太和镇"]},
                "黄埔区": {"lat": 23.1031, "lon": 113.4656, "towns": ["黄埔街道", "鱼珠街道", "长洲街道"]},
                "天河区": {"lat": 23.1369, "lon": 113.3319, "towns": ["五山街道", "员村街道", "车陂街道"]},
                "越秀区": {"lat": 23.1281, "lon": 113.2644, "towns": ["北京街道", "人民街道", "光塔街道"]},
                "荔湾区": {"lat": 23.1256, "lon": 113.2431, "towns": ["沙面街道", "岭南街道", "华林街道"]},
                "海珠区": {"lat": 23.1031, "lon": 113.2631, "towns": ["赤岗街道", "新港街道", "昌岗街道"]}
            }
        },
        "深圳市": {
            "lat": 22.5431, "lon": 114.0579,
            "counties": {
                "罗湖区": {"lat": 22.5481, "lon": 114.1319, "towns": ["桂园街道", "黄贝街道", "东门街道"]},
                "福田区": {"lat": 22.5231, "lon": 114.0519, "towns": ["园岭街道", "南园街道", "华富街道"]},
                "南山区": {"lat": 22.5319, "lon": 113.9306, "towns": ["南头街道", "南山街道", "沙河街道"]},
                "宝安区": {"lat": 22.5556, "lon": 113.8831, "towns": ["新安街道", "西乡街道", "福永街道"]},
                "龙岗区": {"lat": 22.7206, "lon": 114.2481, "towns": ["平湖街道", "布吉街道", "坂田街道"]},
                "盐田区": {"lat": 22.5556, "lon": 114.2256, "towns": ["沙头角街道", "海山街道", "盐田街道"]},
                "龙华区": {"lat": 22.6569, "lon": 114.0431, "towns": ["观湖街道", "民治街道", "龙华街道"]},
                "坪山区": {"lat": 22.6906, "lon": 114.3469, "towns": ["坪山街道", "坑梓街道", "龙田街道"]},
                "光明区": {"lat": 22.7481, "lon": 113.9356, "towns": ["光明街道", "公明街道", "新湖街道"]}
            }
        },
        "东莞市": {
            "lat": 23.0206, "lon": 113.7519,
            "counties": {
                "莞城街道": {"lat": 23.0206, "lon": 113.7519, "towns": ["东正社区", "西隅社区", "北隅社区"]},
                "南城街道": {"lat": 23.0206, "lon": 113.7519, "towns": ["胜和社区", "元美社区", "鸿福社区"]},
                "东城街道": {"lat": 23.0206, "lon": 113.7519, "towns": ["东泰社区", "主山社区", "温塘社区"]},
                "万江街道": {"lat": 23.0206, "lon": 113.7519, "towns": ["万江社区", "新村社区", "拔蛟窝社区"]},
                "石碣镇": {"lat": 23.1031, "lon": 113.8131, "towns": ["石碣村", "单屋村", "水南村"]},
                "石龙镇": {"lat": 23.1031, "lon": 113.8631, "towns": ["中山东社区", "中山西社区", "兴龙社区"]},
                "茶山镇": {"lat": 23.0769, "lon": 113.8631, "towns": ["茶山村", "上元村", "下朗村"]},
                "石排镇": {"lat": 23.1031, "lon": 113.9131, "towns": ["石排村", "福隆村", "田寮村"]},
                "企石镇": {"lat": 23.0769, "lon": 113.9631, "towns": ["企石村", "江边村", "东山村"]},
                "横沥镇": {"lat": 23.0206, "lon": 113.9631, "towns": ["横沥村", "田坑村", "田头村"]},
                "桥头镇": {"lat": 23.0206, "lon": 114.0131, "towns": ["桥头村", "田新村", "邓屋村"]},
                "谢岗镇": {"lat": 22.9631, "lon": 114.0131, "towns": ["谢岗村", "黎村", "赵林村"]},
                "东坑镇": {"lat": 23.0206, "lon": 113.9131, "towns": ["东坑村", "坑美村", "井美村"]},
                "常平镇": {"lat": 23.0206, "lon": 113.9631, "towns": ["常平村", "板石村", "土塘村"]},
                "寮步镇": {"lat": 23.0206, "lon": 113.8631, "towns": ["寮步村", "石步村", "凫山村"]},
                "樟木头镇": {"lat": 22.9131, "lon": 114.0631, "towns": ["樟木头村", "石新村", "柏地村"]},
                "大朗镇": {"lat": 22.9631, "lon": 113.9131, "towns": ["大朗村", "长塘村", "巷头村"]},
                "黄江镇": {"lat": 22.9131, "lon": 113.9631, "towns": ["黄江村", "田美村", "板湖村"]},
                "清溪镇": {"lat": 22.8631, "lon": 114.1631, "towns": ["清溪村", "浮岗村", "大利村"]},
                "塘厦镇": {"lat": 22.8131, "lon": 114.0631, "towns": ["塘厦村", "林村", "石潭埔村"]},
                "凤岗镇": {"lat": 22.7631, "lon": 114.1131, "towns": ["凤岗村", "雁田村", "官井头村"]},
                "大岭山镇": {"lat": 22.9131, "lon": 113.8131, "towns": ["大岭山村", "连平村", "大片美村"]},
                "长安镇": {"lat": 22.8131, "lon": 113.8131, "towns": ["长安村", "乌沙村", "锦厦村"]},
                "虎门镇": {"lat": 22.8131, "lon": 113.6731, "towns": ["虎门村", "南栅村", "北栅村"]},
                "厚街镇": {"lat": 22.8631, "lon": 113.6731, "towns": ["厚街村", "珊美村", "河田村"]},
                "沙田镇": {"lat": 22.9131, "lon": 113.6131, "towns": ["沙田村", "齐沙村", "义沙村"]},
                "道滘镇": {"lat": 23.0206, "lon": 113.6731, "towns": ["道滘村", "南城村", "永庆村"]},
                "洪梅镇": {"lat": 23.0206, "lon": 113.6131, "towns": ["洪梅村", "新庄村", "梅沙村"]},
                "麻涌镇": {"lat": 23.0769, "lon": 113.5631, "towns": ["麻涌村", "大步村", "东太村"]},
                "望牛墩镇": {"lat": 23.0206, "lon": 113.5631, "towns": ["望牛墩村", "杜屋村", "李屋村"]},
                "中堂镇": {"lat": 23.0769, "lon": 113.6131, "towns": ["中堂村", "潢涌村", "三涌村"]},
                "高埗镇": {"lat": 23.0769, "lon": 113.7131, "towns": ["高埗村", "低涌村", "冼沙村"]}
            }
        }
    },
    
    # 江苏省
    "江苏": {
        "南京市": {
            "lat": 32.0603, "lon": 118.7969,
            "counties": {
                "溧水区": {"lat": 31.6531, "lon": 119.0281, "towns": ["永阳街道", "柘塘街道", "白马镇"]},
                "高淳区": {"lat": 31.3281, "lon": 118.8731, "towns": ["淳溪街道", "古柏街道", "阳江镇"]},
                "六合区": {"lat": 32.3431, "lon": 118.8431, "towns": ["雄州街道", "龙池街道", "程桥街道"]},
                "浦口区": {"lat": 32.0581, "lon": 118.6231, "towns": ["江浦街道", "桥林街道", "汤泉街道"]},
                "栖霞区": {"lat": 32.1231, "lon": 118.9131, "towns": ["尧化街道", "马群街道", "燕子矶街道"]},
                "雨花台区": {"lat": 32.0031, "lon": 118.7731, "towns": ["雨花街道", "赛虹桥街道", "西善桥街道"]},
                "江宁区": {"lat": 31.9531, "lon": 118.8431, "towns": ["东山街道", "秣陵街道", "汤山街道"]},
                "建邺区": {"lat": 32.0031, "lon": 118.7231, "towns": ["莫愁湖街道", "南苑街道", "兴隆街道"]},
                "鼓楼区": {"lat": 32.0581, "lon": 118.7731, "towns": ["华侨路街道", "宁海路街道", "湖南路街道"]},
                "秦淮区": {"lat": 32.0231, "lon": 118.7931, "towns": ["夫子庙街道", "中华门街道", "双塘街道"]},
                "玄武区": {"lat": 32.0481, "lon": 118.8031, "towns": ["梅园新村街道", "新街口街道", "锁金村街道"]}
            }
        },
        "苏州市": {
            "lat": 31.2989, "lon": 120.5853,
            "counties": {
                "常熟市": {"lat": 31.6531, "lon": 120.7481, "towns": ["虞山街道", "琴川街道", "莫城街道"]},
                "张家港市": {"lat": 31.8731, "lon": 120.5531, "towns": ["杨舍镇", "塘桥镇", "金港镇"]},
                "昆山市": {"lat": 31.3831, "lon": 120.9531, "towns": ["玉山镇", "巴城镇", "周市镇"]},
                "太仓市": {"lat": 31.4531, "lon": 121.1131, "towns": ["城厢镇", "沙溪镇", "浏河镇"]},
                "吴江区": {"lat": 31.1531, "lon": 120.6431, "towns": ["松陵镇", "同里镇", "平望镇"]},
                "吴中区": {"lat": 31.2731, "lon": 120.6231, "towns": ["长桥街道", "苏苑街道", "龙西街道"]},
                "相城区": {"lat": 31.3731, "lon": 120.6331, "towns": ["元和街道", "太平街道", "黄埭镇"]},
                "姑苏区": {"lat": 31.2989, "lon": 120.5853, "towns": ["平江街道", "沧浪街道", "金阊街道"]},
                "虎丘区": {"lat": 31.3231, "lon": 120.5531, "towns": ["狮山街道", "枫桥街道", "浒墅关镇"]}
            }
        }
    },
    
    # 浙江省
    "浙江": {
        "杭州市": {
            "lat": 30.2741, "lon": 120.1551,
            "counties": {
                "建德市": {"lat": 29.4731, "lon": 119.2831, "towns": ["新安江街道", "更楼街道", "洋溪街道"]},
                "富阳区": {"lat": 30.0481, "lon": 119.9531, "towns": ["富春街道", "春江街道", "东洲街道"]},
                "临安区": {"lat": 30.2331, "lon": 119.7131, "towns": ["锦城街道", "锦北街道", "玲珑街道"]},
                "桐庐县": {"lat": 29.7931, "lon": 119.6831, "towns": ["桐君街道", "城南街道", "旧县街道"]},
                "淳安县": {"lat": 29.6031, "lon": 119.0431, "towns": ["千岛湖镇", "威坪镇", "姜家镇"]},
                "萧山区": {"lat": 30.1631, "lon": 120.2631, "towns": ["城厢街道", "北干街道", "蜀山街道"]},
                "余杭区": {"lat": 30.3831, "lon": 120.3031, "towns": ["临平街道", "南苑街道", "东湖街道"]},
                "西湖区": {"lat": 30.2331, "lon": 120.1231, "towns": ["北山街道", "西溪街道", "翠苑街道"]},
                "上城区": {"lat": 30.2431, "lon": 120.1631, "towns": ["清波街道", "湖滨街道", "小营街道"]},
                "下城区": {"lat": 30.2731, "lon": 120.1631, "towns": ["武林街道", "天水街道", "朝晖街道"]},
                "江干区": {"lat": 30.2531, "lon": 120.2031, "towns": ["凯旋街道", "采荷街道", "闸弄口街道"]},
                "拱墅区": {"lat": 30.3131, "lon": 120.1431, "towns": ["米市巷街道", "湖墅街道", "小河街道"]},
                "滨江区": {"lat": 30.1931, "lon": 120.2131, "towns": ["西兴街道", "长河街道", "浦沿街道"]}
            }
        },
        "宁波市": {
            "lat": 29.8683, "lon": 121.5440,
            "counties": {
                "余姚市": {"lat": 30.0331, "lon": 121.1531, "towns": ["凤山街道", "阳明街道", "梨洲街道"]},
                "慈溪市": {"lat": 30.1731, "lon": 121.2631, "towns": ["浒山街道", "古塘街道", "白沙路街道"]},
                "奉化区": {"lat": 29.6531, "lon": 121.4031, "towns": ["锦屏街道", "岳林街道", "江口街道"]},
                "象山县": {"lat": 29.4731, "lon": 121.8731, "towns": ["丹东街道", "丹西街道", "爵溪街道"]},
                "宁海县": {"lat": 29.2831, "lon": 121.4231, "towns": ["跃龙街道", "桃源街道", "梅林街道"]},
                "鄞州区": {"lat": 29.8131, "lon": 121.5431, "towns": ["中河街道", "钟公庙街道", "下应街道"]},
                "海曙区": {"lat": 29.8731, "lon": 121.5431, "towns": ["鼓楼街道", "南门街道", "西门街道"]},
                "江北区": {"lat": 29.8831, "lon": 121.5531, "towns": ["中马街道", "白沙街道", "文教街道"]},
                "北仑区": {"lat": 29.9031, "lon": 121.8431, "towns": ["新碶街道", "小港街道", "大碶街道"]},
                "镇海区": {"lat": 29.9531, "lon": 121.7131, "towns": ["招宝山街道", "蛟川街道", "骆驼街道"]}
            }
        }
    }
}

def get_county_town_data():
    """获取县级市和乡镇数据"""
    return COUNTY_TOWN_DATABASE

def search_county_town(query):
    """搜索县级市和乡镇"""
    results = []
    query_lower = query.lower()
    
    for province, cities in COUNTY_TOWN_DATABASE.items():
        for city, data in cities.items():
            # 搜索地级市
            if query_lower in city.lower():
                results.append({
                    "name": city,
                    "lat": data["lat"],
                    "lon": data["lon"],
                    "region": province,
                    "type": "city",
                    "level": "地级市"
                })
            
            # 搜索县级市/区/县
            if "counties" in data:
                for county, county_data in data["counties"].items():
                    if query_lower in county.lower():
                        results.append({
                            "name": county,
                            "lat": county_data["lat"],
                            "lon": county_data["lon"],
                            "region": f"{province}-{city}",
                            "type": "county",
                            "level": "县级市/区/县"
                        })
                    
                    # 搜索乡镇
                    if "towns" in county_data:
                        for town in county_data["towns"]:
                            if query_lower in town.lower():
                                results.append({
                                    "name": town,
                                    "lat": county_data["lat"] + (hash(town) % 100 - 50) * 0.001,  # 添加小偏移
                                    "lon": county_data["lon"] + (hash(town) % 100 - 50) * 0.001,
                                    "region": f"{province}-{city}-{county}",
                                    "type": "town",
                                    "level": "乡镇",
                                    "county": county
                                })
    
    return results

if __name__ == "__main__":
    # 测试搜索功能
    test_queries = ["成都", "都江堰", "灌口镇", "广州", "深圳", "东莞", "南京", "苏州", "杭州", "宁波"]
    
    for query in test_queries:
        results = search_county_town(query)
        print(f"\n搜索 '{query}':")
        for result in results[:3]:  # 只显示前3个结果
            print(f"  - {result['name']} ({result['level']}) - {result['region']} - 坐标: {result['lat']:.4f}, {result['lon']:.4f}")
