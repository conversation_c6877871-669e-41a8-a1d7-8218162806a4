#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动矢量地图查看器
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🗺️  启动矢量地图查看器")
    print("=" * 50)
    
    # 检查数据
    vector_dir = Path("static/vector_data")
    if not vector_dir.exists():
        print("❌ 矢量数据目录不存在")
        print("   请先运行: python simple_vector_downloader.py")
        return
    
    regions = [d for d in vector_dir.iterdir() if d.is_dir()]
    if not regions:
        print("❌ 没有找到矢量数据")
        print("   请先运行: python simple_vector_downloader.py")
        return
    
    print(f"✅ 找到 {len(regions)} 个区域的矢量数据")
    
    # 启动地图查看器
    print("\n🚀 启动矢量地图查看器...")
    print("   访问地址: http://localhost:5000")
    print("   按 Ctrl+C 停止服务")
    
    try:
        # 导入并启动Flask应用
        from vector_map_viewer import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
