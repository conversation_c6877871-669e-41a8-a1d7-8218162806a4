# 离线地图下载指南

## 概述
本工具支持下载中国全国18级瓦片作为离线地图使用，支持多种地图源。

## 支持的地图源

### 1. OpenStreetMap (OSM)
- **特点**: 开源免费，全球覆盖
- **最大缩放**: 19级
- **推荐**: 适合国际使用

### 2. 百度地图
- **特点**: 中国本土化，中文标注
- **最大缩放**: 19级
- **推荐**: 适合中国国内使用

### 3. 高德地图
- **特点**: 中国本土化，详细道路信息
- **最大缩放**: 18级
- **推荐**: 适合导航使用

## 使用方法

### 方法1: 交互式下载 (推荐)
```bash
python interactive_download.py
```
- 提供友好的菜单界面
- 支持选择地图源和下载范围
- 实时显示下载进度

### 方法2: 直接下载
```bash
python download_china_18.py
```
- 直接下载全国18级瓦片
- 支持选择地图源
- 批量处理，适合长时间运行

### 方法3: 测试下载
```bash
python test_china_download.py
```
- 先测试小范围瓦片
- 验证地图源可用性
- 检查瓦片质量

## 下载范围

### 全国范围
- **覆盖**: 中国全境
- **瓦片数量**: 约100万+ (18级)
- **下载时间**: 数小时到数天
- **存储空间**: 数GB到数十GB

### 区域范围
- **覆盖**: 指定中心点和半径
- **瓦片数量**: 根据范围而定
- **下载时间**: 几分钟到几小时
- **存储空间**: 几MB到几GB

## 参数设置

### 并发数 (max_workers)
- **推荐值**: 6-8
- **说明**: 同时下载的瓦片数量
- **注意**: 过高可能导致被限制

### 批次大小 (batch_size)
- **推荐值**: 500-1000
- **说明**: 每批处理的瓦片数量
- **注意**: 影响内存使用

## 瓦片存储

### 目录结构
```
static/tiles/
├── 18/
│   ├── 215837/
│   │   ├── 99333.png
│   │   └── 99334.png
│   └── 215838/
│       └── 99333.png
└── 19/
    └── ...
```

### 文件命名
- 格式: `{z}/{x}/{y}.png`
- z: 缩放级别
- x: 瓦片X坐标
- y: 瓦片Y坐标

## 质量检查

### 文件大小
- **正常**: > 2000 bytes
- **异常**: < 1000 bytes
- **说明**: 过小的文件可能是错误页面

### 内容检查
- 使用 `test_tile_quality()` 函数
- 检查瓦片是否包含有效内容
- 验证地图源可用性

## 服务器配置

### 启动瓦片服务器
```bash
python tileserver.py
```
- 端口: 8080
- 访问: http://localhost:8080
- API: http://localhost:8080/tiles/{z}/{x}/{y}.png

### 集成到现有系统
- 修改地图配置指向本地服务器
- 更新瓦片URL为本地地址
- 确保瓦片目录权限正确

## 注意事项

### 1. 网络限制
- 某些地图源可能有访问限制
- 建议使用VPN或代理
- 控制下载速度避免被封

### 2. 存储空间
- 18级全国瓦片需要大量存储空间
- 建议预留50GB以上空间
- 定期清理无效瓦片

### 3. 法律合规
- 遵守地图服务商的使用条款
- 仅用于个人学习研究
- 不得用于商业用途

### 4. 性能优化
- 使用SSD存储提高访问速度
- 合理设置并发数
- 定期检查瓦片完整性

## 故障排除

### 常见问题

1. **下载失败**
   - 检查网络连接
   - 尝试更换地图源
   - 降低并发数

2. **瓦片空白**
   - 检查文件大小
   - 重新下载问题瓦片
   - 验证地图源可用性

3. **服务器无法访问**
   - 检查端口是否被占用
   - 确认瓦片目录存在
   - 检查文件权限

### 日志分析
- 查看下载日志
- 统计成功率
- 识别问题瓦片

## 更新维护

### 定期更新
- 瓦片数据会定期更新
- 建议每季度重新下载
- 关注地图源变化

### 增量更新
- 只下载新增或变更的瓦片
- 使用时间戳标记
- 提高更新效率

## 技术支持

如有问题，请检查：
1. 网络连接状态
2. 地图源可用性
3. 存储空间充足
4. 文件权限正确

---

**注意**: 本工具仅供学习研究使用，请遵守相关法律法规和地图服务商的使用条款。
