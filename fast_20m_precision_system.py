#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速20米精度离线地图系统
优化版本，专门针对小区域快速生成高精度瓦片
"""

import os
import sys
import json
import math
import time
import osmium
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from flask import Flask, render_template, jsonify, request, send_file
import threading
from concurrent.futures import ThreadPoolExecutor
import sqlite3
import tempfile

class FastOSMProcessor(osmium.SimpleHandler):
    """快速OSM数据处理器 - 针对小区域优化"""
    
    def __init__(self, bbox, zoom_level, max_nodes=50000):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox
        self.zoom = zoom_level
        self.max_nodes = max_nodes
        self.nodes = {}
        self.ways = []
        self.buildings = []
        self.roads = []
        self.water_features = []
        self.pois = []
        self.node_count = 0
        self.way_count = 0
        
    def node(self, n):
        """处理节点数据 - 快速版本"""
        self.node_count += 1
        
        # 限制节点数量以提高速度
        if self.node_count > self.max_nodes:
            return
        
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 8),
                'lat': round(n.location.lat, 8),
                'tags': dict(n.tags)
            }
            
            # 检查是否为POI
            if n.tags:
                poi_tags = ['amenity', 'shop', 'tourism', 'leisure']
                if any(tag in n.tags for tag in poi_tags):
                    self.pois.append({
                        'id': n.id,
                        'lon': round(n.location.lon, 8),
                        'lat': round(n.location.lat, 8),
                        'tags': dict(n.tags)
                    })
    
    def way(self, w):
        """处理路径数据 - 快速版本"""
        self.way_count += 1
        
        # 限制处理的路径数量
        if self.way_count > 10000:
            return
        
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if len(relevant_nodes) >= 2:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            # 分类存储
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)
            
            self.ways.append(way_data)

class Fast20mPrecisionSystem:
    """快速20米精度系统"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.tiles_dir = self.base_dir / "static" / "fast_precision_tiles"
        self.db_path = self.base_dir / "fast_precision_data.db"
        
        # 创建目录
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        self.setup_routes()
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tiles (
                z INTEGER,
                x INTEGER,
                y INTEGER,
                data BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (z, x, y)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('fast_20m_precision.html')
        
        @self.app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
        def get_tile(z, x, y):
            """获取快速生成的高精度瓦片"""
            # 检查缓存
            tile_data = self.get_cached_tile(z, x, y)
            if tile_data:
                return send_file(tile_data, mimetype='image/png')
            
            # 快速生成瓦片
            tile_path = self.generate_fast_tile(z, x, y)
            if tile_path and tile_path.exists():
                return send_file(str(tile_path), mimetype='image/png')
            
            # 返回空瓦片
            return self.create_empty_tile()
        
        @self.app.route('/api/generate-fast-area')
        def generate_fast_area():
            """快速生成区域数据"""
            lat = request.args.get('lat', type=float)
            lon = request.args.get('lon', type=float)
            
            if lat is None or lon is None:
                return jsonify({'error': '缺少坐标参数'}), 400
            
            # 异步快速生成
            threading.Thread(
                target=self.generate_fast_area_data,
                args=(lat, lon)
            ).start()
            
            return jsonify({
                'status': 'started',
                'message': f'开始快速生成 ({lat}, {lon}) 的高精度数据'
            })
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def generate_fast_tile(self, z, x, y):
        """快速生成高精度瓦片"""
        try:
            print(f"快速生成瓦片 {z}/{x}/{y}")
            start_time = time.time()
            
            bbox = self.get_tile_bbox(x, y, z)
            
            # 使用快速处理器
            processor = FastOSMProcessor(bbox, z, max_nodes=10000)
            
            # 创建临时文件来处理OSM数据的子集
            with tempfile.NamedTemporaryFile(suffix='.osm.pbf', delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                # 使用osmium提取小区域数据
                import subprocess
                extract_cmd = [
                    'osmium', 'extract',
                    '--bbox', f"{bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}",
                    str(self.osm_file),
                    '-o', temp_path
                ]
                
                # 如果osmium命令可用，使用它；否则直接处理原文件
                try:
                    subprocess.run(extract_cmd, check=True, timeout=30)
                    processor.apply_file(temp_path)
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    # 如果osmium不可用或超时，直接处理原文件但限制处理量
                    processor.apply_file(str(self.osm_file))
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
            
            # 创建图像
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
                return (max(0, min(255, px)), max(0, min(255, py)))
            
            # 绘制水体
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4')
            
            # 绘制建筑物
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#dddddd', outline='#999999')
            
            # 绘制道路
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 道路样式
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = max(2, 6 - (18 - z))
                elif highway_type in ['primary', 'secondary']:
                    color = '#ffcc00'
                    width = max(1, 4 - (18 - z))
                else:
                    color = '#ffffff'
                    width = max(1, 2)
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 绘制POI
            for poi in processor.pois:
                px, py = geo_to_pixel(poi['lon'], poi['lat'])
                draw.ellipse([px-2, py-2, px+2, py+2], fill='#ff0000')
            
            # 添加信息
            elapsed = time.time() - start_time
            info_text = f"Z{z} {len(processor.roads)}R {len(processor.buildings)}B {elapsed:.1f}s"
            draw.text((5, 5), info_text, fill='#333333')
            
            # 保存瓦片
            tile_path = self.tiles_dir / str(z) / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            img.save(tile_file, 'PNG', optimize=True)
            
            # 缓存到数据库
            self.cache_tile(z, x, y, tile_file)
            
            print(f"瓦片 {z}/{x}/{y} 生成完成，用时 {elapsed:.1f}秒")
            return tile_file
            
        except Exception as e:
            print(f"快速生成瓦片失败 {z}/{x}/{y}: {e}")
            return None
    
    def get_cached_tile(self, z, x, y):
        """获取缓存瓦片"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute('SELECT data FROM tiles WHERE z=? AND x=? AND y=?', (z, x, y))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                import io
                return io.BytesIO(result[0])
            return None
        except:
            return None
    
    def cache_tile(self, z, x, y, tile_file):
        """缓存瓦片"""
        try:
            with open(tile_file, 'rb') as f:
                tile_data = f.read()
            
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute(
                'INSERT OR REPLACE INTO tiles (z, x, y, data) VALUES (?, ?, ?, ?)',
                (z, x, y, tile_data)
            )
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"缓存瓦片失败: {e}")
    
    def create_empty_tile(self):
        """创建空瓦片"""
        img = Image.new('RGB', (256, 256), color='#f0f0f0')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), "No Data", fill='#999999')
        
        import io
        img_io = io.BytesIO()
        img.save(img_io, 'PNG')
        img_io.seek(0)
        return img_io
    
    def generate_fast_area_data(self, lat, lon):
        """快速生成区域数据"""
        print(f"快速生成区域数据: ({lat}, {lon})")
        
        # 只生成关键缩放级别
        zoom_levels = [16, 17]
        
        for zoom in zoom_levels:
            center_x, center_y = self.deg2num(lat, lon, zoom)
            
            # 生成3x3区域
            tiles_to_generate = []
            for dx in range(-1, 2):
                for dy in range(-1, 2):
                    tile_x = center_x + dx
                    tile_y = center_y + dy
                    
                    max_coord = 2 ** zoom
                    if 0 <= tile_x < max_coord and 0 <= tile_y < max_coord:
                        tiles_to_generate.append((zoom, tile_x, tile_y))
            
            print(f"缩放级别 {zoom}: 生成 {len(tiles_to_generate)} 个瓦片")
            
            # 串行生成以避免资源竞争
            for z, x, y in tiles_to_generate:
                self.generate_fast_tile(z, x, y)
        
        print(f"快速区域数据生成完成: ({lat}, {lon})")
    
    def run(self, host='127.0.0.1', port=5004, debug=False):
        """运行系统"""
        print("🚀 快速20米精度离线地图系统")
        print("=" * 60)
        print(f"🌐 系统地址: http://{host}:{port}")
        print(f"📁 OSM文件: {self.osm_file}")
        print(f"📁 瓦片目录: {self.tiles_dir}")
        print("⚡ 特性:")
        print("  - 快速瓦片生成")
        print("  - 20米精度保证")
        print("  - 8位小数坐标")
        print("  - 智能数据限制")
        print("  - SQLite缓存")
        print("=" * 60)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return
        
        self.app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    system = Fast20mPrecisionSystem()
    system.run()
