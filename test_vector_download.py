#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试矢量数据下载功能
"""

from china_vector_downloader import ChinaVectorDownloader

def test_beijing_vector_download():
    """测试北京市矢量数据下载"""
    print("🧪 测试北京市矢量数据下载")
    print("=" * 40)
    
    downloader = ChinaVectorDownloader()
    
    # 北京市边界框
    beijing_bbox = (116.0, 39.4, 117.0, 40.2)
    
    # 测试下载道路数据
    print("\n1. 测试道路数据下载")
    success, message = downloader.download_osm_vector_data(
        beijing_bbox, 
        downloader.output_dir / "test_beijing_roads.xml",
        ['highway']
    )
    print(f"   结果: {message}")
    
    # 测试下载建筑物数据
    print("\n2. 测试建筑物数据下载")
    success, message = downloader.download_osm_vector_data(
        beijing_bbox, 
        downloader.output_dir / "test_beijing_buildings.xml",
        ['building']
    )
    print(f"   结果: {message}")
    
    # 测试下载全部数据
    print("\n3. 测试全部数据下载")
    success, message = downloader.download_osm_vector_data(
        beijing_bbox, 
        downloader.output_dir / "test_beijing_all.xml",
        ['highway', 'building', 'waterway', 'natural', 'boundary']
    )
    print(f"   结果: {message}")

def test_china_admin_boundaries():
    """测试中国行政区划边界下载"""
    print("\n🧪 测试中国行政区划边界下载")
    print("=" * 40)
    
    downloader = ChinaVectorDownloader()
    
    output_file = downloader.output_dir / "test_china_admin.xml"
    success, message = downloader.download_china_admin_boundaries(output_file)
    print(f"   结果: {message}")

def test_province_download():
    """测试省份下载"""
    print("\n🧪 测试省份下载")
    print("=" * 40)
    
    downloader = ChinaVectorDownloader()
    
    # 测试下载北京市
    beijing = downloader.provinces['1']
    success = downloader.download_province_vector_data(
        beijing['name'], 
        beijing['bbox'], 
        ['highway', 'building']
    )
    print(f"   结果: {'成功' if success else '失败'}")

def test_city_download():
    """测试城市下载"""
    print("\n🧪 测试城市下载")
    print("=" * 40)
    
    downloader = ChinaVectorDownloader()
    
    # 测试下载北京市
    beijing = downloader.cities['1']
    success = downloader.download_city_vector_data(
        beijing['name'], 
        beijing['bbox'], 
        ['highway', 'building']
    )
    print(f"   结果: {'成功' if success else '失败'}")

def main():
    """主函数"""
    print("🧪 矢量数据下载功能测试")
    print("=" * 50)
    
    # 测试北京市矢量数据下载
    test_beijing_vector_download()
    
    # 测试中国行政区划边界下载
    test_china_admin_boundaries()
    
    # 测试省份下载
    test_province_download()
    
    # 测试城市下载
    test_city_download()
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    main()
