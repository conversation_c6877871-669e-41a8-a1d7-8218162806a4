<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化渲染测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 600px; width: 100%; border: 2px solid #333; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🎯 简化渲染测试</h1>
    
    <div class="info">
        <div>测试坐标: 30.295, 109.486 (恩施)</div>
        <div id="status">正在加载...</div>
    </div>
    
    <div id="map"></div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([30.295, 109.486], 16); // 降低缩放级别
        
        // 添加在线地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        const statusDiv = document.getElementById('status');
        
        // 加载并渲染数据
        async function loadAndRender() {
            try {
                statusDiv.innerHTML = '正在加载数据...';
                
                const response = await fetch('/api/ultra-precision-data/恩施');
                const data = await response.json();
                
                statusDiv.innerHTML = `数据加载成功: ${data.metadata.total_features}个要素`;
                
                // 渲染道路 - 使用更明显的样式
                if (data.roads && data.roads.features) {
                    const roadsLayer = L.geoJSON(data.roads, {
                        style: {
                            color: '#ff0000',
                            weight: 15, // 非常粗的线
                            opacity: 1.0
                        }
                    }).addTo(map);
                    console.log(`道路渲染完成: ${data.roads.features.length}条`);
                }
                
                // 渲染建筑 - 使用更明显的样式
                if (data.buildings && data.buildings.features) {
                    const buildingsLayer = L.geoJSON(data.buildings, {
                        style: {
                            color: '#0000ff',
                            weight: 8,
                            fillColor: '#ffff00',
                            fillOpacity: 1.0
                        }
                    }).addTo(map);
                    console.log(`建筑渲染完成: ${data.buildings.features.length}个`);
                }
                
                // 渲染POI - 使用更大的标记
                if (data.pois && data.pois.features) {
                    const poisLayer = L.geoJSON(data.pois, {
                        pointToLayer: function(feature, latlng) {
                            return L.circleMarker(latlng, {
                                radius: 20, // 很大的圆
                                fillColor: '#00ff00',
                                color: '#000000',
                                weight: 5,
                                opacity: 1,
                                fillOpacity: 1.0
                            });
                        }
                    }).addTo(map);
                    console.log(`POI渲染完成: ${data.pois.features.length}个`);
                }
                
                // 渲染机井 - 使用更大的标记
                if (data.wells && data.wells.features) {
                    const wellsLayer = L.geoJSON(data.wells, {
                        pointToLayer: function(feature, latlng) {
                            return L.circleMarker(latlng, {
                                radius: 25, // 很大的圆
                                fillColor: '#ff00ff',
                                color: '#ffffff',
                                weight: 5,
                                opacity: 1,
                                fillOpacity: 1.0
                            });
                        }
                    }).addTo(map);
                    console.log(`机井渲染完成: ${data.wells.features.length}个`);
                }
                
                statusDiv.innerHTML += '<br>✅ 所有数据渲染完成！';
                
            } catch (error) {
                console.error('加载失败:', error);
                statusDiv.innerHTML = `❌ 加载失败: ${error.message}`;
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            loadAndRender();
        });
    </script>
</body>
</html>
