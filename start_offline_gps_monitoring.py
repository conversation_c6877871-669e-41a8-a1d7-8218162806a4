#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线GPS机井监控系统启动器
同时启动瓦片服务器和GPS监控系统
"""

import subprocess
import threading
import time
import webbrowser
import requests
from gps_well_monitoring_system import GPSWellMonitoringSystem

def check_tile_server():
    """检查瓦片服务器是否运行"""
    try:
        response = requests.get('http://127.0.0.1:8080/config', timeout=5)
        return response.status_code == 200
    except:
        return False

def start_tile_server():
    """启动瓦片服务器"""
    print("🗺️  启动本地瓦片服务器...")
    try:
        subprocess.Popen(['python', 'tileserver.py'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # 等待服务器启动
        for i in range(10):
            if check_tile_server():
                print("✅ 瓦片服务器启动成功 (端口: 8080)")
                return True
            time.sleep(1)
        
        print("❌ 瓦片服务器启动失败")
        return False
    except Exception as e:
        print(f"❌ 瓦片服务器启动错误: {e}")
        return False

def start_gps_monitoring():
    """启动GPS监控系统"""
    print("🔧 启动GPS机井监控系统...")
    system = GPSWellMonitoringSystem()
    
    # 延迟启动浏览器
    def open_browser():
        time.sleep(3)
        webbrowser.open('http://127.0.0.1:5000')
        print("🌐 浏览器已自动打开")
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🚀 GPS机井监控系统启动完成")
    print("🌐 系统将在 http://127.0.0.1:5000 启动")
    print("🗺️  瓦片服务器运行在 http://127.0.0.1:8080")
    print("=" * 60)
    
    # 启动Flask应用
    system.run(host='127.0.0.1', port=5000, debug=False)

def main():
    """主函数"""
    print("🔧 离线GPS机井监控系统启动器")
    print("=" * 60)
    print("🎯 系统特点:")
    print("  - 完全离线运行")
    print("  - 使用本地瓦片地图")
    print("  - GPS坐标定位")
    print("  - 机井实时监控")
    print("  - 气体检测数据")
    print("  - 送风机状态监控")
    print("=" * 60)
    
    # 启动瓦片服务器
    if not start_tile_server():
        print("⚠️  瓦片服务器启动失败，系统将在在线模式下运行")
        print("   请确保 tileserver.py 文件存在且可执行")
    
    # 启动GPS监控系统
    start_gps_monitoring()

if __name__ == "__main__":
    main()
