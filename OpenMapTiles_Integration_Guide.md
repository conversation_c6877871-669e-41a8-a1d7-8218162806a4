# OpenMapTiles 离线地图集成指南

## 🗺️ 概述

本指南详细说明如何在野外深机井监控系统中集成OpenMapTiles离线地图，确保系统在内网环境下正常运行。

## 📋 系统要求

- Python 3.7+
- Flask Web框架
- Leaflet.js 地图库
- 足够的存储空间 (地图数据可能较大)

## 🚀 快速开始

### 1. 运行自动设置

```bash
# 运行地图设置脚本
python map_setup.py
```

这将自动完成：
- ✅ 创建必要的目录结构
- ✅ 下载示例地图瓦片
- ✅ 配置瓦片服务器
- ✅ 生成测试页面
- ✅ 更新主应用配置

### 2. 测试离线地图

```bash
# 方法1: 打开测试页面
# 直接在浏览器中打开 map_test.html

# 方法2: 启动瓦片服务器
python tileserver.py
# 然后访问 http://localhost:8080

# 方法3: 启动完整系统
python start_system.py test
# 访问 http://localhost:5000
```

## 📁 目录结构

```
monitor/
├── static/
│   └── tiles/              # 离线地图瓦片目录
│       ├── 10/             # 缩放级别 10
│       │   ├── 838/
│       │   │   ├── 387.png
│       │   │   └── 388.png
│       │   └── 839/
│       ├── 12/             # 缩放级别 12
│       └── 15/             # 缩放级别 15
├── map_data/               # 地图源数据
├── map_config.json         # 地图配置文件
├── map_setup.py           # 地图设置脚本
├── tileserver.py          # 瓦片服务器
├── offline_map_config.py  # 离线地图配置模块
└── map_test.html          # 地图测试页面
```

## 🔧 详细配置

### 1. OpenMapTiles 数据获取

#### 方法A: 使用预构建数据

```bash
# 下载中国地区数据 (示例)
wget https://data.maptiler.com/downloads/tileset/osm/china/china.mbtiles

# 或使用其他OpenMapTiles数据源
# 参考: https://openmaptiles.org/downloads/
```

#### 方法B: 自定义区域数据

```bash
# 使用自定义下载脚本
python download_tiles.py

# 或手动指定区域
python -c "
from map_setup import OpenMapTilesSetup
setup = OpenMapTilesSetup()
setup.download_region_tiles(39.9, 116.4, 40.0, 116.5, [10, 12, 15])
"
```

### 2. 瓦片格式转换

如果您有MBTiles格式的数据，需要转换为目录结构：

```bash
# 安装mbutil工具
pip install mbutil

# 转换MBTiles到目录结构
mb-util china.mbtiles static/tiles --image_format=png
```

### 3. 地图配置

编辑 `map_config.json` 自定义地图设置：

```json
{
  "name": "野外深机井监控地图",
  "center": [116.4074, 39.9042],
  "bounds": [115.0, 38.5, 118.0, 41.0],
  "minzoom": 1,
  "maxzoom": 18,
  "tile_sources": [
    {
      "name": "local_tiles",
      "url": "/static/tiles/{z}/{x}/{y}.png",
      "priority": 1
    },
    {
      "name": "tile_server", 
      "url": "http://localhost:8080/tiles/{z}/{x}/{y}.png",
      "priority": 2
    }
  ]
}
```

## 🌐 多层级地图策略

系统采用三层级地图策略，确保最佳可用性：

### 第1层: 本地离线瓦片
- 路径: `/static/tiles/{z}/{x}/{y}.png`
- 优点: 最快访问速度，无网络依赖
- 缺点: 需要预先下载数据

### 第2层: 本地瓦片服务器
- 路径: `http://localhost:8080/tiles/{z}/{x}/{y}.png`
- 优点: 动态服务，支持更多格式
- 缺点: 需要额外服务进程

### 第3层: 在线备用地图
- 路径: `https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png`
- 优点: 完整覆盖，实时更新
- 缺点: 需要网络连接

## 📊 性能优化

### 1. 瓦片预加载

```javascript
// 在前端预加载关键区域瓦片
function preloadTiles(lat, lng, zoom, radius = 2) {
    for (let dx = -radius; dx <= radius; dx++) {
        for (let dy = -radius; dy <= radius; dy++) {
            const x = Math.floor((lng + 180) / 360 * Math.pow(2, zoom)) + dx;
            const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom)) + dy;
            
            const img = new Image();
            img.src = `/static/tiles/${zoom}/${x}/${y}.png`;
        }
    }
}
```

### 2. 缓存策略

```python
# 在Flask应用中添加缓存头
@app.route('/static/tiles/<path:filename>')
def serve_tile_with_cache(filename):
    response = send_from_directory('static/tiles', filename)
    response.cache_control.max_age = 86400  # 24小时缓存
    return response
```

### 3. 压缩优化

```bash
# 压缩PNG瓦片以节省空间
find static/tiles -name "*.png" -exec optipng -o7 {} \;

# 或使用WebP格式 (需要浏览器支持)
find static/tiles -name "*.png" -exec cwebp -q 80 {} -o {}.webp \;
```

## 🔍 故障排除

### 常见问题

1. **瓦片显示空白**
   ```bash
   # 检查瓦片文件是否存在
   ls -la static/tiles/15/26819/12392.png
   
   # 检查文件权限
   chmod 644 static/tiles/**/*.png
   ```

2. **地图位置偏移**
   ```javascript
   // 检查坐标系统 (WGS84 vs GCJ02)
   // 中国地区可能需要坐标转换
   function wgs84ToGcj02(lng, lat) {
       // 坐标转换算法
   }
   ```

3. **瓦片服务器无法访问**
   ```bash
   # 检查端口占用
   netstat -an | grep 8080
   
   # 检查防火墙设置
   # Windows: 允许Python通过防火墙
   # Linux: sudo ufw allow 8080
   ```

### 调试工具

```bash
# 检查瓦片统计
python -c "
from offline_map_config import offline_map
print(offline_map.get_tile_stats())
"

# 检查位置覆盖
python -c "
from offline_map_config import offline_map
coverage = offline_map.check_tile_coverage(39.9042, 116.4074)
print(coverage)
"
```

## 📈 扩展功能

### 1. 多区域支持

```python
# 添加新的监控区域
regions = {
    "site_1": {"lat": 39.9042, "lng": 116.4074, "name": "1号井"},
    "site_2": {"lat": 31.2304, "lng": 121.4737, "name": "2号井"}
}

for site_id, site_info in regions.items():
    download_region_tiles(site_info["lat"], site_info["lng"])
```

### 2. 实时瓦片更新

```python
# 定期更新瓦片数据
import schedule

def update_tiles():
    # 下载最新瓦片
    pass

schedule.every().day.at("02:00").do(update_tiles)
```

### 3. 地图样式自定义

```css
/* 自定义地图样式 */
.leaflet-tile {
    filter: brightness(0.9) contrast(1.1);
}

/* 夜间模式 */
.night-mode .leaflet-tile {
    filter: invert(1) hue-rotate(180deg) brightness(0.8);
}
```

## 📚 参考资源

- [OpenMapTiles 官方文档](https://openmaptiles.org/docs/)
- [Leaflet.js 文档](https://leafletjs.com/reference.html)
- [MBTiles 规范](https://github.com/mapbox/mbtiles-spec)
- [瓦片地图原理](https://wiki.openstreetmap.org/wiki/Slippy_map_tilenames)

## 🆘 技术支持

如遇到问题，请检查：

1. **系统日志**: 查看控制台输出
2. **网络连接**: 确认瓦片服务器可访问
3. **文件权限**: 确保瓦片文件可读
4. **浏览器控制台**: 查看JavaScript错误
5. **地图配置**: 验证坐标和缩放级别

---

*本指南基于OpenMapTiles开源项目，适用于野外深机井监控系统的离线地图需求。*