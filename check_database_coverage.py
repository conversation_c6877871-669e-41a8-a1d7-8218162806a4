#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库覆盖范围
"""

from china_complete_database import CHINA_COMPLETE_DATABASE

# 全国34个省份/直辖市/自治区完整列表
ALL_34_PROVINCES = [
    # 直辖市 (4个)
    "北京", "上海", "天津", "重庆",
    
    # 省份 (23个)
    "河北", "山西", "辽宁", "吉林", "黑龙江", "江苏", "浙江", "安徽", "福建", "江西", 
    "山东", "河南", "湖北", "湖南", "广东", "海南", "四川", "贵州", "云南", "陕西", 
    "甘肃", "青海", "台湾",
    
    # 自治区 (5个)
    "内蒙古", "广西", "西藏", "宁夏", "新疆",
    
    # 特别行政区 (2个)
    "香港", "澳门"
]

def check_database_coverage():
    """检查数据库覆盖范围"""
    print("🔍 检查数据库覆盖范围")
    print("=" * 60)
    
    # 获取当前数据库中的省份
    current_provinces = set(CHINA_COMPLETE_DATABASE.keys())
    all_provinces_set = set(ALL_34_PROVINCES)
    
    print(f"📊 统计信息:")
    print(f"  - 全国应有省份/直辖市/自治区: {len(ALL_34_PROVINCES)}")
    print(f"  - 当前数据库包含: {len(current_provinces)}")
    print(f"  - 覆盖率: {len(current_provinces)/len(ALL_34_PROVINCES)*100:.1f}%")
    
    # 检查缺失的省份
    missing_provinces = all_provinces_set - current_provinces
    if missing_provinces:
        print(f"\n❌ 缺失的省份/直辖市/自治区 ({len(missing_provinces)}个):")
        for province in sorted(missing_provinces):
            print(f"  - {province}")
    else:
        print(f"\n✅ 所有省份/直辖市/自治区都已包含!")
    
    # 检查多余的省份
    extra_provinces = current_provinces - all_provinces_set
    if extra_provinces:
        print(f"\n⚠️  数据库中的额外省份 ({len(extra_provinces)}个):")
        for province in sorted(extra_provinces):
            print(f"  - {province}")
    
    print(f"\n📋 当前数据库包含的省份:")
    for i, province in enumerate(sorted(current_provinces), 1):
        print(f"  {i:2d}. {province}")
    
    return missing_provinces, extra_provinces

if __name__ == "__main__":
    missing, extra = check_database_coverage()
    
    if missing:
        print(f"\n🔧 需要添加 {len(missing)} 个省份/直辖市/自治区")
        print("建议运行数据库扩展脚本")
    else:
        print(f"\n🎉 数据库已包含全国所有34个省份/直辖市/自治区!")
