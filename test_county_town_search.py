#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试县级市和乡镇搜索功能
"""

import requests
import json

def test_county_town_search():
    """测试县级市和乡镇搜索功能"""
    print("🏛️ 测试县级市和乡镇搜索功能")
    print("=" * 60)
    
    # 测试不同类型的搜索
    test_queries = [
        # 地级市
        "北京", "上海", "天津", "重庆", "石家庄", "太原", "沈阳", "大连",
        # 县级市/区/县
        "东城区", "黄浦区", "和平区", "万州区", "长安区", "小店区", "和平区", "中山区",
        # 乡镇
        "东华门", "南京东路", "劝业场", "高笋塘", "建北", "平阳路", "太原街", "青泥洼桥"
    ]
    
    for query in test_queries:
        print(f"\n🔍 搜索: {query}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ 找到 {len(results)} 个结果:")
                for i, result in enumerate(results[:3]):  # 只显示前3个
                    level_info = f" ({result.get('level', '')})" if result.get('level') else ""
                    county_info = f" [所属: {result['county']}]" if result.get('county') else ""
                    print(f"      {i+1}. {result['name']} - {result['region']}{level_info}{county_info}")
                if len(results) > 3:
                    print(f"      ... 还有 {len(results)-3} 个结果")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    # 测试快速搜索
    print(f"\n🚀 测试快速搜索:")
    quick_test_queries = ["北京", "东城区", "东华门", "上海", "黄浦区", "南京东路"]
    
    for query in quick_test_queries:
        print(f"\n🔍 快速搜索: {query}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/quick-search?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ 找到 {len(results)} 个结果:")
                for i, result in enumerate(results[:2]):  # 只显示前2个
                    level_info = f" ({result.get('level', '')})" if result.get('level') else ""
                    county_info = f" [所属: {result['county']}]" if result.get('county') else ""
                    print(f"      {i+1}. {result['name']} - {result['region']}{level_info}{county_info}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    test_county_town_search()
