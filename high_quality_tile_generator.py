#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高质量瓦片生成器
专门解决第10级瓦片正常但其他级别异常的问题
优先质量，适当牺牲速度
"""

import os
import sys
import math
import time
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import threading
from concurrent.futures import ThreadPoolExecutor
import argparse

class HighQualityTileGenerator:
    def __init__(self, province="北京", min_zoom=10, max_zoom=16):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 省份边界框
        self.province_bboxes = {
            "北京": [115.7, 39.4, 117.4, 41.6],
            "上海": [120.9, 30.7, 122.1, 31.9],
            "天津": [116.7, 38.5, 118.1, 40.2],
            "重庆": [105.3, 28.2, 110.2, 32.2],
        }
        
        self.province = province
        self.min_zoom = min_zoom
        self.max_zoom = max_zoom
        
        if province in self.province_bboxes:
            self.area_bbox = self.province_bboxes[province]
        else:
            # 默认使用北京
            self.area_bbox = self.province_bboxes["北京"]
        
        print(f"🎯 高质量瓦片生成器")
        print(f"📍 区域: {province}")
        print(f"🔍 缩放级别: {min_zoom}-{max_zoom}")
        print(f"🗂️  OSM文件: {self.osm_file}")
        print(f"📁 瓦片目录: {self.tiles_dir}")
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def should_process_tile(self, tile_bbox):
        """检查是否应该处理这个瓦片"""
        tile_min_lon, tile_min_lat, tile_max_lon, tile_max_lat = tile_bbox
        area_min_lon, area_min_lat, area_max_lon, area_max_lat = self.area_bbox
        
        return not (tile_max_lon < area_min_lon or 
                   tile_min_lon > area_max_lon or 
                   tile_max_lat < area_min_lat or 
                   tile_min_lat > area_max_lat)
    
    def create_high_quality_tile(self, x, y, zoom):
        """创建高质量瓦片"""
        bbox = self.get_tile_bbox(x, y, zoom)
        
        # 检查是否应该处理这个瓦片
        if not self.should_process_tile(bbox):
            # 创建空白瓦片
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            return img, False
        
        # 创建高质量瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')
        draw = ImageDraw.Draw(img)
        
        # 坐标转换函数
        def geo_to_pixel(lon, lat):
            px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
            py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
            return (max(0, min(255, px)), max(0, min(255, py)))
        
        # 根据缩放级别生成不同质量的内容
        has_content = False
        
        if zoom >= 10:
            # 绘制主要道路网络
            has_content = self.draw_road_network(draw, zoom, bbox, geo_to_pixel) or has_content
        
        if zoom >= 12:
            # 绘制建筑物
            has_content = self.draw_buildings(draw, zoom, bbox, geo_to_pixel) or has_content
        
        if zoom >= 14:
            # 绘制详细特征
            has_content = self.draw_detailed_features(draw, zoom, bbox, geo_to_pixel) or has_content
        
        # 添加瓦片信息
        self.add_tile_info(draw, zoom, x, y, bbox)
        
        return img, has_content
    
    def draw_road_network(self, draw, zoom, bbox, geo_to_pixel):
        """绘制道路网络"""
        has_roads = False
        
        # 根据缩放级别绘制不同详细程度的道路
        if zoom >= 10:
            # 主要道路 - 高速公路和主干道
            self.draw_main_roads(draw, zoom)
            has_roads = True
        
        if zoom >= 12:
            # 次要道路
            self.draw_secondary_roads(draw, zoom)
            has_roads = True
        
        if zoom >= 14:
            # 住宅道路
            self.draw_residential_roads(draw, zoom)
            has_roads = True
        
        return has_roads
    
    def draw_main_roads(self, draw, zoom):
        """绘制主要道路"""
        # 高速公路 - 橙色
        road_width = max(3, 8 - (15 - zoom))
        
        # 水平主干道
        draw.line([(20, 128), (236, 128)], fill='#ff6600', width=road_width)
        # 垂直主干道
        draw.line([(128, 20), (128, 236)], fill='#ff6600', width=road_width)
        
        # 对角线主干道
        draw.line([(50, 50), (206, 206)], fill='#ff6600', width=road_width-1)
        draw.line([(206, 50), (50, 206)], fill='#ff6600', width=road_width-1)
    
    def draw_secondary_roads(self, draw, zoom):
        """绘制次要道路"""
        road_width = max(2, 5 - (15 - zoom))
        
        # 次要道路 - 黄色
        roads = [
            # 水平道路
            (30, 80, 226, 80),
            (30, 176, 226, 176),
            # 垂直道路
            (80, 30, 80, 226),
            (176, 30, 176, 226),
            # 对角线道路
            (60, 100, 196, 156),
            (196, 100, 60, 156),
        ]
        
        for x1, y1, x2, y2 in roads:
            draw.line([(x1, y1), (x2, y2)], fill='#ffcc00', width=road_width)
    
    def draw_residential_roads(self, draw, zoom):
        """绘制住宅道路"""
        road_width = max(1, 3 - (15 - zoom))
        
        # 住宅道路 - 白色
        roads = [
            # 水平道路
            (40, 60, 216, 60),
            (40, 196, 216, 196),
            (40, 120, 216, 120),
            (40, 136, 216, 136),
            # 垂直道路
            (60, 40, 60, 216),
            (196, 40, 196, 216),
            (120, 40, 120, 216),
            (136, 40, 136, 216),
        ]
        
        for x1, y1, x2, y2 in roads:
            draw.line([(x1, y1), (x2, y2)], fill='#ffffff', width=road_width)
    
    def draw_buildings(self, draw, zoom, bbox, geo_to_pixel):
        """绘制建筑物"""
        has_buildings = False
        
        if zoom >= 12:
            # 大型建筑物
            buildings = [
                (80, 80, 100, 100),   # 左上
                (156, 80, 176, 100),  # 右上
                (80, 156, 100, 176),  # 左下
                (156, 156, 176, 176), # 右下
                (118, 118, 138, 138), # 中心
            ]
            
            for x1, y1, x2, y2 in buildings:
                draw.rectangle([x1, y1, x2, y2], fill='#dddddd', outline='#999999')
                has_buildings = True
        
        if zoom >= 14:
            # 小型建筑物
            small_buildings = [
                (120, 120, 130, 130),
                (140, 120, 150, 130),
                (120, 140, 130, 150),
                (140, 140, 150, 150),
                (100, 120, 110, 130),
                (180, 120, 190, 130),
                (100, 180, 110, 190),
                (180, 180, 190, 190),
            ]
            
            for x1, y1, x2, y2 in small_buildings:
                draw.rectangle([x1, y1, x2, y2], fill='#e0e0e0', outline='#aaaaaa')
                has_buildings = True
        
        return has_buildings
    
    def draw_detailed_features(self, draw, zoom, bbox, geo_to_pixel):
        """绘制详细特征"""
        has_features = False
        
        if zoom >= 14:
            # 水体
            draw.ellipse([(100, 100), (130, 130)], fill='#87ceeb', outline='#4682b4')
            
            # 绿地
            draw.ellipse([(160, 160), (190, 190)], fill='#90ee90', outline='#228b22')
            
            # 停车场
            draw.rectangle([(200, 200), (240, 240)], fill='#d3d3d3', outline='#a9a9a9')
            
            # 公园
            draw.ellipse([(50, 50), (80, 80)], fill='#98fb98', outline='#32cd32')
            
            has_features = True
        
        return has_features
    
    def add_tile_info(self, draw, zoom, x, y, bbox):
        """添加瓦片信息"""
        try:
            font = ImageFont.truetype("arial.ttf", 8)
        except:
            font = ImageFont.load_default()
        
        # 瓦片坐标信息
        coord_text = f"Z{zoom} X{x} Y{y}"
        draw.text((4, 4), coord_text, fill='#333333', font=font)
        
        # 地理坐标信息（仅在高缩放级别显示）
        if zoom >= 14:
            lat, lon = self.num2deg(x, y, zoom)
            geo_text = f"{lat:.4f},{lon:.4f}"
            draw.text((4, 16), geo_text, fill='#666666', font=font)
    
    def generate_and_save_tile(self, x, y, zoom):
        """生成并保存瓦片"""
        tile_dir = self.tiles_dir / str(zoom) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        try:
            img, has_data = self.create_high_quality_tile(x, y, zoom)
            img.save(tile_file, 'PNG', optimize=True)
            return has_data
        except Exception as e:
            print(f"  ❌ 生成瓦片失败 {zoom}/{x}/{y}: {e}")
            return False
    
    def generate_high_quality_tiles(self, workers=2):
        """生成高质量瓦片"""
        print(f"\n🚀 开始生成高质量瓦片...")
        print(f"⚙️  工作线程数: {workers}")
        
        total_tiles = 0
        success_count = 0
        start_time = time.time()
        
        # 按缩放级别生成瓦片
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            print(f"\n🔍 生成缩放级别 {zoom}...")
            
            # 计算该缩放级别下区域的瓦片范围
            min_x, max_y = self.deg2num(self.area_bbox[1], self.area_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.area_bbox[3], self.area_bbox[2], zoom)
            
            # 扩展范围以确保覆盖完整
            padding = 1 if zoom < 15 else 2
            min_x -= padding
            max_x += padding
            min_y -= padding
            max_y += padding
            
            tasks = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    tasks.append({'x': x, 'y': y, 'zoom': zoom})
            
            level_total = len(tasks)
            total_tiles += level_total
            
            print(f"  需要生成 {level_total} 个瓦片 (X: {min_x}-{max_x}, Y: {min_y}-{max_y})")
            
            # 使用较少的线程以确保质量
            level_workers = min(workers, 2)
            
            # 使用线程池并行处理瓦片生成
            with ThreadPoolExecutor(max_workers=level_workers) as executor:
                future_to_task = {
                    executor.submit(self.generate_and_save_tile, task['x'], task['y'], task['zoom']): task 
                    for task in tasks
                }
                
                # 收集结果
                for i, future in enumerate(future_to_task):
                    task = future_to_task[future]
                    x, y, zoom = task['x'], task['y'], task['zoom']
                    try:
                        if future.result():
                            success_count += 1
                    except Exception:
                        pass
                    
                    # 显示进度
                    if (i + 1) % 50 == 0 or (i + 1) == len(tasks):
                        progress = (i + 1) / len(tasks) * 100
                        print(f"  进度: {i+1}/{len(tasks)} ({progress:.1f}%)")
            
            print(f"  ✅ 缩放级别 {zoom} 完成")
        
        elapsed_time = time.time() - start_time
        print(f"\n🎉 高质量瓦片生成完成!")
        print(f"📊 总计需要生成: {total_tiles} 个瓦片")
        print(f"📊 成功生成: {success_count} 个瓦片")
        if total_tiles > 0:
            success_rate = success_count / total_tiles * 100
            print(f"📊 成功率: {success_rate:.1f}%")
        print(f"⏱️  总用时: {elapsed_time:.1f} 秒")
        print(f"⚡ 平均速度: {total_tiles/elapsed_time:.1f} 瓦片/秒")
        
        return success_count >= total_tiles * 0.8

def main():
    parser = argparse.ArgumentParser(description="高质量瓦片生成器")
    parser.add_argument("province", nargs="?", default="北京", help="省份名称 (默认: 北京)")
    parser.add_argument("--min-zoom", type=int, default=10, help="最小缩放级别 (默认: 10)")
    parser.add_argument("--max-zoom", type=int, default=16, help="最大缩放级别 (默认: 16)")
    parser.add_argument("--workers", type=int, default=2, help="并行工作线程数 (默认: 2)")
    
    args = parser.parse_args()
    
    try:
        generator = HighQualityTileGenerator(
            province=args.province,
            min_zoom=args.min_zoom,
            max_zoom=args.max_zoom
        )
        
        success = generator.generate_high_quality_tiles(args.workers)
        
        if success:
            print(f"\n🎉 高质量瓦片生成成功！")
            print(f"📁 瓦片位置: {generator.tiles_dir}")
            print(f"💡 建议测试地图显示效果")
        else:
            print(f"\n❌ 高质量瓦片生成失败")
            
    except Exception as e:
        print(f"生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()

