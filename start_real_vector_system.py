#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实矢量数据系统启动器
"""

import os
import sys
import webbrowser
import time
from real_vector_system import RealVectorSystem

def main():
    print("🌍 真实矢量数据系统启动器")
    print("=" * 60)
    print("🚀 启动真实矢量数据系统...")
    print("🌐 系统将在 http://127.0.0.1:5001 启动")
    print("📱 请在浏览器中访问该地址")
    print("🎯 系统功能:")
    print("  - 真实OSM矢量数据")
    print("  - 道路、建筑、POI显示")
    print("  - GPS坐标定位")
    print("  - 高精度地理定位")
    print("=" * 60)
    
    # 检查矢量数据目录
    vector_data_path = os.path.join(os.path.dirname(__file__), 'static', 'vector_data')
    if not os.path.exists(vector_data_path):
        print(f"❌ 矢量数据目录不存在: {vector_data_path}")
        print("请确保矢量数据已下载到正确位置")
        return
    
    # 统计可用区域
    regions = []
    for item in os.listdir(vector_data_path):
        item_path = os.path.join(vector_data_path, item)
        if os.path.isdir(item_path):
            osm_files = [f for f in os.listdir(item_path) if f.endswith('.osm') or f.endswith('.xml')]
            if osm_files:
                regions.append(item)
    
    print(f"📁 矢量数据路径: {vector_data_path}")
    print(f"🗺️ 可用区域: {len(regions)}个")
    if regions:
        print(f"   区域列表: {', '.join(regions[:5])}{'...' if len(regions) > 5 else ''}")
    
    print("=" * 60)
    
    # 启动系统
    system = RealVectorSystem()
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5001')
        print("🌐 浏览器已自动打开")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        system.run(host='127.0.0.1', port=5001, debug=False)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")

if __name__ == "__main__":
    main()
