#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复不完整的瓦片问题
专门解决除了第10级外其他级别瓦片生成不完整的问题
"""

import os
import sys
import math
import time
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class IncompleteTileFixer:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 北京市中心坐标
        self.center_lat = 39.9042
        self.center_lon = 116.4074
        self.radius_km = 50
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        R = 6371  # 地球半径
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def get_tile_bounds(self, zoom):
        """计算指定缩放级别的瓦片边界"""
        # 计算中心瓦片坐标
        center_x, center_y = self.deg2num(self.center_lat, self.center_lon, zoom)
        
        # 根据半径计算瓦片范围
        tile_size_km = 40075.0 / (2 ** zoom)
        tile_radius = int(self.radius_km / tile_size_km) + 3  # 加3确保覆盖完整
        
        min_x = max(0, center_x - tile_radius)
        max_x = min(2**zoom - 1, center_x + tile_radius)
        min_y = max(0, center_y - tile_radius)
        max_y = min(2**zoom - 1, center_y + tile_radius)
        
        return min_x, max_x, min_y, max_y
    
    def download_tile(self, z, x, y):
        """下载单个瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 如果瓦片已存在且大小合理，跳过
        if tile_file.exists() and tile_file.stat().st_size > 10000:  # 大于10KB
            return True, "exists"
        
        # 检查是否在范围内
        tile_lat, tile_lon = self.num2deg(x, y, z)
        distance = self.calculate_distance(self.center_lat, self.center_lon, tile_lat, tile_lon)
        if distance > self.radius_km:
            # 创建空白瓦片而不是跳过
            self.create_blank_tile(tile_file)
            return True, "blank"
        
        # 尝试多个瓦片源
        tile_sources = [
            f"https://tile.openstreetmap.org/{z}/{x}/{y}.png",
            f"https://a.tile.openstreetmap.org/{z}/{x}/{y}.png",
            f"https://b.tile.openstreetmap.org/{z}/{x}/{y}.png",
            f"https://c.tile.openstreetmap.org/{z}/{x}/{y}.png"
        ]
        
        for url in tile_sources:
            try:
                response = self.session.get(url, timeout=15)
                if response.status_code == 200 and len(response.content) > 1000:
                    with open(tile_file, 'wb') as f:
                        f.write(response.content)
                    return True, "downloaded"
                time.sleep(0.1)  # 避免请求过快
            except Exception as e:
                continue
        
        # 如果下载失败，创建空白瓦片
        self.create_blank_tile(tile_file)
        return True, "blank_created"
    
    def create_blank_tile(self, tile_file):
        """创建空白瓦片"""
        from PIL import Image
        img = Image.new('RGB', (256, 256), color='#f0f0f0')
        img.save(tile_file, 'PNG')
    
    def fix_zoom_level(self, zoom, workers=8):
        """修复指定缩放级别的瓦片"""
        print(f"\n🔧 修复缩放级别 {zoom}...")
        
        min_x, max_x, min_y, max_y = self.get_tile_bounds(zoom)
        total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        
        print(f"   瓦片范围: X({min_x}-{max_x}) Y({min_y}-{max_y})")
        print(f"   预计瓦片: {total_tiles}")
        
        # 生成瓦片坐标列表
        tiles_to_process = []
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                tiles_to_process.append((zoom, x, y))
        
        # 多线程处理
        stats = {"downloaded": 0, "exists": 0, "blank": 0, "blank_created": 0, "failed": 0}
        
        with ThreadPoolExecutor(max_workers=workers) as executor:
            future_to_tile = {
                executor.submit(self.download_tile, z, x, y): (z, x, y) 
                for z, x, y in tiles_to_process
            }
            
            for i, future in enumerate(as_completed(future_to_tile)):
                z, x, y = future_to_tile[future]
                try:
                    success, status = future.result()
                    if success:
                        stats[status] = stats.get(status, 0) + 1
                    else:
                        stats["failed"] += 1
                except Exception as e:
                    stats["failed"] += 1
                
                # 显示进度
                if (i + 1) % 100 == 0 or (i + 1) == len(tiles_to_process):
                    progress = (i + 1) / len(tiles_to_process) * 100
                    print(f"   进度: {i+1}/{len(tiles_to_process)} ({progress:.1f}%)")
        
        total_processed = sum(stats.values())
        print(f"   ✅ 级别 {zoom} 修复完成:")
        print(f"      已存在: {stats['exists']}")
        print(f"      下载成功: {stats['downloaded']}")
        print(f"      空白瓦片: {stats['blank']}")
        print(f"      创建空白: {stats['blank_created']}")
        print(f"      失败: {stats['failed']}")
        print(f"      总计: {total_processed}")
        
        return stats
    
    def fix_all_incomplete_levels(self):
        """修复所有不完整的缩放级别"""
        print("🔧 开始修复不完整的瓦片...")
        print(f"📍 中心坐标: {self.center_lat:.6f}, {self.center_lon:.6f}")
        print(f"📏 覆盖半径: {self.radius_km} 公里")
        
        # 需要修复的缩放级别（除了10级，因为10级是完整的）
        zoom_levels_to_fix = [11, 12, 13, 14, 15, 16]
        
        all_stats = {}
        for zoom in zoom_levels_to_fix:
            stats = self.fix_zoom_level(zoom)
            all_stats[zoom] = stats
        
        # 显示总体统计
        print(f"\n" + "=" * 50)
        print(f"📊 总体修复统计:")
        total_downloaded = sum(stats.get('downloaded', 0) for stats in all_stats.values())
        total_created_blank = sum(stats.get('blank_created', 0) for stats in all_stats.values())
        total_failed = sum(stats.get('failed', 0) for stats in all_stats.values())
        
        print(f"   新下载瓦片: {total_downloaded}")
        print(f"   创建空白瓦片: {total_created_blank}")
        print(f"   失败: {total_failed}")
        
        if total_failed == 0:
            print(f"✅ 所有缩放级别的瓦片修复完成!")
            return True
        else:
            print(f"⚠️  修复完成，但有 {total_failed} 个瓦片下载失败")
            return total_failed < 100  # 如果失败少于100个，认为基本成功

def main():
    fixer = IncompleteTileFixer()
    
    try:
        success = fixer.fix_all_incomplete_levels()
        
        if success:
            print(f"\n🚀 下一步:")
            print(f"   1. 验证修复结果: python verify_tile_integrity.py")
            print(f"   2. 重启地图服务: python start_fixed_map.py")
            print(f"   3. 访问地图: http://localhost:5000")
        else:
            print(f"\n❌ 修复失败，请检查网络连接")
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断修复")
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()