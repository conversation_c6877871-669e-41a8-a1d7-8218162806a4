#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线地图修复工具
解决瓦片缺失和显示问题
"""

import os
import json
import subprocess
import sys
from pathlib import Path
import requests
from PIL import Image, ImageDraw, ImageFont
import math

class OfflineMapFixer:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.config_file = self.base_dir / "map_config.json"
        
    def load_config(self):
        """加载地图配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def create_placeholder_tile(self, x, y, z):
        """创建占位瓦片"""
        # 创建256x256的占位图片
        img = Image.new('RGB', (256, 256), color='#f0f0f0')
        draw = ImageDraw.Draw(img)
        
        # 绘制网格
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
            draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
        
        # 添加瓦片信息
        try:
            # 尝试使用默认字体
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"Z{z}/X{x}/Y{y}"
        if font:
            draw.text((10, 10), text, fill='#666666', font=font)
        else:
            draw.text((10, 10), text, fill='#666666')
        
        # 绘制边框
        draw.rectangle([(0, 0), (255, 255)], outline='#cccccc', width=2)
        
        return img
    
    def generate_tiles_for_area(self, center_lat, center_lon, radius_km, min_zoom=10, max_zoom=15):
        """为指定区域生成瓦片"""
        print(f"🗺️  开始生成瓦片...")
        print(f"   中心: ({center_lat}, {center_lon})")
        print(f"   半径: {radius_km} 公里")
        print(f"   缩放级别: {min_zoom}-{max_zoom}")
        
        total_tiles = 0
        
        for zoom in range(min_zoom, max_zoom + 1):
            print(f"\n📍 处理缩放级别 {zoom}...")
            
            # 计算瓦片范围
            center_x, center_y = self.deg2num(center_lat, center_lon, zoom)
            
            # 根据半径计算瓦片范围（粗略估算）
            tile_radius = max(1, int(radius_km / (40075 / (2 ** zoom)) * 1000 / 256))
            
            min_x = max(0, center_x - tile_radius)
            max_x = min(2**zoom - 1, center_x + tile_radius)
            min_y = max(0, center_y - tile_radius)
            max_y = min(2**zoom - 1, center_y + tile_radius)
            
            zoom_tiles = 0
            
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    # 检查距离是否在半径内
                    tile_lat, tile_lon = self.num2deg(x, y, zoom)
                    distance = self.calculate_distance(center_lat, center_lon, tile_lat, tile_lon)
                    
                    if distance <= radius_km:
                        if self.create_tile(x, y, zoom):
                            zoom_tiles += 1
                            total_tiles += 1
            
            print(f"   生成了 {zoom_tiles} 个瓦片")
        
        print(f"\n✅ 总共生成了 {total_tiles} 个瓦片")
        return total_tiles > 0
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        R = 6371  # 地球半径
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def create_tile(self, x, y, z):
        """创建单个瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        
        tile_path = tile_dir / f"{y}.png"
        
        # 如果瓦片已存在，跳过
        if tile_path.exists():
            return True
        
        try:
            # 创建占位瓦片
            img = self.create_placeholder_tile(x, y, z)
            img.save(tile_path, 'PNG')
            return True
        except Exception as e:
            print(f"❌ 创建瓦片失败 {z}/{x}/{y}: {e}")
            return False
    
    def check_osm_file(self):
        """检查OSM文件"""
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return False
        
        file_size = self.osm_file.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ OSM文件存在: {self.osm_file}")
        print(f"   文件大小: {file_size:.1f} MB")
        return True
    
    def check_tiles_directory(self):
        """检查瓦片目录"""
        if not self.tiles_dir.exists():
            print(f"📁 创建瓦片目录: {self.tiles_dir}")
            self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计现有瓦片
        tile_count = 0
        if self.tiles_dir.exists():
            for zoom_dir in self.tiles_dir.iterdir():
                if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                    for x_dir in zoom_dir.iterdir():
                        if x_dir.is_dir() and x_dir.name.isdigit():
                            tile_count += len([f for f in x_dir.iterdir() if f.suffix == '.png'])
        
        print(f"📊 现有瓦片数量: {tile_count}")
        return tile_count
    
    def fix_map(self):
        """修复地图"""
        print("🔧 开始修复离线地图...")
        
        # 1. 检查配置文件
        config = self.load_config()
        if not config:
            return False
        
        # 2. 检查OSM文件
        if not self.check_osm_file():
            return False
        
        # 3. 检查瓦片目录
        existing_tiles = self.check_tiles_directory()
        
        # 4. 生成缺失的瓦片
        center_lat = config['center'][1]
        center_lon = config['center'][0]
        radius_km = config.get('radius_km', 50)
        min_zoom = config.get('minZoom', 10)
        max_zoom = config.get('maxZoom', 15)
        
        success = self.generate_tiles_for_area(
            center_lat, center_lon, radius_km, min_zoom, max_zoom
        )
        
        if success:
            print("\n✅ 地图修复完成！")
            print("🚀 现在可以启动地图服务:")
            print("   python tileserver.py")
            return True
        else:
            print("\n❌ 地图修复失败")
            return False

def main():
    """主函数"""
    print("🗺️  离线地图修复工具")
    print("=" * 50)
    
    fixer = OfflineMapFixer()
    success = fixer.fix_map()
    
    if success:
        print("\n🎉 修复成功！请重新加载地图页面查看效果。")
    else:
        print("\n💡 如果问题仍然存在，请检查:")
        print("   1. OSM文件是否完整")
        print("   2. 网络连接是否正常")
        print("   3. 磁盘空间是否充足")

if __name__ == "__main__":
    main()