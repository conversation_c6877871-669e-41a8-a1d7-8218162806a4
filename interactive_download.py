5#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式瓦片下载工具
让用户选择地图源和下载范围
"""

from download_china_18 import ChinaTileDownloader
import math
from pathlib import Path

# 省市级区域配置
PROVINCES = {
    '1': {'name': '北京市', 'center': (39.9042, 116.4074), 'radius': 50},
    '2': {'name': '上海市', 'center': (31.2304, 121.4737), 'radius': 40},
    '3': {'name': '天津市', 'center': (39.3434, 117.3616), 'radius': 30},
    '4': {'name': '重庆市', 'center': (29.4316, 106.9123), 'radius': 60},
    '5': {'name': '广东省', 'center': (23.3417, 113.4244), 'radius': 200},
    '6': {'name': '江苏省', 'center': (32.0603, 118.7969), 'radius': 150},
    '7': {'name': '浙江省', 'center': (30.2741, 120.1551), 'radius': 120},
    '8': {'name': '山东省', 'center': (36.6512, 117.1201), 'radius': 180},
    '9': {'name': '河南省', 'center': (34.7566, 113.6401), 'radius': 160},
    '10': {'name': '四川省', 'center': (30.5728, 104.0668), 'radius': 250},
    '11': {'name': '湖北省', 'center': (30.5928, 114.3055), 'radius': 140},
    '12': {'name': '湖南省', 'center': (28.2278, 112.9388), 'radius': 150},
    '13': {'name': '河北省', 'center': (38.0428, 114.5149), 'radius': 180},
    '14': {'name': '福建省', 'center': (26.0745, 119.2965), 'radius': 120},
    '15': {'name': '安徽省', 'center': (31.8612, 117.2832), 'radius': 130},
    '16': {'name': '辽宁省', 'center': (41.8057, 123.4315), 'radius': 150},
    '17': {'name': '江西省', 'center': (28.6765, 115.8922), 'radius': 120},
    '18': {'name': '黑龙江省', 'center': (45.7732, 126.6617), 'radius': 200},
    '19': {'name': '吉林省', 'center': (43.8868, 125.3245), 'radius': 140},
    '20': {'name': '山西省', 'center': (37.8735, 112.5489), 'radius': 130},
    '21': {'name': '陕西省', 'center': (34.2658, 108.9540), 'radius': 150},
    '22': {'name': '甘肃省', 'center': (36.0611, 103.8343), 'radius': 200},
    '23': {'name': '青海省', 'center': (36.6232, 101.7782), 'radius': 250},
    '24': {'name': '云南省', 'center': (25.0389, 102.7183), 'radius': 200},
    '25': {'name': '贵州省', 'center': (26.5783, 106.7135), 'radius': 150},
    '26': {'name': '海南省', 'center': (20.0174, 110.3312), 'radius': 100},
    '27': {'name': '台湾省', 'center': (25.0330, 121.5654), 'radius': 80},
    '28': {'name': '内蒙古自治区', 'center': (40.8175, 111.7656), 'radius': 300},
    '29': {'name': '新疆维吾尔自治区', 'center': (43.7928, 87.6177), 'radius': 400},
    '30': {'name': '西藏自治区', 'center': (29.6465, 91.1172), 'radius': 350},
    '31': {'name': '宁夏回族自治区', 'center': (38.4872, 106.2782), 'radius': 120},
    '32': {'name': '广西壮族自治区', 'center': (22.8170, 108.3669), 'radius': 180},
    '33': {'name': '香港特别行政区', 'center': (22.3193, 114.1694), 'radius': 20},
    '34': {'name': '澳门特别行政区', 'center': (22.1987, 113.5439), 'radius': 10}
}

CITIES = {
    '1': {'name': '北京市', 'center': (39.9042, 116.4074), 'radius': 30},
    '2': {'name': '上海市', 'center': (31.2304, 121.4737), 'radius': 25},
    '3': {'name': '广州市', 'center': (23.1291, 113.2644), 'radius': 20},
    '4': {'name': '深圳市', 'center': (22.5431, 114.0579), 'radius': 15},
    '5': {'name': '杭州市', 'center': (30.2741, 120.1551), 'radius': 20},
    '6': {'name': '南京市', 'center': (32.0603, 118.7969), 'radius': 20},
    '7': {'name': '成都市', 'center': (30.5728, 104.0668), 'radius': 25},
    '8': {'name': '武汉市', 'center': (30.5928, 114.3055), 'radius': 20},
    '9': {'name': '西安市', 'center': (34.2658, 108.9540), 'radius': 20},
    '10': {'name': '重庆市', 'center': (29.4316, 106.9123), 'radius': 30},
    '11': {'name': '天津市', 'center': (39.3434, 117.3616), 'radius': 20},
    '12': {'name': '青岛市', 'center': (36.0986, 120.3719), 'radius': 15},
    '13': {'name': '大连市', 'center': (38.9140, 121.6147), 'radius': 15},
    '14': {'name': '厦门市', 'center': (24.4798, 118.0819), 'radius': 15},
    '15': {'name': '苏州市', 'center': (31.2989, 120.5853), 'radius': 15},
    '16': {'name': '无锡市', 'center': (31.4912, 120.3124), 'radius': 15},
    '17': {'name': '宁波市', 'center': (29.8683, 121.5440), 'radius': 15},
    '18': {'name': '长沙市', 'center': (28.2278, 112.9388), 'radius': 15},
    '19': {'name': '郑州市', 'center': (34.7566, 113.6401), 'radius': 15},
    '20': {'name': '沈阳市', 'center': (41.8057, 123.4315), 'radius': 15}
}

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🗺️  省市级瓦片下载工具")
    print("="*60)
    print("1. 下载省级瓦片 (百度地图)")
    print("2. 下载省级瓦片 (高德地图)")
    print("3. 下载市级瓦片 (百度地图)")
    print("4. 下载市级瓦片 (高德地图)")
    print("5. 测试瓦片质量")
    print("6. 查看瓦片统计")
    print("0. 退出")
    print("="*60)

def show_provinces():
    """显示省份列表"""
    print("\n📋 请选择省份/直辖市:")
    print("-" * 40)
    
    # 分页显示
    items_per_page = 10
    total_pages = (len(PROVINCES) + items_per_page - 1) // items_per_page
    
    for page in range(total_pages):
        start_idx = page * items_per_page
        end_idx = min(start_idx + items_per_page, len(PROVINCES))
        
        for i in range(start_idx, end_idx):
            key = str(i + 1)
            if key in PROVINCES:
                province = PROVINCES[key]
                print(f"{key:2}. {province['name']}")
        
        if page < total_pages - 1:
            input("\n按回车键继续...")
    
    print("0. 返回主菜单")

def show_cities():
    """显示城市列表"""
    print("\n📋 请选择城市:")
    print("-" * 40)
    
    # 分页显示
    items_per_page = 10
    total_pages = (len(CITIES) + items_per_page - 1) // items_per_page
    
    for page in range(total_pages):
        start_idx = page * items_per_page
        end_idx = min(start_idx + items_per_page, len(CITIES))
        
        for i in range(start_idx, end_idx):
            key = str(i + 1)
            if key in CITIES:
                city = CITIES[key]
                print(f"{key:2}. {city['name']}")
        
        if page < total_pages - 1:
            input("\n按回车键继续...")
    
    print("0. 返回主菜单")

def download_province_tiles(source_name):
    """下载省级瓦片"""
    print(f"\n🚀 下载省级瓦片 ({source_name})")
    
    show_provinces()
    choice = input("\n请选择省份 (输入编号): ").strip()
    
    if choice == '0':
        return
    
    if choice not in PROVINCES:
        print("❌ 无效选择")
        return
    
    province = PROVINCES[choice]
    print(f"\n已选择: {province['name']}")
    print(f"中心坐标: {province['center']}")
    print(f"覆盖半径: {province['radius']} km")
    
    # 设置缩放级别
    try:
        zoom = int(input("缩放级别 (默认: 18): ") or "18")
        if zoom < 10 or zoom > 19:
            print("缩放级别应在10-19之间，使用默认值18")
            zoom = 18
    except ValueError:
        zoom = 18
    
    # 设置参数
    try:
        max_workers = int(input("并发数 (默认: 8): ") or "8")
    except ValueError:
        max_workers = 8
    
    # 计算瓦片数量
    lat, lon = province['center']
    radius = province['radius']
    
    lat_range = radius / 111.0
    lon_range = radius / (111.0 * math.cos(math.radians(lat)))
    
    min_lat = lat - lat_range
    max_lat = lat + lat_range
    min_lon = lon - lon_range
    max_lon = lon + lon_range
    
    downloader = ChinaTileDownloader()
    downloader.set_source(source_name)
    
    min_x, max_y = downloader.deg2num(max_lat, min_lon, zoom)
    max_x, min_y = downloader.deg2num(min_lat, max_lon, zoom)
    
    if min_x > max_x:
        min_x, max_x = max_x, min_x
    if min_y > max_y:
        min_y, max_y = max_y, min_y
    
    total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
    
    print(f"\n下载信息:")
    print(f"  地图源: {source_name}")
    print(f"  缩放级别: {zoom}")
    print(f"  瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
    print(f"  总瓦片数: {total_tiles:,}")
    
    if total_tiles > 50000:
        print(f"  ⚠️  瓦片数量较多，可能需要较长时间")
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消下载")
            return
    
    # 开始下载
    print(f"\n开始下载...")
    success_count = 0
    
    for x in range(min_x, max_x + 1):
        for y in range(min_y, max_y + 1):
            success, message = downloader.download_tile(x, y, zoom)
            if success:
                success_count += 1
            else:
                print(f"❌ {message}")
    
    print(f"\n✅ 下载完成!")
    print(f"   成功: {success_count}/{total_tiles}")
    print(f"   成功率: {success_count/total_tiles*100:.1f}%")

def download_city_tiles(source_name):
    """下载市级瓦片"""
    print(f"\n🚀 下载市级瓦片 ({source_name})")
    
    show_cities()
    choice = input("\n请选择城市 (输入编号): ").strip()
    
    if choice == '0':
        return
    
    if choice not in CITIES:
        print("❌ 无效选择")
        return
    
    city = CITIES[choice]
    print(f"\n已选择: {city['name']}")
    print(f"中心坐标: {city['center']}")
    print(f"覆盖半径: {city['radius']} km")
    
    # 设置缩放级别
    try:
        zoom = int(input("缩放级别 (默认: 18): ") or "18")
        if zoom < 10 or zoom > 19:
            print("缩放级别应在10-19之间，使用默认值18")
            zoom = 18
    except ValueError:
        zoom = 18
    
    # 设置参数
    try:
        max_workers = int(input("并发数 (默认: 8): ") or "8")
    except ValueError:
        max_workers = 8
    
    # 计算瓦片数量
    lat, lon = city['center']
    radius = city['radius']
    
    lat_range = radius / 111.0
    lon_range = radius / (111.0 * math.cos(math.radians(lat)))
    
    min_lat = lat - lat_range
    max_lat = lat + lat_range
    min_lon = lon - lon_range
    max_lon = lon + lon_range
    
    downloader = ChinaTileDownloader()
    downloader.set_source(source_name)
    
    min_x, max_y = downloader.deg2num(max_lat, min_lon, zoom)
    max_x, min_y = downloader.deg2num(min_lat, max_lon, zoom)
    
    if min_x > max_x:
        min_x, max_x = max_x, min_x
    if min_y > max_y:
        min_y, max_y = max_y, min_y
    
    total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
    
    print(f"\n下载信息:")
    print(f"  地图源: {source_name}")
    print(f"  缩放级别: {zoom}")
    print(f"  瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
    print(f"  总瓦片数: {total_tiles:,}")
    
    if total_tiles > 10000:
        print(f"  ⚠️  瓦片数量较多，可能需要一些时间")
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消下载")
            return
    
    # 开始下载
    print(f"\n开始下载...")
    success_count = 0
    
    for x in range(min_x, max_x + 1):
        for y in range(min_y, max_y + 1):
            success, message = downloader.download_tile(x, y, zoom)
            if success:
                success_count += 1
            else:
                print(f"❌ {message}")
    
    print(f"\n✅ 下载完成!")
    print(f"   成功: {success_count}/{total_tiles}")
    print(f"   成功率: {success_count/total_tiles*100:.1f}%")

def download_region_tiles():
    """下载区域瓦片"""
    print("\n🗺️  下载区域瓦片")
    
    try:
        # 获取区域信息
        lat = float(input("中心纬度 (默认: 39.9042 北京): ") or "39.9042")
        lon = float(input("中心经度 (默认: 116.4074 北京): ") or "116.4074")
        radius = float(input("半径(km, 默认: 10): ") or "10")
        zoom = int(input("缩放级别 (默认: 18): ") or "18")
        
        # 选择地图源
        print("\n可用地图源:")
        print("1. OSM")
        print("2. 百度地图")
        source_choice = input("请选择 (默认: 1): ").strip() or "1"
        
        source_name = 'osm' if source_choice == '1' else 'baidu'
        
        downloader = ChinaTileDownloader()
        downloader.set_source(source_name)
        
        # 计算瓦片范围
        lat_range = radius / 111.0
        lon_range = radius / (111.0 * math.cos(math.radians(lat)))
        
        min_lat = lat - lat_range
        max_lat = lat + lat_range
        min_lon = lon - lon_range
        max_lon = lon + lon_range
        
        min_x, max_y = downloader.deg2num(max_lat, min_lon, zoom)
        max_x, min_y = downloader.deg2num(min_lat, max_lon, zoom)
        
        if min_x > max_x:
            min_x, max_x = max_x, min_x
        if min_y > max_y:
            min_y, max_y = max_y, min_y
        
        total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        
        print(f"\n区域信息:")
        print(f"  中心点: ({lat}, {lon})")
        print(f"  半径: {radius} km")
        print(f"  缩放级别: {zoom}")
        print(f"  瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
        print(f"  总瓦片数: {total_tiles:,}")
        
        if total_tiles > 10000:
            print(f"  ⚠️  瓦片数量较多，可能需要一些时间")
            confirm = input("是否继续? (y/N): ").strip().lower()
            if confirm != 'y':
                print("已取消下载")
                return
        
        # 开始下载
        print(f"\n开始下载...")
        success_count = 0
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                success, message = downloader.download_tile(x, y, zoom)
                if success:
                    success_count += 1
                else:
                    print(f"❌ {message}")
        
        print(f"\n✅ 下载完成!")
        print(f"   成功: {success_count}/{total_tiles}")
        print(f"   成功率: {success_count/total_tiles*100:.1f}%")
        
    except ValueError:
        print("❌ 输入格式错误")
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def test_tile_quality():
    """测试瓦片质量"""
    print("\n🔍 测试瓦片质量")
    
    try:
        # 选择地图源
        print("可用地图源:")
        print("1. 百度地图")
        print("2. 高德地图")
        source_choice = input("请选择 (默认: 1): ").strip() or "1"
        
        source_name = 'baidu' if source_choice == '1' else 'amap'
        
        downloader = ChinaTileDownloader()
        downloader.set_source(source_name)
        
        # 测试北京天安门瓦片
        x, y = downloader.deg2num(39.9042, 116.4074, 18)
        print(f"测试瓦片: 18/{x}/{y}")
        
        success, message = downloader.download_tile(x, y, 18)
        if success:
            print(f"✅ {message}")
            
            # 检查文件
            tile_path = downloader.output_dir / "18" / str(x) / f"{y}.png"
            if tile_path.exists():
                file_size = tile_path.stat().st_size
                print(f"   文件大小: {file_size} bytes")
                if file_size > 2000:
                    print("   ✅ 瓦片质量正常")
                else:
                    print("   ⚠️  瓦片可能有问题")
            else:
                print("   ❌ 文件不存在")
        else:
            print(f"❌ {message}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_tile_stats():
    """显示瓦片统计"""
    print("\n📊 瓦片统计信息")
    
    tiles_dir = Path("static/tiles")
    if not tiles_dir.exists():
        print("❌ 瓦片目录不存在")
        return
    
    total_tiles = 0
    zoom_levels = {}
    
    for zoom_dir in tiles_dir.iterdir():
        if zoom_dir.is_dir() and zoom_dir.name.isdigit():
            zoom = int(zoom_dir.name)
            tile_count = 0
            
            for x_dir in zoom_dir.iterdir():
                if x_dir.is_dir():
                    tile_count += len([f for f in x_dir.iterdir() if f.suffix == '.png'])
            
            zoom_levels[zoom] = tile_count
            total_tiles += tile_count
    
    print(f"总瓦片数: {total_tiles:,}")
    print("各缩放级别:")
    for zoom in sorted(zoom_levels.keys()):
        print(f"  {zoom}级: {zoom_levels[zoom]:,} 个瓦片")

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择 (0-6): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            download_province_tiles('baidu')
        elif choice == '2':
            download_province_tiles('amap')
        elif choice == '3':
            download_city_tiles('baidu')
        elif choice == '4':
            download_city_tiles('amap')
        elif choice == '5':
            test_tile_quality()
        elif choice == '6':
            show_tile_stats()
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
