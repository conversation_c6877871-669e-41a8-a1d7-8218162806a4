#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统启动脚本
提供多种启动模式：正常模式、测试模式、演示模式
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖包是否已安装"""
    required_packages = [
        'flask', 'flask-socketio', 'pymodbus', 
        'pynmea2', 'pyserial', 'eventlet'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_map_integrity():
    """检查地图完整性"""
    try:
        from pathlib import Path
        tiles_dir = Path(__file__).parent / "static" / "tiles"
        
        if not tiles_dir.exists():
            print("⚠️  瓦片目录不存在，地图可能无法正常显示")
            return False
        
        # 检查关键缩放级别的瓦片是否存在
        critical_zooms = [8, 10, 11, 12, 15]
        missing_zooms = []
        
        for zoom in critical_zooms:
            zoom_dir = tiles_dir / str(zoom)
            if not zoom_dir.exists() or not any(zoom_dir.iterdir()):
                missing_zooms.append(zoom)
        
        if missing_zooms:
            print(f"⚠️  缺少关键缩放级别的瓦片: {missing_zooms}")
            print("   建议运行: python verify_tile_integrity.py")
            return False
        
        print("✅ 地图完整性检查通过")
        return True
        
    except Exception as e:
        print(f"⚠️  地图完整性检查失败: {e}")
        return False

def check_ports():
    """检查串口配置"""
    from config import Config
    
    print(f"📡 GPS串口配置: {Config.GPS_PORT} @ {Config.GPS_BAUDRATE}")
    print(f"🔌 MODBUS串口配置: {Config.MODBUS_PORT} @ {Config.MODBUS_BAUDRATE}")
    
    # 这里可以添加串口可用性检查
    return True

def start_normal_mode():
    """启动正常模式"""
    print("🚀 启动正常模式...")
    print("系统将尝试连接实际的GPS和PLC设备")
    
    if not check_dependencies():
        return False
    
    if not check_map_integrity():
        print("⚠️  地图完整性检查失败，系统仍将继续启动...")
    
    if not check_ports():
        return False
    
    # 启动主应用
    try:
        import app
        print("✅ 系统启动成功!")
        print("📱 Web界面地址: http://localhost:5000")
        print("按 Ctrl+C 停止系统")
        app.socketio.run(app.app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        return False
    
    return True

def start_test_mode():
    """启动测试模式"""
    print("🧪 启动测试模式...")
    print("系统将使用模拟数据进行测试")
    
    # 替换数据接收器为模拟版本
    import app
    from test_system import MockDataReceiver
    
    # 使用模拟数据接收器
    app.data_receiver = MockDataReceiver()
    
    try:
        app.data_receiver.start()
        print("✅ 测试模式启动成功!")
        print("📱 Web界面地址: http://localhost:5000")
        print("🎭 使用模拟数据，无需实际硬件")
        print("按 Ctrl+C 停止系统")
        
        # 启动数据更新线程
        import threading
        import time
        
        def test_data_update_thread():
            while True:
                try:
                    gps_data = app.data_receiver.get_gps_data()
                    if gps_data:
                        app.current_data['gps'].update(gps_data)
                        app.current_data['gps']['timestamp'] = time.time()
                    
                    plc_data = app.data_receiver.get_plc_data()
                    if plc_data:
                        app.current_data['plc'].update(plc_data)
                        app.current_data['plc']['timestamp'] = time.time()
                    
                    app.socketio.emit('data_update', app.current_data)
                    
                except Exception as e:
                    print(f"测试数据更新错误: {e}")
                
                time.sleep(2)  # 测试模式更新频率稍慢
        
        update_thread = threading.Thread(target=test_data_update_thread, daemon=True)
        update_thread.start()
        
        app.socketio.run(app.app, host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 测试模式已停止")
    except Exception as e:
        print(f"❌ 测试模式启动失败: {e}")
        return False
    
    return True

def start_demo_mode():
    """启动演示模式"""
    print("🎬 启动演示模式...")
    print("系统将运行数据生成测试")
    
    try:
        from test_system import test_data_generation, test_gas_thresholds, test_modbus_simulation
        
        print("\n" + "="*50)
        test_data_generation()
        
        print("\n" + "="*50)
        test_gas_thresholds()
        
        print("\n" + "="*50)
        test_modbus_simulation()
        
        print("\n✅ 演示模式完成!")
        
    except Exception as e:
        print(f"❌ 演示模式失败: {e}")
        return False
    
    return True

def show_system_info():
    """显示系统信息"""
    print("📊 系统信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   系统平台: {sys.platform}")
    
    # 检查文件结构
    required_files = [
        'app.py', 'data_receiver.py', 'config.py',
        'requirements.txt', 'templates/index.html',
        'static/css/style.css', 'static/js/app.js'
    ]
    
    print("\n📁 文件检查:")
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")

def main():
    parser = argparse.ArgumentParser(
        description='野外深机井监控系统启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
启动模式说明:
  normal  - 正常模式，连接实际GPS和PLC设备
  test    - 测试模式，使用模拟数据进行测试
  demo    - 演示模式，运行功能演示和测试
  info    - 显示系统信息和文件检查

示例:
  python start_system.py normal    # 启动正常模式
  python start_system.py test     # 启动测试模式
  python start_system.py demo     # 运行演示
  python start_system.py info     # 查看系统信息
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['normal', 'test', 'demo', 'info'],
        help='启动模式'
    )
    
    args = parser.parse_args()
    
    print("🏭 野外深机井监控系统")
    print("=" * 50)
    
    if args.mode == 'normal':
        return start_normal_mode()
    elif args.mode == 'test':
        return start_test_mode()
    elif args.mode == 'demo':
        return start_demo_mode()
    elif args.mode == 'info':
        show_system_info()
        return True
    
    return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)