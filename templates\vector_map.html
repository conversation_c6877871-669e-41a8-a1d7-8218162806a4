<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矢量地图查看器</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: #f5f5f5;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #ddd;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .control-panel {
            background: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #005a87;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .search-result {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .search-result:hover {
            background: #f8f9fa;
        }
        
        .search-result:last-child {
            border-bottom: none;
        }
        
        .result-name {
            font-weight: bold;
            color: #333;
        }
        
        .result-type {
            color: #666;
            font-size: 0.9em;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .coordinates {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="control-panel">
                <h3>🗺️ 矢量地图查看器</h3>
                
                <div class="form-group">
                    <label for="regionSelect">选择区域:</label>
                    <select id="regionSelect">
                        <option value="">请选择区域</option>
                        {% for region in regions %}
                        <option value="{{ region.name }}">{{ region.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <button class="btn" onclick="loadVectorData()">加载矢量数据</button>
                <button class="btn btn-secondary" onclick="clearMap()">清除地图</button>
            </div>
            
            <div class="control-panel">
                <h3>🔍 搜索定位</h3>
                
                <div class="form-group">
                    <label for="searchInput">搜索地点:</label>
                    <input type="text" id="searchInput" placeholder="输入地点名称，如：天安门">
                </div>
                
                <button class="btn" onclick="searchLocation()">搜索</button>
                <button class="btn btn-secondary" onclick="showFamousPlaces()">著名地标</button>
                
                <div id="searchResults" class="search-results" style="display: none;"></div>
                <div id="famousPlaces" class="search-results" style="display: none;"></div>
            </div>
            
            <div class="control-panel">
                <h3>📍 坐标定位</h3>
                
                <div class="form-group">
                    <label for="latInput">纬度:</label>
                    <input type="number" id="latInput" step="0.000001" placeholder="39.9042">
                </div>
                
                <div class="form-group">
                    <label for="lngInput">经度:</label>
                    <input type="number" id="lngInput" step="0.000001" placeholder="116.4074">
                </div>
                
                <button class="btn" onclick="goToLocation()">定位</button>
                <button class="btn btn-secondary" onclick="getCurrentLocation()">获取当前位置</button>
            </div>
            
            <div class="control-panel">
                <h3>ℹ️ 状态信息</h3>
                <div id="status" class="status info">
                    请选择区域并加载矢量数据
                </div>
                
                <div id="coordinates" class="coordinates" style="display: none;">
                    坐标: <span id="coordText"></span>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 地图配置
        const mapConfig = {{ map_config | tojson }};
        
        // 初始化地图
        const map = L.map('map').setView([mapConfig.center_lat, mapConfig.center_lng], mapConfig.zoom);
        
        // 添加底图
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: mapConfig.max_zoom,
            minZoom: mapConfig.min_zoom
        }).addTo(map);
        
        // 存储矢量数据图层
        let vectorLayers = {
            roads: null,
            buildings: null
        };
        
        // 状态显示
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 显示坐标
        function showCoordinates(lat, lng) {
            const coordDiv = document.getElementById('coordinates');
            const coordText = document.getElementById('coordText');
            coordText.textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            coordDiv.style.display = 'block';
        }
        
        // 加载矢量数据
        async function loadVectorData() {
            const region = document.getElementById('regionSelect').value;
            if (!region) {
                showStatus('请先选择区域', 'error');
                return;
            }
            
            showStatus('正在加载矢量数据...', 'info');
            
            try {
                const response = await fetch(`/api/vector-data/${region}`);
                const data = await response.json();
                
                if (data.error) {
                    showStatus(data.error, 'error');
                    return;
                }
                
                // 清除现有图层
                clearMap();
                
                // 添加道路图层
                if (data.roads && data.roads.length > 0) {
                    const roadLayer = L.layerGroup();
                    data.roads.forEach(road => {
                        if (road.coordinates && road.coordinates.length > 0) {
                            const polyline = L.polyline(road.coordinates, {
                                color: '#3388ff',
                                weight: 2,
                                opacity: 0.8
                            });
                            
                            // 添加道路信息
                            const popup = `
                                <b>${road.name || '未命名道路'}</b><br>
                                类型: ${road.type}<br>
                                坐标数: ${road.coordinates.length}
                            `;
                            polyline.bindPopup(popup);
                            
                            roadLayer.addLayer(polyline);
                        }
                    });
                    roadLayer.addTo(map);
                    vectorLayers.roads = roadLayer;
                }
                
                // 添加建筑物图层
                if (data.buildings && data.buildings.length > 0) {
                    const buildingLayer = L.layerGroup();
                    data.buildings.forEach(building => {
                        if (building.coordinates && building.coordinates.length > 0) {
                            const polygon = L.polygon(building.coordinates, {
                                color: '#ff7800',
                                weight: 1,
                                opacity: 0.8,
                                fillOpacity: 0.3
                            });
                            
                            // 添加建筑物信息
                            const popup = `
                                <b>${building.name || '未命名建筑物'}</b><br>
                                类型: ${building.type}<br>
                                坐标数: ${building.coordinates.length}
                            `;
                            polygon.bindPopup(popup);
                            
                            buildingLayer.addLayer(polygon);
                        }
                    });
                    buildingLayer.addTo(map);
                    vectorLayers.buildings = buildingLayer;
                }
                
                showStatus(`成功加载 ${region} 矢量数据`, 'success');
                
            } catch (error) {
                showStatus(`加载失败: ${error.message}`, 'error');
            }
        }
        
        // 清除地图
        function clearMap() {
            if (vectorLayers.roads) {
                map.removeLayer(vectorLayers.roads);
                vectorLayers.roads = null;
            }
            if (vectorLayers.buildings) {
                map.removeLayer(vectorLayers.buildings);
                vectorLayers.buildings = null;
            }
        }
        
        // 搜索地点
        async function searchLocation() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showStatus('请输入搜索关键词', 'error');
                return;
            }
            
            const region = document.getElementById('regionSelect').value;
            showStatus('正在搜索...', 'info');
            
            try {
                const url = `/api/search?q=${encodeURIComponent(query)}${region ? `&region=${encodeURIComponent(region)}` : ''}`;
                const response = await fetch(url);
                const results = await response.json();
                
                displaySearchResults(results);
                
                if (results.length > 0) {
                    showStatus(`找到 ${results.length} 个结果`, 'success');
                } else {
                    showStatus('未找到匹配结果', 'error');
                }
                
            } catch (error) {
                showStatus(`搜索失败: ${error.message}`, 'error');
            }
        }
        
        // 显示搜索结果
        function displaySearchResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            const famousDiv = document.getElementById('famousPlaces');
            
            // 隐藏著名地标列表
            famousDiv.style.display = 'none';
            
            if (results.length === 0) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            resultsDiv.innerHTML = '';
            results.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'search-result';
                resultDiv.innerHTML = `
                    <div class="result-name">${result.name}</div>
                    <div class="result-type">${result.type} - ${result.region}</div>
                `;
                
                resultDiv.onclick = () => {
                    map.setView([result.lat, result.lon], 16);
                    L.marker([result.lat, result.lon]).addTo(map)
                        .bindPopup(`<b>${result.name}</b><br>类型: ${result.type}<br>城市: ${result.region}`)
                        .openPopup();
                    showCoordinates(result.lat, result.lon);
                };
                
                resultsDiv.appendChild(resultDiv);
            });
            
            resultsDiv.style.display = 'block';
        }
        
        // 显示著名地标
        async function showFamousPlaces() {
            const famousDiv = document.getElementById('famousPlaces');
            const resultsDiv = document.getElementById('searchResults');
            
            // 隐藏搜索结果
            resultsDiv.style.display = 'none';
            
            try {
                const response = await fetch('/api/famous-places');
                const places = await response.json();
                
                famousDiv.innerHTML = '';
                
                // 按城市分组
                const cities = {};
                Object.entries(places).forEach(([name, info]) => {
                    if (!cities[info.city]) {
                        cities[info.city] = [];
                    }
                    cities[info.city].push({name, ...info});
                });
                
                Object.entries(cities).forEach(([city, cityPlaces]) => {
                    const cityDiv = document.createElement('div');
                    cityDiv.innerHTML = `<h4 style="margin: 10px 0 5px 0; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px;">${city}</h4>`;
                    famousDiv.appendChild(cityDiv);
                    
                    cityPlaces.forEach(place => {
                        const placeDiv = document.createElement('div');
                        placeDiv.className = 'search-result';
                        placeDiv.innerHTML = `
                            <div class="result-name">${place.name}</div>
                            <div class="result-type">${place.type}</div>
                        `;
                        
                        placeDiv.onclick = () => {
                            map.setView([place.lat, place.lng], 16);
                            L.marker([place.lat, place.lng]).addTo(map)
                                .bindPopup(`<b>${place.name}</b><br>类型: ${place.type}<br>城市: ${place.city}`)
                                .openPopup();
                            showCoordinates(place.lat, place.lng);
                        };
                        
                        famousDiv.appendChild(placeDiv);
                    });
                });
                
                famousDiv.style.display = 'block';
                
            } catch (error) {
                showStatus(`加载著名地标失败: ${error.message}`, 'error');
            }
        }
        
        // 定位到指定坐标
        function goToLocation() {
            const lat = parseFloat(document.getElementById('latInput').value);
            const lng = parseFloat(document.getElementById('lngInput').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                showStatus('请输入有效的坐标', 'error');
                return;
            }
            
            map.setView([lat, lng], 16);
            L.marker([lat, lng]).addTo(map)
                .bindPopup(`<b>定位点</b><br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
            
            showCoordinates(lat, lng);
            showStatus('已定位到指定坐标', 'success');
        }
        
        // 获取当前位置
        function getCurrentLocation() {
            if (!navigator.geolocation) {
                showStatus('浏览器不支持定位功能', 'error');
                return;
            }
            
            showStatus('正在获取当前位置...', 'info');
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    document.getElementById('latInput').value = lat;
                    document.getElementById('lngInput').value = lng;
                    
                    map.setView([lat, lng], 16);
                    L.marker([lat, lng]).addTo(map)
                        .bindPopup(`<b>当前位置</b><br>纬度: ${lat.toFixed(6)}<br>经度: ${lng.toFixed(6)}`)
                        .openPopup();
                    
                    showCoordinates(lat, lng);
                    showStatus('已获取当前位置', 'success');
                },
                (error) => {
                    showStatus(`获取位置失败: ${error.message}`, 'error');
                }
            );
        }
        
        // 地图点击事件
        map.on('click', (e) => {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            
            document.getElementById('latInput').value = lat;
            document.getElementById('lngInput').value = lng;
            
            showCoordinates(lat, lng);
            showStatus('已选择坐标', 'info');
        });
        
        // 初始化状态
        showStatus('矢量地图查看器已就绪', 'success');
    </script>
</body>
</html>
