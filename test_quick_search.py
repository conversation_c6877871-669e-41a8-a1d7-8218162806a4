#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试快速搜索API
"""

from integrated_vector_system import IntegratedVectorSystem

def test_quick_search():
    """测试快速搜索API"""
    print("🔍 测试快速搜索API")
    print("=" * 50)
    
    # 创建系统实例
    system = IntegratedVectorSystem()
    
    # 模拟请求
    class MockRequest:
        def __init__(self, query):
            self.args = {'q': query}
    
    # 测试快速搜索函数
    def quick_search_test(query):
        query = query.strip()
        if not query:
            return []
        
        results = []
        query_lower = query.lower()
        
        # 快速搜索著名地标
        for name, info in system.famous_places.items():
            if query_lower in name.lower():
                results.append({
                    "name": name,
                    "lat": info["lat"],
                    "lon": info["lon"],
                    "region": info["region"],
                    "type": "famous_place"
                })
        
        # 快速搜索区域
        for region_name, region in system.regions.items():
            if query_lower in region_name.lower():
                results.append({
                    "name": f"{region_name}",
                    "lat": region["center"][1],
                    "lon": region["center"][0],
                    "region": region_name,
                    "type": "region"
                })
        
        return results[:10]
    
    # 测试查询
    test_queries = ["北京", "天安门", "上海", "故宫"]
    
    for query in test_queries:
        print(f"\n📝 测试查询: {query}")
        results = quick_search_test(query)
        print(f"  结果数量: {len(results)}")
        for result in results:
            print(f"    - {result['name']} ({result['type']}) - {result['region']}")

if __name__ == "__main__":
    test_quick_search()
