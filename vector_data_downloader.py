#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矢量数据下载工具
支持下载省市级矢量地图数据
"""

import os
import requests
import json
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class VectorDataDownloader:
    def __init__(self, output_dir="static/vector_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 矢量数据源配置
        self.vector_sources = {
            'osm': {
                'name': 'OpenStreetMap Vector',
                'base_url': 'https://api.openstreetmap.org/api/0.6/map',
                'overpass_url': 'https://overpass-api.de/api/interpreter',
                'format': 'osm'
            },
            'natural_earth': {
                'name': 'Natural Earth',
                'base_url': 'https://www.naturalearthdata.com/http//www.naturalearthdata.com/download',
                'format': 'shp'
            },
            'gadm': {
                'name': 'GADM',
                'base_url': 'https://gadm.org/data.html',
                'format': 'gpkg'
            }
        }
        
        self.current_source = 'osm'
        
    def download_osm_vector_data(self, bbox, output_file):
        """下载OSM矢量数据"""
        print(f"🗺️  下载OSM矢量数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        # 构建Overpass API查询
        min_lon, min_lat, max_lon, max_lat = bbox
        query = f"""
        [out:xml][timeout:300];
        (
          way["highway"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["building"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["waterway"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["natural"]({min_lat},{min_lon},{max_lat},{max_lon});
          relation["boundary"="administrative"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        try:
            response = requests.post(
                self.vector_sources['osm']['overpass_url'],
                data=query,
                timeout=300,
                headers={'User-Agent': 'VectorDataDownloader/1.0'}
            )
            response.raise_for_status()
            
            # 保存数据
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"   ✅ 下载完成: {file_size:,} bytes")
            return True, f"OSM矢量数据下载成功 ({file_size:,} bytes)"
            
        except Exception as e:
            return False, f"OSM矢量数据下载失败: {e}"
    
    def download_china_admin_boundaries(self, output_file):
        """下载中国行政区划边界数据"""
        print(f"🗺️  下载中国行政区划边界数据")
        print(f"   输出文件: {output_file}")
        
        # 使用Overpass API查询中国行政区划
        query = """
        [out:xml][timeout:300];
        (
          relation["ISO3166-1"="CN"]["admin_level"];
          relation["boundary"="administrative"]["admin_level"~"^[2-4]$"](area);
        );
        out geom;
        """
        
        try:
            response = requests.post(
                self.vector_sources['osm']['overpass_url'],
                data=query,
                timeout=300,
                headers={'User-Agent': 'VectorDataDownloader/1.0'}
            )
            response.raise_for_status()
            
            # 保存数据
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"   ✅ 下载完成: {file_size:,} bytes")
            return True, f"中国行政区划数据下载成功 ({file_size:,} bytes)"
            
        except Exception as e:
            return False, f"中国行政区划数据下载失败: {e}"
    
    def download_province_vector_data(self, province_name, bbox):
        """下载省级矢量数据"""
        print(f"🗺️  下载{province_name}矢量数据")
        
        # 创建省份目录
        province_dir = self.output_dir / province_name
        province_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载OSM矢量数据
        osm_file = province_dir / f"{province_name}_osm.xml"
        success, message = self.download_osm_vector_data(bbox, osm_file)
        
        if success:
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
        
        return success
    
    def download_city_vector_data(self, city_name, bbox):
        """下载市级矢量数据"""
        print(f"🗺️  下载{city_name}矢量数据")
        
        # 创建城市目录
        city_dir = self.output_dir / city_name
        city_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载OSM矢量数据
        osm_file = city_dir / f"{city_name}_osm.xml"
        success, message = self.download_osm_vector_data(bbox, osm_file)
        
        if success:
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
        
        return success
    
    def convert_to_geojson(self, osm_file, geojson_file):
        """将OSM数据转换为GeoJSON格式"""
        print(f"🔄 转换OSM数据为GeoJSON格式")
        print(f"   输入: {osm_file}")
        print(f"   输出: {geojson_file}")
        
        try:
            # 使用osmium工具转换
            import subprocess
            
            cmd = [
                'osmium', 'export',
                '--output-format=geojson',
                '--output', str(geojson_file),
                str(osm_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ 转换成功")
                return True, "OSM转GeoJSON成功"
            else:
                print(f"   ❌ 转换失败: {result.stderr}")
                return False, f"转换失败: {result.stderr}"
                
        except FileNotFoundError:
            print(f"   ⚠️  osmium工具未安装，跳过转换")
            return False, "osmium工具未安装"
        except Exception as e:
            print(f"   ❌ 转换失败: {e}")
            return False, f"转换失败: {e}"
    
    def get_province_bbox(self, province_name):
        """获取省份边界框"""
        # 省份边界框配置
        province_bboxes = {
            '北京市': (115.7, 39.4, 117.4, 41.6),
            '上海市': (120.9, 30.7, 122.1, 31.9),
            '天津市': (116.7, 38.6, 118.1, 40.2),
            '重庆市': (105.3, 28.2, 110.2, 32.2),
            '广东省': (109.7, 20.2, 117.2, 25.5),
            '江苏省': (116.2, 30.7, 121.9, 35.2),
            '浙江省': (118.0, 27.0, 123.0, 31.5),
            '山东省': (114.5, 34.4, 122.7, 38.4),
            '河南省': (110.4, 31.2, 116.7, 36.4),
            '四川省': (97.3, 26.0, 108.5, 34.3),
            '湖北省': (108.3, 29.0, 116.1, 33.3),
            '湖南省': (108.8, 24.6, 114.3, 30.1),
            '河北省': (113.5, 36.0, 119.8, 42.6),
            '福建省': (115.8, 23.5, 120.4, 28.3),
            '安徽省': (114.9, 29.4, 119.3, 34.7),
            '辽宁省': (118.3, 38.7, 125.3, 43.3),
            '江西省': (113.6, 24.5, 118.5, 30.0),
            '黑龙江省': (121.1, 43.4, 135.1, 53.6),
            '吉林省': (121.6, 40.9, 131.2, 46.3),
            '山西省': (110.2, 34.6, 114.6, 40.7),
            '陕西省': (105.5, 31.7, 111.3, 39.6),
            '甘肃省': (92.1, 32.1, 108.7, 42.8),
            '青海省': (89.4, 31.6, 103.1, 39.2),
            '云南省': (97.5, 21.1, 106.2, 29.2),
            '贵州省': (103.6, 24.6, 109.6, 29.2),
            '海南省': (108.6, 18.2, 111.1, 20.1),
            '台湾省': (119.3, 21.9, 122.0, 25.3),
            '内蒙古自治区': (97.2, 37.2, 126.0, 53.3),
            '新疆维吾尔自治区': (73.4, 34.3, 96.4, 48.9),
            '西藏自治区': (78.4, 27.4, 99.1, 36.5),
            '宁夏回族自治区': (104.2, 35.2, 107.6, 39.4),
            '广西壮族自治区': (104.3, 20.9, 112.0, 26.2),
            '香港特别行政区': (113.8, 22.2, 114.4, 22.6),
            '澳门特别行政区': (113.5, 22.1, 113.6, 22.2)
        }
        
        return province_bboxes.get(province_name, None)
    
    def get_city_bbox(self, city_name):
        """获取城市边界框"""
        # 城市边界框配置
        city_bboxes = {
            '北京市': (116.0, 39.4, 117.0, 40.2),
            '上海市': (121.2, 31.0, 121.8, 31.4),
            '广州市': (113.0, 22.7, 113.6, 23.4),
            '深圳市': (113.7, 22.4, 114.4, 22.8),
            '杭州市': (119.8, 30.0, 120.5, 30.5),
            '南京市': (118.4, 31.8, 119.2, 32.3),
            '成都市': (103.8, 30.4, 104.3, 30.8),
            '武汉市': (114.0, 30.4, 114.6, 30.8),
            '西安市': (108.7, 34.0, 109.2, 34.4),
            '重庆市': (106.2, 29.2, 107.0, 29.8),
            '天津市': (117.0, 38.8, 117.8, 39.4),
            '青岛市': (120.1, 35.9, 120.6, 36.3),
            '大连市': (121.3, 38.7, 121.9, 39.1),
            '厦门市': (117.9, 24.4, 118.3, 24.6),
            '苏州市': (120.4, 31.1, 121.0, 31.5),
            '无锡市': (120.1, 31.4, 120.6, 31.7),
            '宁波市': (121.3, 29.7, 121.9, 30.0),
            '长沙市': (112.7, 28.0, 113.2, 28.4),
            '郑州市': (113.4, 34.6, 114.0, 34.9),
            '沈阳市': (123.2, 41.6, 123.8, 42.0)
        }
        
        return city_bboxes.get(city_name, None)

def main():
    """主函数"""
    print("🗺️  矢量数据下载工具")
    print("=" * 50)
    
    downloader = VectorDataDownloader()
    
    print("可用功能:")
    print("1. 下载省级矢量数据")
    print("2. 下载市级矢量数据")
    print("3. 下载中国行政区划边界")
    print("4. 测试矢量数据下载")
    
    choice = input("\n请选择功能 (1-4): ").strip()
    
    if choice == '1':
        # 下载省级矢量数据
        province_name = input("请输入省份名称 (如: 北京市): ").strip()
        bbox = downloader.get_province_bbox(province_name)
        
        if bbox:
            downloader.download_province_vector_data(province_name, bbox)
        else:
            print(f"❌ 未找到省份: {province_name}")
    
    elif choice == '2':
        # 下载市级矢量数据
        city_name = input("请输入城市名称 (如: 北京市): ").strip()
        bbox = downloader.get_city_bbox(city_name)
        
        if bbox:
            downloader.download_city_vector_data(city_name, bbox)
        else:
            print(f"❌ 未找到城市: {city_name}")
    
    elif choice == '3':
        # 下载中国行政区划边界
        output_file = downloader.output_dir / "china_admin_boundaries.xml"
        downloader.download_china_admin_boundaries(output_file)
    
    elif choice == '4':
        # 测试矢量数据下载
        print("🧪 测试矢量数据下载")
        test_bbox = (116.0, 39.4, 117.0, 40.2)  # 北京市
        test_file = downloader.output_dir / "test_beijing.xml"
        success, message = downloader.download_osm_vector_data(test_bbox, test_file)
        print(f"测试结果: {message}")
    
    else:
        print("❌ 无效选择")
    
    print(f"\n✅ 完成! 数据保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
