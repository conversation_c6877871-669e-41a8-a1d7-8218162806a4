#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试搜索问题
"""

from integrated_vector_system import IntegratedVectorSystem

def debug_search_issue():
    """调试搜索问题"""
    print("🔍 调试搜索问题")
    print("=" * 50)
    
    # 创建系统实例
    system = IntegratedVectorSystem()
    
    print(f"📊 系统状态:")
    print(f"  - 区域数: {len(system.regions)}")
    print(f"  - 著名地标数: {len(system.famous_places)}")
    
    # 检查四川区域
    print(f"\n🔍 检查四川区域:")
    if "四川" in system.regions:
        region = system.regions["四川"]
        print(f"  ✅ 四川区域存在")
        print(f"  - 状态: {region['status']}")
        print(f"  - 中心坐标: {region['center']}")
        print(f"  - 边界: {region['bbox']}")
    else:
        print(f"  ❌ 四川区域不存在")
        print(f"  - 可用区域: {list(system.regions.keys())[:10]}...")
    
    # 测试搜索功能
    print(f"\n🔍 测试搜索功能:")
    test_queries = ["四川", "北京", "天安门", "九寨沟"]
    
    for query in test_queries:
        print(f"\n  测试查询: '{query}'")
        
        # 测试区域搜索
        query_lower = query.lower()
        region_found = False
        for region_name, region in system.regions.items():
            if query_lower in region_name.lower():
                print(f"    ✅ 区域匹配: {region_name}")
                region_found = True
                break
        
        if not region_found:
            print(f"    ❌ 无区域匹配")
        
        # 测试著名地标搜索
        famous_found = False
        for name, info in system.famous_places.items():
            if query_lower in name.lower():
                print(f"    ✅ 著名地标匹配: {name}")
                famous_found = True
                break
        
        if not famous_found:
            print(f"    ❌ 无著名地标匹配")
        
        # 测试完整搜索
        results = system.search_poi(query)
        print(f"    📊 搜索结果: {len(results)} 个")
        for result in results[:3]:  # 显示前3个
            print(f"      - {result['name']} ({result['type']}) - {result['region']}")

if __name__ == "__main__":
    debug_search_issue()
