<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超高精度监控系统 - 20米精度</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 15px 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .precision-badge {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .control-panel {
            padding: 20px;
        }
        
        .control-group {
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .control-group h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .control-group h3::before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-size: 14px;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status {
            padding: 12px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .region-list {
            max-height: 200px;
            overflow-y: auto;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .region-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .region-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .region-item:last-child {
            border-bottom: none;
        }
        
        .region-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .region-files {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .legend h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 20px;
            height: 4px;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        .legend-text {
            font-size: 12px;
            color: #666;
        }
        
        .precision-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .precision-info h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        
        .precision-info .precision-value {
            font-size: 18px;
            font-weight: 700;
            color: #ff6b6b;
        }
        
        .well-popup {
            font-size: 12px;
        }
        
        .well-popup h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        
        .well-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .well-status.normal {
            background: #d4edda;
            color: #155724;
        }
        
        .well-status.maintenance {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 超高精度监控系统 <span class="precision-badge">20米精度</span></h1>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="control-panel">
                <div class="control-group">
                    <h3>📍 GPS定位</h3>
                    <div class="form-group">
                        <label>纬度 (Latitude)</label>
                        <input type="number" id="latitude" step="0.000001" placeholder="30.295000" value="30.295000">
                    </div>
                    <div class="form-group">
                        <label>经度 (Longitude)</label>
                        <input type="number" id="longitude" step="0.000001" placeholder="109.486000" value="109.486000">
                    </div>
                    <button class="btn" onclick="locateByGPS()">🎯 20米精度定位</button>
                </div>
                
                <div class="control-group">
                    <h3>🗺️ 区域选择</h3>
                    <div id="region-list" class="region-list">
                        <div class="status info">正在加载区域列表...</div>
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>🎛️ 图层控制</h3>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-roads" checked> 显示道路
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-buildings" checked> 显示建筑
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-pois" checked> 显示POI
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="show-wells" checked> 显示机井
                        </label>
                    </div>
                </div>
                
                <div id="status" class="status info">
                    系统就绪，支持20米精度定位
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            
            <div class="precision-info">
                <h4>定位精度</h4>
                <div class="precision-value">±20米</div>
                <div style="font-size: 12px; color: #666;">6位小数坐标</div>
            </div>
            
            <div class="legend">
                <h4>图例</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff0000;"></div>
                    <div class="legend-text">主要道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff8800;"></div>
                    <div class="legend-text">次要道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff00;"></div>
                    <div class="legend-text">住宅道路</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ffff00;"></div>
                    <div class="legend-text">建筑</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #0066ff;"></div>
                    <div class="legend-text">POI</div>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff00ff;"></div>
                    <div class="legend-text">机井</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let vectorLayers = [];
        let currentRegion = null;
        let gpsMarker = null;
        
        // 初始化地图
        function initMap() {
            map = L.map('map', {
                zoomControl: true,
                attributionControl: true
            }).setView([30.295, 109.486], 16);
            
            // 添加在线地图图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);
            
            // 加载区域列表
            loadRegions();
        }
        
        // 加载区域列表
        async function loadRegions() {
            try {
                const response = await fetch('/api/regions');
                const regions = await response.json();
                
                const regionList = document.getElementById('region-list');
                regionList.innerHTML = '';
                
                if (regions.length === 0) {
                    regionList.innerHTML = '<div class="status error">未找到矢量数据</div>';
                    return;
                }
                
                regions.forEach(region => {
                    const regionItem = document.createElement('div');
                    regionItem.className = 'region-item';
                    regionItem.innerHTML = `
                        <div class="region-name">${region.name}</div>
                        <div class="region-files">${region.files.join(', ')}</div>
                    `;
                    regionItem.onclick = () => loadRegionData(region.name);
                    regionList.appendChild(regionItem);
                });
                
                updateStatus(`找到 ${regions.length} 个区域，支持20米精度`, 'success');
                
            } catch (error) {
                console.error('加载区域列表失败:', error);
                updateStatus('加载区域列表失败', 'error');
            }
        }
        
        // 加载区域数据
        async function loadRegionData(regionName) {
            try {
                updateStatus(`正在加载 ${regionName} 超高精度数据...`, 'info');
                
                // 清除之前的矢量图层
                clearVectorLayers();
                
                const response = await fetch(`/api/ultra-precision-data/${regionName}`);
                const data = await response.json();
                
                if (data.error) {
                    updateStatus(`加载失败: ${data.error}`, 'error');
                    return;
                }
                
                currentRegion = regionName;
                
                // 渲染超高精度矢量数据
                renderUltraPrecisionData(data);
                
                updateStatus(`${regionName} 超高精度数据加载完成: ${data.metadata.total_features}个要素，精度±${data.metadata.accuracy_meters}米`, 'success');
                
            } catch (error) {
                console.error('加载区域数据失败:', error);
                updateStatus('加载区域数据失败', 'error');
            }
        }
        
        // 渲染超高精度矢量数据
        function renderUltraPrecisionData(data) {
            // 渲染道路
            if (data.roads && data.roads.features && document.getElementById('show-roads').checked) {
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#666';
                        let weight = 2;
                        
                        switch(highway) {
                            case 'primary':
                                color = '#ff0000';
                                weight = 6;
                                break;
                            case 'secondary':
                                color = '#ff8800';
                                weight = 4;
                                break;
                            case 'residential':
                                color = '#00ff00';
                                weight = 3;
                                break;
                            default:
                                color = '#0000ff';
                                weight = 2;
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 0.9
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway || '未知'}<br>
                                精度: ${props.precision || '20米'}<br>
                                坐标精度: ${props.coordinate_precision || 6}位小数
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(roadsLayer);
            }
            
            // 渲染建筑
            if (data.buildings && data.buildings.features && document.getElementById('show-buildings').checked) {
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: {
                        color: '#ff0000',
                        weight: 3,
                        fillColor: '#ffff00',
                        fillOpacity: 0.7
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building || '未知'}<br>
                                精度: ${props.precision || '20米'}<br>
                                坐标精度: ${props.coordinate_precision || 6}位小数
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(buildingsLayer);
            }
            
            // 渲染POI
            if (data.pois && data.pois.features && document.getElementById('show-pois').checked) {
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 8,
                            fillColor: '#0066ff',
                            color: '#ffffff',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.amenity || props.shop || props.tourism || '未知'}<br>
                                精度: ${props.precision || '20米'}<br>
                                坐标精度: ${props.coordinate_precision || 6}位小数
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(poisLayer);
            }
            
            // 渲染机井
            if (data.wells && data.wells.features && document.getElementById('show-wells').checked) {
                const wellsLayer = L.geoJSON(data.wells, {
                    pointToLayer: function(feature, latlng) {
                        const status = feature.properties.status;
                        const color = status === 'normal' ? '#00ff00' : '#ff8800';
                        
                        return L.circleMarker(latlng, {
                            radius: 10,
                            fillColor: color,
                            color: '#ffffff',
                            weight: 3,
                            opacity: 1,
                            fillOpacity: 0.9
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        const statusClass = props.status === 'normal' ? 'normal' : 'maintenance';
                        layer.bindPopup(`
                            <div class="well-popup">
                                <h4>${props.name}</h4>
                                <div><strong>状态:</strong> <span class="well-status ${statusClass}">${props.status === 'normal' ? '正常' : '维护中'}</span></div>
                                <div><strong>气体浓度:</strong> ${props.gas_level}%</div>
                                <div><strong>送风机:</strong> ${props.fan_status === 'on' ? '开启' : '关闭'}</div>
                                <div><strong>最后更新:</strong> ${new Date(props.last_update).toLocaleString()}</div>
                                <div><strong>定位精度:</strong> ${props.precision}</div>
                                <div><strong>坐标精度:</strong> ${props.coordinate_precision}位小数</div>
                            </div>
                        `);
                    }
                }).addTo(map);
                vectorLayers.push(wellsLayer);
            }
            
            // 调整地图视图到数据范围
            if (vectorLayers.length > 0) {
                const group = new L.featureGroup(vectorLayers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
        }
        
        // 清除矢量图层
        function clearVectorLayers() {
            vectorLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            vectorLayers = [];
        }
        
        // GPS定位
        function locateByGPS() {
            const lat = parseFloat(document.getElementById('latitude').value);
            const lon = parseFloat(document.getElementById('longitude').value);
            
            if (isNaN(lat) || isNaN(lon)) {
                updateStatus('请输入有效的GPS坐标', 'error');
                return;
            }
            
            if (lat < -90 || lat > 90 || lon < -180 || lon > 180) {
                updateStatus('GPS坐标超出有效范围', 'error');
                return;
            }
            
            // 定位到指定坐标
            map.setView([lat, lon], 18); // 使用18级缩放以获得最高精度
            
            // 移除之前的GPS标记
            if (gpsMarker) {
                map.removeLayer(gpsMarker);
            }
            
            // 添加GPS标记
            gpsMarker = L.marker([lat, lon], {
                icon: L.divIcon({
                    className: 'gps-marker',
                    html: '<div style="background: #ff0000; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 10px rgba(0,0,0,0.5);"></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                })
            }).addTo(map);
            
            gpsMarker.bindPopup(`
                <div style="font-size: 12px;">
                    <strong>🎯 GPS定位点</strong><br>
                    纬度: ${lat.toFixed(6)}<br>
                    经度: ${lon.toFixed(6)}<br>
                    <strong>精度: ±20米</strong><br>
                    坐标精度: 6位小数
                </div>
            `).openPopup();
            
            updateStatus(`已定位到: ${lat.toFixed(6)}, ${lon.toFixed(6)}，精度±20米`, 'success');
        }
        
        // 更新状态
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 图层控制事件
        document.getElementById('show-roads').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        document.getElementById('show-buildings').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        document.getElementById('show-pois').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        document.getElementById('show-wells').addEventListener('change', function() {
            if (currentRegion) {
                loadRegionData(currentRegion);
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
    </script>
</body>
</html>
