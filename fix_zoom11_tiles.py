#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复11级瓦片覆盖范围不足的问题
"""

import requests
import os
import math
import time
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def download_tile(base_url, z, x, y, tiles_dir):
    """下载单个瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    if tile_file.exists():
        return True
    
    try:
        url = f"{base_url}/{z}/{x}/{y}.png"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, timeout=15, headers=headers)
        if response.status_code == 200:
            with open(tile_file, 'wb') as f:
                f.write(response.content)
            print(f"  ✅ 下载成功: {z}/{x}/{y}.png")
            return True
        else:
            print(f"  ❌ 下载失败: {z}/{x}/{y}.png (HTTP {response.status_code})")
            return False
    except Exception as e:
        print(f"  ❌ 下载失败: {z}/{x}/{y}.png ({e})")
        return False

def fix_zoom11_coverage():
    """修复11级瓦片覆盖范围"""
    print("🔧 开始修复11级瓦片覆盖范围...")
    
    # 北京坐标
    center_lat, center_lng = 39.9042, 116.4074
    zoom = 11
    
    # 计算中心瓦片坐标
    center_x, center_y = deg2num(center_lat, center_lng, zoom)
    print(f"📍 中心瓦片坐标: {center_x}, {center_y}")
    
    # 扩大覆盖范围 - 从中心向外扩展更多瓦片
    # 对于50公里半径，11级大约需要 ±15 个瓦片
    tile_range = 15
    
    tiles_dir = Path("static/tiles")
    base_url = "https://tile.openstreetmap.org"
    
    total_tiles = (2 * tile_range + 1) ** 2
    downloaded = 0
    failed = 0
    
    print(f"📦 计划下载 {total_tiles} 个瓦片...")
    
    for dx in range(-tile_range, tile_range + 1):
        for dy in range(-tile_range, tile_range + 1):
            x = center_x + dx
            y = center_y + dy
            
            if download_tile(base_url, zoom, x, y, tiles_dir):
                downloaded += 1
            else:
                failed += 1
            
            # 避免请求过快
            time.sleep(0.1)
            
            # 进度显示
            if (downloaded + failed) % 50 == 0:
                print(f"📊 进度: {downloaded + failed}/{total_tiles} ({downloaded} 成功, {failed} 失败)")
    
    print(f"\n✅ 修复完成!")
    print(f"📊 总计: {total_tiles} 个瓦片")
    print(f"✅ 成功: {downloaded} 个")
    print(f"❌ 失败: {failed} 个")
    print(f"📈 成功率: {downloaded/total_tiles*100:.1f}%")

if __name__ == "__main__":
    fix_zoom11_coverage()