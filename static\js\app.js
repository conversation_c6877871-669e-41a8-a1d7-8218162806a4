class MonitoringSystem {
    constructor() {
        this.map = null;
        this.marker = null;
        this.socket = null;
        this.isConnected = false;
        
        this.initializeMap();
        this.initializeWebSocket();
        this.setupEventListeners();
    }

    initializeMap() {
        // 初始化离线地图，明确设置最大缩放级别
        this.map = L.map('map', {
            minZoom: 8,
            maxZoom: 18,
            zoomControl: true
        }).setView([39.9042, 116.4074], 15);

        // 离线瓦片图层 (优先级1)
        const offlineTileLayer = L.tileLayer('/static/tiles/{z}/{x}/{y}.png', {
            attribution: '© OpenMapTiles © OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 8,
            errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });

        // 本地瓦片服务器图层 (优先级2)
        const tileServerLayer = L.tileLayer('http://localhost:8080/tiles/{z}/{x}/{y}.png', {
            attribution: '© OpenMapTiles © OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 8
        });

        // 在线备用图层 (优先级3，仅用于开发测试)
        const onlineBackupLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18,
            minZoom: 8
        });

        // 瓦片加载失败计数
        let offlineErrorCount = 0;
        let serverErrorCount = 0;
        let currentLayer = 'offline';
        let totalTileRequests = 0;
        let successfulTileLoads = 0;

        // 离线瓦片错误处理
        offlineTileLayer.on('tileerror', (e) => {
            offlineErrorCount++;
            totalTileRequests++;
            console.warn('离线瓦片加载失败:', e);
            
            // 记录失败的瓦片信息
            console.warn(`瓦片加载失败: ${e.tile.src}`);
            
            // 如果失败率超过50%且已请求超过10个瓦片，则切换到瓦片服务器
            if (totalTileRequests > 10 && (offlineErrorCount / totalTileRequests) > 0.5 && currentLayer === 'offline') {
                console.warn('离线瓦片加载失败率过高，切换到瓦片服务器');
                this.map.removeLayer(offlineTileLayer);
                tileServerLayer.addTo(this.map);
                currentLayer = 'server';
            }
        });

        // 瓦片服务器错误处理
        tileServerLayer.on('tileerror', (e) => {
            serverErrorCount++;
            console.warn('瓦片服务器加载失败:', e);
            if (serverErrorCount > 5 && currentLayer === 'server') {
                console.warn('瓦片服务器连接失败，切换到在线备用地图');
                this.map.removeLayer(tileServerLayer);
                onlineBackupLayer.addTo(this.map);
                currentLayer = 'online';
            }
        });

        // 瓦片加载成功处理
        offlineTileLayer.on('tileload', () => {
            successfulTileLoads++;
            totalTileRequests++;
            console.log('离线瓦片加载成功');
            
            // 如果成功加载的瓦片数量超过失败的数量，保持当前图层
            if (successfulTileLoads > offlineErrorCount) {
                console.log(`瓦片加载状态: 成功${successfulTileLoads}, 失败${offlineErrorCount}`);
            }
        });

        // 默认使用离线瓦片
        offlineTileLayer.addTo(this.map);

        // 添加图层控制器
        const layerControl = L.control.layers({
            '离线地图': offlineTileLayer,
            '瓦片服务器': tileServerLayer,
            '在线地图': onlineBackupLayer
        }, {}, {
            position: 'topright',
            collapsed: true
        });
        layerControl.addTo(this.map);

        // 创建自定义图标
        this.wellIcon = L.divIcon({
            className: 'well-marker',
            html: '<div class="marker-pin"></div><div class="marker-label">深机井</div>',
            iconSize: [30, 40],
            iconAnchor: [15, 40]
        });

        // 添加CSS样式到头部
        const style = document.createElement('style');
        style.textContent = `
            .well-marker {
                position: relative;
            }
            .marker-pin {
                width: 20px;
                height: 20px;
                background: #dc3545;
                border: 3px solid white;
                border-radius: 50%;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            .marker-label {
                position: absolute;
                top: -30px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 12px;
                white-space: nowrap;
            }
        `;
        document.head.appendChild(style);
    }

    initializeWebSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.updateConnectionStatus('已连接', 'connected');
        });

        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.isConnected = false;
            this.updateConnectionStatus('连接断开', 'disconnected');
        });

        this.socket.on('data_update', (data) => {
            this.updateDisplay(data);
        });

        this.socket.on('connect_error', (error) => {
            console.error('WebSocket连接错误:', error);
            this.updateConnectionStatus('连接错误', 'disconnected');
        });
    }

    updateConnectionStatus(status, className) {
        const statusElement = document.getElementById('connection-status');
        statusElement.textContent = `连接状态: ${status}`;
        statusElement.className = `status-indicator ${className}`;
    }

    updateDisplay(data) {
        // 更新最后更新时间
        const now = new Date();
        document.getElementById('last-update').textContent = 
            `最后更新: ${now.toLocaleTimeString()}`;

        // 更新GPS信息
        if (data.gps) {
            this.updateGPSDisplay(data.gps);
        }

        // 更新PLC数据
        if (data.plc) {
            this.updatePLCDisplay(data.plc);
        }
    }

    updateGPSDisplay(gpsData) {
        if (gpsData.lat && gpsData.lng) {
            // 更新GPS坐标显示
            document.getElementById('current-lat').textContent = gpsData.lat.toFixed(6);
            document.getElementById('current-lng').textContent = gpsData.lng.toFixed(6);
            document.getElementById('current-alt').textContent = gpsData.altitude || 0;

            // 更新地图位置
            const newLatLng = [gpsData.lat, gpsData.lng];
            
            if (this.marker) {
                this.marker.setLatLng(newLatLng);
            } else {
                this.marker = L.marker(newLatLng, { icon: this.wellIcon }).addTo(this.map);
                this.map.setView(newLatLng, 15);
            }

            // 平滑移动地图视图
            this.map.panTo(newLatLng);
        }
    }

    updatePLCDisplay(plcData) {
        // 更新气体浓度
        if (plcData.gas_levels) {
            this.updateGasLevels(plcData.gas_levels);
        }

        // 更新风机状态
        if (plcData.fan_status) {
            this.updateFanStatus(plcData.fan_status);
        }

        // 更新环境数据
        if (plcData.temperature !== undefined) {
            document.getElementById('temperature-value').textContent = `${plcData.temperature}°C`;
        }
        if (plcData.humidity !== undefined) {
            document.getElementById('humidity-value').textContent = `${plcData.humidity}%`;
        }
    }

    updateGasLevels(gasLevels) {
        const gasThresholds = {
            CO: { warning: 25, danger: 50 },      // ppm
            H2S: { warning: 10, danger: 20 },     // ppm
            CH4: { warning: 10, danger: 25 },     // %LEL
            O2: { warning: 19, danger: 16 }       // % (低氧)
        };

        Object.entries(gasLevels).forEach(([gas, value]) => {
            const gasItem = document.getElementById(`${gas.toLowerCase()}-value`).parentElement;
            const valueElement = document.getElementById(`${gas.toLowerCase()}-value`);
            const statusElement = document.getElementById(`${gas.toLowerCase()}-status`);

            // 更新数值
            let unit = 'ppm';
            if (gas === 'CH4') unit = '%LEL';
            if (gas === 'O2') unit = '%';
            valueElement.textContent = `${value.toFixed(1)} ${unit}`;

            // 更新状态和样式
            let status = '正常';
            let statusClass = '';
            let itemClass = '';

            const threshold = gasThresholds[gas];
            if (threshold) {
                if (gas === 'O2') {
                    // 氧气浓度判断（低氧危险）
                    if (value <= threshold.danger) {
                        status = '危险';
                        statusClass = 'danger';
                        itemClass = 'danger';
                    } else if (value <= threshold.warning) {
                        status = '警告';
                        statusClass = 'warning';
                        itemClass = 'warning';
                    }
                } else {
                    // 其他气体浓度判断（高浓度危险）
                    if (value >= threshold.danger) {
                        status = '危险';
                        statusClass = 'danger';
                        itemClass = 'danger';
                    } else if (value >= threshold.warning) {
                        status = '警告';
                        statusClass = 'warning';
                        itemClass = 'warning';
                    }
                }
            }

            statusElement.textContent = status;
            statusElement.className = `gas-status ${statusClass}`;
            gasItem.className = `gas-item ${itemClass}`;
        });
    }

    updateFanStatus(fanStatus) {
        Object.entries(fanStatus).forEach(([fan, isRunning]) => {
            const statusElement = document.getElementById(`${fan}-status`);
            const indicatorElement = document.getElementById(`${fan}-indicator`);

            if (isRunning) {
                statusElement.textContent = '运行中';
                statusElement.className = 'equipment-status running';
                indicatorElement.className = 'status-indicator running';
            } else {
                statusElement.textContent = '停止';
                statusElement.className = 'equipment-status stopped';
                indicatorElement.className = 'status-indicator';
            }
        });
    }

    setupEventListeners() {
        // 窗口大小改变时重新调整地图
        window.addEventListener('resize', () => {
            if (this.map) {
                setTimeout(() => {
                    this.map.invalidateSize();
                }, 100);
            }
        });

        // 页面卸载时断开WebSocket连接
        window.addEventListener('beforeunload', () => {
            if (this.socket) {
                this.socket.disconnect();
            }
        });
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    new MonitoringSystem();
});