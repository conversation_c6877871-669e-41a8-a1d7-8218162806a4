#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSM集成验证脚本
"""

import os
import json
from pathlib import Path

def verify_integration():
    """验证OSM集成结果"""
    base_dir = Path(__file__).parent
    
    print("🔍 验证OpenMapTiles集成结果...")
    print("="*50)
    
    # 检查文件
    files_to_check = [
        "china-latest.osm.pbf",
        "openmaptiles_cn_integration.py",
        "simple_osm_tile_generator.py",
        "map_config.json",
        "tileserver.py"
    ]
    
    print("📁 检查必要文件:")
    for file_name in files_to_check:
        file_path = base_dir / file_name
        if file_path.exists():
            if file_name == "china-latest.osm.pbf":
                size_mb = file_path.stat().st_size / (1024*1024)
                print(f"   ✅ {file_name} ({size_mb:.1f} MB)")
            else:
                print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} (缺失)")
    
    # 检查瓦片目录
    tiles_dir = base_dir / "static" / "tiles"
    print(f"\n🗺️ 检查瓦片目录: {tiles_dir}")
    
    if tiles_dir.exists():
        zoom_levels = []
        total_tiles = 0
        
        for zoom_dir in tiles_dir.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom = int(zoom_dir.name)
                tile_count = 0
                
                for x_dir in zoom_dir.iterdir():
                    if x_dir.is_dir():
                        tile_count += len([f for f in x_dir.iterdir() 
                                         if f.suffix == '.png'])
                
                zoom_levels.append((zoom, tile_count))
                total_tiles += tile_count
        
        zoom_levels.sort()
        
        print(f"   📊 总瓦片数: {total_tiles}")
        print("   📋 缩放级别分布:")
        for zoom, count in zoom_levels:
            print(f"      级别 {zoom}: {count} 个瓦片")
    else:
        print("   ❌ 瓦片目录不存在")
    
    # 检查配置文件
    config_file = base_dir / "map_config.json"
    print(f"\n⚙️ 检查配置文件: {config_file}")
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"   ✅ 配置加载成功")
            print(f"   📍 地图名称: {config.get('name', 'N/A')}")
            print(f"   🎯 中心点: {config.get('center', 'N/A')}")
            print(f"   📏 缩放范围: {config.get('minzoom', 'N/A')}-{config.get('maxzoom', 'N/A')}")
            
            if 'data_source' in config:
                ds = config['data_source']
                print(f"   📁 数据源: {ds.get('osm_file', 'N/A')}")
                print(f"   📊 文件大小: {ds.get('file_size_mb', 'N/A')} MB")
        
        except Exception as e:
            print(f"   ❌ 配置文件解析失败: {e}")
    else:
        print("   ❌ 配置文件不存在")
    
    print("\n" + "="*50)
    print("🎯 验证完成!")
    print("\n📋 下一步操作:")
    print("1. 启动瓦片服务器: python tileserver.py")
    print("2. 访问 http://localhost:8080 查看瓦片")
    print("3. 启动主应用: python app.py")
    print("4. 访问 http://localhost:5000 查看完整系统")

if __name__ == "__main__":
    verify_integration()