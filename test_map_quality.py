#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试地图质量改进效果
"""

import requests
import json

def test_map_quality():
    """测试地图质量改进"""
    base_url = "http://127.0.0.1:5000"
    
    print("🗺️ 测试地图质量改进效果")
    print("=" * 50)
    
    try:
        # 测试系统状态
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 系统状态正常:")
            print(f"  - 总区域数: {stats['total_regions']}")
            print(f"  - 完整区域: {stats['complete_regions']}")
            print(f"  - 完成率: {stats['completion_rate']}%")
            print(f"  - 著名地标: {stats['famous_places_count']}")
        
        # 测试搜索功能
        test_queries = ["北京", "天安门", "上海", "故宫"]
        print(f"\n🔍 测试搜索功能:")
        for query in test_queries:
            response = requests.get(f"{base_url}/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"  ✅ '{query}': 找到 {len(results)} 个结果")
            else:
                print(f"  ❌ '{query}': 搜索失败")
        
        # 测试区域数据
        test_regions = ["北京", "上海", "广东"]
        print(f"\n🗺️ 测试区域数据:")
        for region in test_regions:
            for data_type in ["roads", "buildings", "pois"]:
                response = requests.get(f"{base_url}/api/region/{region}/{data_type}")
                if response.status_code == 200:
                    data = response.json()
                    if "error" not in data:
                        node_count = len(data.get("nodes", []))
                        way_count = len(data.get("ways", []))
                        print(f"  ✅ {region} {data_type}: {node_count} 节点, {way_count} 路径")
                    else:
                        print(f"  ❌ {region} {data_type}: {data['error']}")
                else:
                    print(f"  ❌ {region} {data_type}: HTTP {response.status_code}")
        
        print(f"\n🎯 地图质量改进总结:")
        print(f"  ✅ 支持18级缩放精度")
        print(f"  ✅ 多种地图图层选择 (高德、百度、OSM、卫星)")
        print(f"  ✅ 智能矢量数据渲染")
        print(f"  ✅ 分类显示道路、建筑、POI")
        print(f"  ✅ 根据类型设置不同颜色和样式")
        print(f"  ✅ 实时搜索和著名地标功能")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 使用右上角图层控制切换地图类型")
        print(f"  2. 高德地图和百度地图提供更好的中文显示")
        print(f"  3. 卫星图适合查看实际地形")
        print(f"  4. 矢量数据提供详细的道路和建筑信息")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保系统正在运行")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_map_quality()
