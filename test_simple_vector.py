#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化矢量数据下载功能
"""

from simple_vector_downloader import SimpleVectorDownloader

def test_beijing_roads():
    """测试北京市道路数据下载"""
    print("🧪 测试北京市道路数据下载")
    print("=" * 40)
    
    downloader = SimpleVectorDownloader()
    
    # 北京市边界框
    beijing_bbox = (116.0, 39.4, 117.0, 40.2)
    test_file = downloader.output_dir / "test_beijing_roads.xml"
    
    success, message = downloader.download_roads_data(beijing_bbox, test_file)
    print(f"结果: {message}")
    
    if success:
        print("✅ 道路数据下载成功!")
        # 检查文件大小
        if test_file.exists():
            file_size = test_file.stat().st_size
            print(f"   文件大小: {file_size:,} bytes")
    else:
        print("❌ 道路数据下载失败")
    
    return success

def test_beijing_buildings():
    """测试北京市建筑物数据下载"""
    print("\n🧪 测试北京市建筑物数据下载")
    print("=" * 40)
    
    downloader = SimpleVectorDownloader()
    
    # 北京市边界框
    beijing_bbox = (116.0, 39.4, 117.0, 40.2)
    test_file = downloader.output_dir / "test_beijing_buildings.xml"
    
    success, message = downloader.download_buildings_data(beijing_bbox, test_file)
    print(f"结果: {message}")
    
    if success:
        print("✅ 建筑物数据下载成功!")
        # 检查文件大小
        if test_file.exists():
            file_size = test_file.stat().st_size
            print(f"   文件大小: {file_size:,} bytes")
    else:
        print("❌ 建筑物数据下载失败")
    
    return success

def test_region_download():
    """测试区域数据下载"""
    print("\n🧪 测试区域数据下载")
    print("=" * 40)
    
    downloader = SimpleVectorDownloader()
    
    # 测试下载北京市的道路数据
    beijing = downloader.cities['1']
    results = downloader.download_region_data(
        beijing['name'], 
        beijing['bbox'], 
        ['roads']
    )
    
    print(f"区域下载结果:")
    for data_type, (success, message) in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {data_type}: {status}")
    
    return all(success for success, _ in results.values())

def main():
    """主函数"""
    print("🧪 简化矢量数据下载功能测试")
    print("=" * 50)
    
    # 测试道路数据下载
    roads_success = test_beijing_roads()
    
    # 测试建筑物数据下载
    buildings_success = test_beijing_buildings()
    
    # 测试区域数据下载
    region_success = test_region_download()
    
    print(f"\n📊 测试结果:")
    print(f"   道路数据: {'✅ 成功' if roads_success else '❌ 失败'}")
    print(f"   建筑物数据: {'✅ 成功' if buildings_success else '❌ 失败'}")
    print(f"   区域下载: {'✅ 成功' if region_success else '❌ 失败'}")
    
    if roads_success and buildings_success and region_success:
        print("\n🎉 所有测试通过! 矢量数据下载功能正常")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
