
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于OSM数据的简化瓦片生成器
"""

import os
import sys
import math
import json
from pathlib import Path
import subprocess
from PIL import Image, ImageDraw, ImageFont
import mercantile

class SimpleOSMTileGenerator:
    def __init__(self, osm_file, output_dir):
        self.osm_file = Path(osm_file)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        xtile = int((lon_deg + 180.0) / 360.0 * n)
        ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (xtile, ytile)
    
    def num2deg(self, xtile, ytile, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = xtile / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def create_tile_image(self, x, y, z, features=None):
        """创建瓦片图像"""
        # 创建256x256的图像
        img = Image.new('RGB', (256, 256), color='#f8f8f8')
        draw = ImageDraw.Draw(img)
        
        # 绘制网格
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
            draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
        
        # 绘制瓦片信息
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        # 瓦片坐标信息
        tile_info = f"Z{z} X{x} Y{y}"
        bbox = draw.textbbox((0, 0), tile_info, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        draw.text(((256 - text_width) // 2, 10), tile_info, 
                 fill='#333333', font=font)
        
        # 绘制经纬度范围
        lat1, lon1 = self.num2deg(x, y, z)
        lat2, lon2 = self.num2deg(x + 1, y + 1, z)
        
        coord_info = f"({lat1:.3f}, {lon1:.3f})"
        draw.text((10, 30), coord_info, fill='#666666', font=font)
        
        coord_info2 = f"({lat2:.3f}, {lon2:.3f})"
        draw.text((10, 50), coord_info2, fill='#666666', font=font)
        
        # 如果有特征数据，绘制简单的地理特征
        if features:
            self.draw_features(draw, features, x, y, z)
        
        return img
    
    def draw_features(self, draw, features, x, y, z):
        """绘制地理特征"""
        # 简单的特征绘制
        # 绘制一些模拟的道路
        for i in range(3):
            start_x = 50 + i * 60
            start_y = 50
            end_x = start_x + 100
            end_y = 200
            draw.line([(start_x, start_y), (end_x, end_y)], 
                     fill='#ffcc00', width=3)
        
        # 绘制一些模拟的建筑
        for i in range(2):
            for j in range(2):
                rect_x = 80 + i * 80
                rect_y = 80 + j * 80
                draw.rectangle([(rect_x, rect_y), (rect_x + 30, rect_y + 30)], 
                             fill='#cccccc', outline='#999999')
    
    def extract_osm_features(self, bbox):
        """从OSM文件提取特征（简化版）"""
        # 这里应该使用osmium或其他工具解析OSM数据
        # 为了简化，返回模拟数据
        return {
            'roads': [],
            'buildings': [],
            'water': []
        }
    
    def generate_tiles_for_bbox(self, north, south, east, west, min_zoom=8, max_zoom=15):
        """为指定边界框生成瓦片"""
        print(f"🗺️ 生成瓦片范围: N{north} S{south} E{east} W{west}")
        print(f"📊 缩放级别: {min_zoom}-{max_zoom}")
        
        total_tiles = 0
        
        for zoom in range(min_zoom, max_zoom + 1):
            print(f"🎯 生成缩放级别 {zoom}...")
            
            # 计算瓦片范围
            min_x, max_y = self.deg2num(north, west, zoom)
            max_x, min_y = self.deg2num(south, east, zoom)
            
            zoom_tiles = 0
            
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    # 创建目录
                    tile_dir = self.output_dir / str(zoom) / str(x)
                    tile_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 生成瓦片
                    tile_path = tile_dir / f"{y}.png"
                    
                    if not tile_path.exists():
                        # 获取瓦片边界框
                        lat1, lon1 = self.num2deg(x, y, zoom)
                        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
                        bbox = (lat1, lon1, lat2, lon2)
                        
                        # 提取OSM特征
                        features = self.extract_osm_features(bbox)
                        
                        # 创建瓦片图像
                        img = self.create_tile_image(x, y, zoom, features)
                        img.save(tile_path, 'PNG')
                        
                        zoom_tiles += 1
                        total_tiles += 1
            
            print(f"✅ 缩放级别 {zoom}: 生成 {zoom_tiles} 个瓦片")
        
        print(f"🎉 总共生成 {total_tiles} 个瓦片")
        return total_tiles
    
    def calculate_bbox_from_gps(self, lat, lon, radius_km=20):
        """根据GPS坐标和半径计算边界框"""
        # 地球半径（公里）
        earth_radius = 6371.0
        
        # 将半径转换为度数
        lat_delta = radius_km / earth_radius * (180 / math.pi)
        lon_delta = radius_km / earth_radius * (180 / math.pi) / math.cos(math.radians(lat))
        
        return {
            'north': lat + lat_delta,
            'south': lat - lat_delta,
            'east': lon + lon_delta,
            'west': lon - lon_delta
        }
    
    def generate_gps_area_tiles(self, lat, lon, radius_km=20, max_zoom=18):
        """生成GPS定位处为中心的指定半径区域瓦片"""
        bbox = self.calculate_bbox_from_gps(lat, lon, radius_km)
        
        print(f"📍 GPS中心点: ({lat}, {lon})")
        print(f"📏 生成半径: {radius_km}公里")
        print(f"🗺️ 计算边界: N{bbox['north']:.4f} S{bbox['south']:.4f} E{bbox['east']:.4f} W{bbox['west']:.4f}")
        
        return self.generate_tiles_for_bbox(**bbox, min_zoom=8, max_zoom=max_zoom)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='简化OSM瓦片生成器')
    parser.add_argument('--osm-file', required=True, help='OSM PBF文件路径')
    parser.add_argument('--output-dir', required=True, help='输出目录')
    parser.add_argument('--region', choices=['china', 'beijing', 'gps'], 
                       default='gps', help='生成区域')
    parser.add_argument('--lat', type=float, help='GPS纬度（用于gps模式）')
    parser.add_argument('--lon', type=float, help='GPS经度（用于gps模式）')
    parser.add_argument('--radius', type=float, default=20, help='半径（公里，默认20）')
    parser.add_argument('--max-zoom', type=int, default=18, help='最大缩放级别（默认18）')
    
    args = parser.parse_args()
    
    generator = SimpleOSMTileGenerator(args.osm_file, args.output_dir)
    
    if args.region == 'china':
        generator.generate_china_tiles()
    elif args.region == 'beijing':
        generator.generate_beijing_tiles()
    elif args.region == 'gps':
        if args.lat is None or args.lon is None:
            print("❌ GPS模式需要指定 --lat 和 --lon 参数")
            sys.exit(1)
        generator.generate_gps_area_tiles(args.lat, args.lon, args.radius, args.max_zoom)
    else:
        print("❌ 未知的区域类型")
        sys.exit(1)

if __name__ == '__main__':
    main()
