from flask import Flask, render_template, jsonify
from flask_socketio import <PERSON>cket<PERSON>, emit
import threading
import time
import json
from data_receiver import DataReceiver
from config import Config
from offline_map_config import offline_map

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局数据存储
current_data = {
    'gps': {'lat': 39.9042, 'lng': 116.4074, 'timestamp': None},
    'plc': {
        'gas_levels': {'CO': 0, 'H2S': 0, 'CH4': 0, 'O2': 21},
        'fan_status': {'fan1': False, 'fan2': False},
        'temperature': 25,
        'humidity': 60,
        'timestamp': None
    }
}

data_receiver = DataReceiver()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/current-data')
def get_current_data():
    return jsonify(current_data)

@app.route('/api/map-config')
def get_map_config():
    """获取地图配置"""
    return jsonify(offline_map.load_config())

@app.route('/api/map-stats')
def get_map_stats():
    """获取地图统计信息"""
    return jsonify(offline_map.get_tile_stats())

@socketio.on('connect')
def handle_connect():
    print('客户端已连接')
    emit('data_update', current_data)

@socketio.on('disconnect')
def handle_disconnect():
    print('客户端已断开连接')

def data_update_thread():
    """数据更新线程"""
    while True:
        try:
            # 获取GPS数据
            gps_data = data_receiver.get_gps_data()
            if gps_data:
                current_data['gps'].update(gps_data)
                current_data['gps']['timestamp'] = time.time()
            
            # 获取PLC数据
            plc_data = data_receiver.get_plc_data()
            if plc_data:
                current_data['plc'].update(plc_data)
                current_data['plc']['timestamp'] = time.time()
            
            # 通过WebSocket发送更新数据
            socketio.emit('data_update', current_data)
            
        except Exception as e:
            print(f"数据更新错误: {e}")
        
        time.sleep(Config.UPDATE_INTERVAL)

if __name__ == '__main__':
    # 启动数据接收器
    data_receiver.start()
    
    # 启动数据更新线程
    update_thread = threading.Thread(target=data_update_thread, daemon=True)
    update_thread.start()
    
    # 启动Flask应用
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)