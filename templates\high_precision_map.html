<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度矢量地图系统 - 20米内精度</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 350px;
            background: #f5f5f5;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #ddd;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .control-panel {
            background: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #005a87;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .search-results {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .search-result {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .search-result:hover {
            background: #f8f9fa;
        }
        
        .search-result:last-child {
            border-bottom: none;
        }
        
        .result-name {
            font-weight: bold;
            color: #333;
        }
        
        .result-type {
            color: #666;
            font-size: 0.9em;
        }
        
        .result-precision {
            color: #28a745;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .coordinates {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        
        .precision-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .precision-high {
            background: #28a745;
            color: white;
        }
        
        .precision-medium {
            background: #ffc107;
            color: black;
        }
        
        .precision-low {
            background: #dc3545;
            color: white;
        }
        
        .precision-info {
            background: #17a2b8;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="control-panel">
                <h3>🎯 高精度矢量地图系统</h3>
                <div class="precision-info">
                    <strong>目标精度: 20米内</strong><br>
                    坐标精度: 6位小数<br>
                    支持最高20级缩放
                </div>
            </div>
            
            <div class="control-panel">
                <h3>🔍 高精度搜索定位</h3>
                
                <div class="form-group">
                    <label for="searchInput">搜索地点:</label>
                    <input type="text" id="searchInput" placeholder="输入地点名称，如：天安门">
                </div>
                
                <button class="btn" onclick="searchLocationHighPrecision()">高精度搜索</button>
                <button class="btn btn-secondary" onclick="showFamousPlacesHighPrecision()">高精度地标</button>
                
                <div id="searchResults" class="search-results" style="display: none;"></div>
                <div id="famousPlaces" class="search-results" style="display: none;"></div>
            </div>
            
            <div class="control-panel">
                <h3>📍 精确坐标定位</h3>
                
                <div class="form-group">
                    <label for="latInput">纬度 (6位小数):</label>
                    <input type="number" id="latInput" step="0.000001" placeholder="39.904200">
                </div>
                
                <div class="form-group">
                    <label for="lngInput">经度 (6位小数):</label>
                    <input type="number" id="lngInput" step="0.000001" placeholder="116.407400">
                </div>
                
                <button class="btn" onclick="goToLocationHighPrecision()">精确定位</button>
                <button class="btn btn-secondary" onclick="getCurrentLocationHighPrecision()">获取当前位置</button>
                <button class="btn btn-success" onclick="validatePrecision()">验证精度</button>
            </div>
            
            <div class="control-panel">
                <h3>ℹ️ 状态信息</h3>
                <div id="status" class="status info">
                    高精度矢量地图系统已就绪
                </div>
                
                <div id="coordinates" class="coordinates" style="display: none;">
                    坐标: <span id="coordText"></span>
                </div>
                
                <div id="precisionInfo" class="coordinates" style="display: none;">
                    精度: <span id="precisionText"></span>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 高精度地图配置
        const mapConfig = {{ map_config | tojson }};
        const famousPlaces = {{ famous_places | tojson }};
        
        // 初始化高精度地图
        const map = L.map('map').setView([mapConfig.center_lat, mapConfig.center_lng], mapConfig.zoom);
        
        // 添加高精度底图
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: mapConfig.max_zoom,
            minZoom: mapConfig.min_zoom
        }).addTo(map);
        
        // 存储矢量数据图层
        let vectorLayers = {
            roads: null,
            buildings: null,
            pois: null
        };
        
        // 状态显示
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 显示坐标
        function showCoordinates(lat, lng) {
            const coordDiv = document.getElementById('coordinates');
            const coordText = document.getElementById('coordText');
            coordText.textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            coordDiv.style.display = 'block';
        }
        
        // 显示精度信息
        function showPrecisionInfo(precision) {
            const precisionDiv = document.getElementById('precisionInfo');
            const precisionText = document.getElementById('precisionText');
            precisionText.textContent = precision;
            precisionDiv.style.display = 'block';
        }
        
        // 获取精度指示器
        function getPrecisionIndicator(precision) {
            if (precision.includes('5m') || precision.includes('10m')) {
                return '<span class="precision-indicator precision-high">高精度</span>';
            } else if (precision.includes('15m') || precision.includes('20m')) {
                return '<span class="precision-indicator precision-medium">中精度</span>';
            } else {
                return '<span class="precision-indicator precision-low">低精度</span>';
            }
        }
        
        // 高精度搜索地点
        async function searchLocationHighPrecision() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showStatus('请输入搜索关键词', 'error');
                return;
            }
            
            showStatus('正在高精度搜索...', 'info');
            
            try {
                const response = await fetch(`/api/search-high-precision?q=${encodeURIComponent(query)}`);
                const results = await response.json();
                
                displaySearchResultsHighPrecision(results);
                
                if (results.length > 0) {
                    showStatus(`找到 ${results.length} 个高精度结果`, 'success');
                } else {
                    showStatus('未找到匹配结果', 'error');
                }
                
            } catch (error) {
                showStatus(`搜索失败: ${error.message}`, 'error');
            }
        }
        
        // 显示高精度搜索结果
        function displaySearchResultsHighPrecision(results) {
            const resultsDiv = document.getElementById('searchResults');
            const famousDiv = document.getElementById('famousPlaces');
            
            // 隐藏著名地标列表
            famousDiv.style.display = 'none';
            
            if (results.length === 0) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            resultsDiv.innerHTML = '';
            results.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'search-result';
                resultDiv.innerHTML = `
                    <div class="result-name">${result.name} ${getPrecisionIndicator(result.precision)}</div>
                    <div class="result-type">${result.type} - ${result.region}</div>
                    <div class="result-precision">精度: ${result.precision}</div>
                `;
                
                resultDiv.onclick = () => {
                    map.setView([result.lat, result.lon], 18); // 使用最高缩放级别
                    L.marker([result.lat, result.lon]).addTo(map)
                        .bindPopup(`<b>${result.name}</b><br>类型: ${result.type}<br>城市: ${result.region}<br>精度: ${result.precision}`)
                        .openPopup();
                    showCoordinates(result.lat, result.lon);
                    showPrecisionInfo(result.precision);
                };
                
                resultsDiv.appendChild(resultDiv);
            });
            
            resultsDiv.style.display = 'block';
        }
        
        // 显示高精度著名地标
        async function showFamousPlacesHighPrecision() {
            const famousDiv = document.getElementById('famousPlaces');
            const resultsDiv = document.getElementById('searchResults');
            
            // 隐藏搜索结果
            resultsDiv.style.display = 'none';
            
            famousDiv.innerHTML = '';
            
            // 按城市分组
            const cities = {};
            Object.entries(famousPlaces).forEach(([name, info]) => {
                if (!cities[info.city]) {
                    cities[info.city] = [];
                }
                cities[info.city].push({name, ...info});
            });
            
            Object.entries(cities).forEach(([city, cityPlaces]) => {
                const cityDiv = document.createElement('div');
                cityDiv.innerHTML = `<h4 style="margin: 10px 0 5px 0; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px;">${city}</h4>`;
                famousDiv.appendChild(cityDiv);
                
                cityPlaces.forEach(place => {
                    const placeDiv = document.createElement('div');
                    placeDiv.className = 'search-result';
                    placeDiv.innerHTML = `
                        <div class="result-name">${place.name} ${getPrecisionIndicator(place.precision)}</div>
                        <div class="result-type">${place.type}</div>
                        <div class="result-precision">精度: ${place.precision}</div>
                    `;
                    
                    placeDiv.onclick = () => {
                        map.setView([place.lat, place.lng], 18); // 使用最高缩放级别
                        L.marker([place.lat, place.lng]).addTo(map)
                            .bindPopup(`<b>${place.name}</b><br>类型: ${place.type}<br>城市: ${place.city}<br>精度: ${place.precision}`)
                            .openPopup();
                        showCoordinates(place.lat, place.lng);
                        showPrecisionInfo(place.precision);
                    };
                    
                    famousDiv.appendChild(placeDiv);
                });
            });
            
            famousDiv.style.display = 'block';
        }
        
        // 高精度定位到指定坐标
        function goToLocationHighPrecision() {
            const lat = parseFloat(document.getElementById('latInput').value);
            const lng = parseFloat(document.getElementById('lngInput').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                showStatus('请输入有效的坐标', 'error');
                return;
            }
            
            map.setView([lat, lng], 18); // 使用最高缩放级别
            L.marker([lat, lng]).addTo(map)
                .bindPopup(`<b>精确定位点</b><br>纬度: ${lat.toFixed(6)}<br>经度: ${lng.toFixed(6)}`)
                .openPopup();
            
            showCoordinates(lat, lng);
            showPrecisionInfo('20米内精度');
            showStatus('已精确定位到指定坐标', 'success');
        }
        
        // 获取高精度当前位置
        function getCurrentLocationHighPrecision() {
            if (!navigator.geolocation) {
                showStatus('浏览器不支持定位功能', 'error');
                return;
            }
            
            showStatus('正在获取高精度当前位置...', 'info');
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    const accuracy = position.coords.accuracy;
                    
                    document.getElementById('latInput').value = lat;
                    document.getElementById('lngInput').value = lng;
                    
                    map.setView([lat, lng], 18); // 使用最高缩放级别
                    L.marker([lat, lng]).addTo(map)
                        .bindPopup(`<b>当前位置</b><br>纬度: ${lat.toFixed(6)}<br>经度: ${lng.toFixed(6)}<br>精度: ${accuracy.toFixed(1)}米`)
                        .openPopup();
                    
                    showCoordinates(lat, lng);
                    showPrecisionInfo(`${accuracy.toFixed(1)}米精度`);
                    showStatus('已获取高精度当前位置', 'success');
                },
                (error) => {
                    showStatus(`获取位置失败: ${error.message}`, 'error');
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
        }
        
        // 验证精度
        async function validatePrecision() {
            const lat = parseFloat(document.getElementById('latInput').value);
            const lng = parseFloat(document.getElementById('lngInput').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                showStatus('请输入有效的坐标', 'error');
                return;
            }
            
            // 这里可以添加精度验证逻辑
            showStatus('精度验证功能开发中...', 'info');
        }
        
        // 地图点击事件
        map.on('click', (e) => {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            
            document.getElementById('latInput').value = lat;
            document.getElementById('lngInput').value = lng;
            
            showCoordinates(lat, lng);
            showPrecisionInfo('20米内精度');
            showStatus('已选择高精度坐标', 'info');
        });
        
        // 初始化状态
        showStatus('高精度矢量地图系统已就绪 - 目标精度20米内', 'success');
    </script>
</body>
</html>
