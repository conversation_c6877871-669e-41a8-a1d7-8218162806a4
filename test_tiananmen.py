#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天安门定位功能
"""

from vector_map_viewer import VectorMapViewer

def test_tiananmen_search():
    """测试天安门搜索功能"""
    print("🧪 测试天安门定位功能")
    print("=" * 50)
    
    viewer = VectorMapViewer()
    
    # 测试搜索天安门
    print("1. 搜索'天安门'")
    results = viewer.search_poi("天安门")
    
    if results:
        print(f"   ✅ 找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result['name']} - {result['type']} ({result['region']})")
            print(f"      坐标: {result['lat']:.6f}, {result['lon']:.6f}")
    else:
        print("   ❌ 未找到结果")
    
    # 测试搜索故宫
    print("\n2. 搜索'故宫'")
    results = viewer.search_poi("故宫")
    
    if results:
        print(f"   ✅ 找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result['name']} - {result['type']} ({result['region']})")
            print(f"      坐标: {result['lat']:.6f}, {result['lon']:.6f}")
    else:
        print("   ❌ 未找到结果")
    
    # 测试搜索其他著名地标
    print("\n3. 测试其他著名地标")
    test_places = ["鸟巢", "水立方", "王府井", "外滩", "西湖"]
    
    for place in test_places:
        results = viewer.search_poi(place)
        if results:
            result = results[0]
            print(f"   ✅ {place}: {result['lat']:.6f}, {result['lon']:.6f}")
        else:
            print(f"   ❌ {place}: 未找到")

def test_famous_places_database():
    """测试著名地标数据库"""
    print("\n🧪 测试著名地标数据库")
    print("=" * 50)
    
    viewer = VectorMapViewer()
    
    print(f"数据库中共有 {len(viewer.famous_places)} 个著名地标:")
    
    # 按城市统计
    cities = {}
    for name, info in viewer.famous_places.items():
        city = info['city']
        if city not in cities:
            cities[city] = []
        cities[city].append(name)
    
    for city, places in cities.items():
        print(f"\n{city} ({len(places)}个):")
        for place in places:
            info = viewer.famous_places[place]
            print(f"   - {place} ({info['type']})")

def test_coordinate_accuracy():
    """测试坐标精度"""
    print("\n🧪 测试坐标精度")
    print("=" * 50)
    
    viewer = VectorMapViewer()
    
    # 天安门精确坐标
    tiananmen = viewer.famous_places['天安门']
    print(f"天安门坐标: {tiananmen['lat']:.6f}, {tiananmen['lng']:.6f}")
    
    # 验证坐标是否在合理范围内
    lat, lng = tiananmen['lat'], tiananmen['lng']
    
    # 北京大致范围检查
    if 39.4 <= lat <= 40.2 and 115.7 <= lng <= 117.4:
        print("   ✅ 坐标在北京市范围内")
    else:
        print("   ❌ 坐标超出北京市范围")
    
    # 天安门广场大致位置检查
    if 39.9 <= lat <= 39.91 and 116.4 <= lng <= 116.42:
        print("   ✅ 坐标在天安门广场附近")
    else:
        print("   ❌ 坐标不在天安门广场附近")

def main():
    """主函数"""
    print("🧪 天安门定位功能测试")
    print("=" * 60)
    
    # 测试搜索功能
    test_tiananmen_search()
    
    # 测试数据库
    test_famous_places_database()
    
    # 测试坐标精度
    test_coordinate_accuracy()
    
    print(f"\n✅ 测试完成!")
    print("\n💡 使用建议:")
    print("   1. 在矢量地图查看器中搜索'天安门'")
    print("   2. 点击'著名地标'按钮查看所有地标")
    print("   3. 直接点击地标名称进行定位")

if __name__ == "__main__":
    main()
