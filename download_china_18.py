#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载中国全国18级瓦片
专门用于创建离线地图
"""

import os
import requests
import math
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class ChinaTileDownloader:
    def __init__(self, output_dir="static/tiles"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 地图源配置
        self.sources = {
            'osm': {
                'name': 'OpenStreetMap',
                'url': 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            },
            'baidu': {
                'name': '百度地图',
                'url': 'https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20240101',
                'subdomains': ['0', '1', '2', '3'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://map.baidu.com/',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            },
            'amap': {
                'name': '高德地图',
                'url': 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
                'subdomains': ['1', '2', '3', '4'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer': 'https://ditu.amap.com/',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            }
        }
        
        self.current_source = 'osm'
        self.stats = {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """将经纬度转换为瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return x, y
    
    def get_china_bounds(self):
        """获取中国边界框"""
        return {
            'min_lat': 3.86,   # 最南端
            'max_lat': 53.55,  # 最北端
            'min_lon': 73.66,  # 最西端
            'max_lon': 135.05  # 最东端
        }
    
    def download_tile(self, x, y, z):
        """下载单个瓦片"""
        source = self.sources[self.current_source]
        
        # 处理子域名轮换
        if 'subdomains' in source:
            subdomain = source['subdomains'][(x + y) % len(source['subdomains'])]
            url = source['url'].format(x=x, y=y, z=z, s=subdomain)
        else:
            url = source['url'].format(x=x, y=y, z=z)
        
        # 创建目录
        tile_dir = self.output_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        
        tile_path = tile_dir / f"{y}.png"
        
        # 如果文件已存在且大小合理，跳过下载
        if tile_path.exists() and tile_path.stat().st_size > 2000:
            self.stats['skipped'] += 1
            return True, f"跳过 {z}/{x}/{y}"
        
        try:
            response = requests.get(url, headers=source['headers'], timeout=15)
            response.raise_for_status()
            
            if len(response.content) < 1000:
                self.stats['failed'] += 1
                return False, f"瓦片 {z}/{x}/{y} 内容过小 ({len(response.content)} bytes)"
            
            with open(tile_path, 'wb') as f:
                f.write(response.content)
            
            self.stats['success'] += 1
            return True, f"下载 {z}/{x}/{y} ({len(response.content)} bytes)"
            
        except Exception as e:
            self.stats['failed'] += 1
            return False, f"下载 {z}/{x}/{y} 失败: {e}"
    
    def download_china_18(self, max_workers=8, batch_size=1000):
        """下载中国全国18级瓦片"""
        print(f"🗺️  开始下载中国全国18级瓦片")
        print(f"   地图源: {self.sources[self.current_source]['name']}")
        print(f"   并发数: {max_workers}")
        print(f"   批次大小: {batch_size}")
        
        # 计算瓦片范围
        bounds = self.get_china_bounds()
        min_x, max_y = self.deg2num(bounds['max_lat'], bounds['min_lon'], 18)
        max_x, min_y = self.deg2num(bounds['min_lat'], bounds['max_lon'], 18)
        
        # 确保范围正确
        if min_x > max_x:
            min_x, max_x = max_x, min_x
        if min_y > max_y:
            min_y, max_y = max_y, min_y
        
        total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        self.stats['total'] = total_tiles
        
        print(f"   瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
        print(f"   总瓦片数: {total_tiles:,}")
        
        if total_tiles > 1000000:
            print(f"   ⚠️  警告: 瓦片数量巨大，可能需要很长时间")
            confirm = input("   是否继续? (y/N): ").strip().lower()
            if confirm != 'y':
                print("   已取消下载")
                return
        
        # 分批下载
        batch_count = 0
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                batch_count += 1
                
                if batch_count % batch_size == 0:
                    print(f"   📦 处理批次 {batch_count//batch_size} ({batch_count:,}/{total_tiles:,})")
                
                success, message = self.download_tile(x, y, 18)
                
                if not success and batch_count % 100 == 0:
                    print(f"   ❌ {message}")
                
                # 避免请求过快
                if batch_count % 10 == 0:
                    time.sleep(0.1)
        
        # 最终统计
        print(f"\n🎉 下载完成!")
        print(f"   总瓦片数: {self.stats['total']:,}")
        print(f"   成功下载: {self.stats['success']:,}")
        print(f"   跳过文件: {self.stats['skipped']:,}")
        print(f"   下载失败: {self.stats['failed']:,}")
        
        if self.stats['total'] > 0:
            success_rate = (self.stats['success'] + self.stats['skipped']) / self.stats['total'] * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        return self.stats
    
    def set_source(self, source_name):
        """设置地图源"""
        if source_name in self.sources:
            self.current_source = source_name
            print(f"✅ 已切换到地图源: {self.sources[source_name]['name']}")
        else:
            print(f"❌ 未知的地图源: {source_name}")
            print(f"可用地图源: {list(self.sources.keys())}")

def main():
    """主函数"""
    print("🗺️  中国全国18级瓦片下载工具")
    print("=" * 50)
    
    downloader = ChinaTileDownloader()
    
    # 显示可用地图源
    print("可用地图源:")
    for key, config in downloader.sources.items():
        print(f"  {key}: {config['name']}")
    
    # 选择地图源
    source = input("\n请选择地图源 (默认: osm): ").strip() or 'osm'
    downloader.set_source(source)
    
    # 设置参数
    try:
        max_workers = int(input("请输入并发数 (默认: 8): ") or "8")
        batch_size = int(input("请输入批次大小 (默认: 1000): ") or "1000")
    except ValueError:
        print("❌ 输入格式错误，使用默认值")
        max_workers = 8
        batch_size = 1000
    
    print(f"\n🚀 开始下载中国全国18级瓦片")
    print(f"   地图源: {downloader.sources[downloader.current_source]['name']}")
    print(f"   并发数: {max_workers}")
    print(f"   批次大小: {batch_size}")
    
    # 开始下载
    stats = downloader.download_china_18(max_workers=max_workers, batch_size=batch_size)
    
    print(f"\n✅ 下载完成! 瓦片保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
