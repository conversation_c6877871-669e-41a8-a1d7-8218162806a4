<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图图层测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 500px; width: 100%; border: 1px solid #ccc; }
        .info { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🗺️ 地图图层选择测试</h1>
    
    <div class="info">
        <h3>测试说明</h3>
        <p>此页面用于测试地图图层选择功能。您应该能看到右上角的图层控制按钮。</p>
        <p>可用的地图图层：</p>
        <ul>
            <li><strong>高德地图</strong> - 中文显示友好</li>
            <li><strong>百度地图</strong> - 详细中文标注</li>
            <li><strong>OpenStreetMap</strong> - 开源地图</li>
            <li><strong>卫星图</strong> - 真实地形</li>
        </ul>
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <h3>使用说明</h3>
        <p>1. 点击右上角的图层控制按钮（通常显示为"图层"或"Layers"）</p>
        <p>2. 选择不同的地图图层</p>
        <p>3. 观察地图的变化</p>
        <p>4. 尝试不同的缩放级别（最大支持18级）</p>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            maxZoom: 18,
            minZoom: 3
        }).setView([39.9042, 116.4074], 10); // 北京天安门
        
        // 高精度地图图层选项
        const baseLayers = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }),
            "高德地图": L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                attribution: '© 高德地图',
                subdomains: ['1', '2', '3', '4'],
                maxZoom: 18
            }),
            "百度地图": L.tileLayer('https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20240101', {
                attribution: '© 百度地图',
                subdomains: ['0', '1', '2', '3'],
                maxZoom: 18
            }),
            "卫星图": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri',
                maxZoom: 18
            })
        };
        
        // 添加默认底图
        baseLayers["高德地图"].addTo(map);
        
        // 添加图层控制
        L.control.layers(baseLayers).addTo(map);
        
        // 添加一个标记点
        L.marker([39.9042, 116.4074]).addTo(map)
            .bindPopup('天安门广场<br>测试地图图层功能')
            .openPopup();
        
        // 添加缩放控制
        L.control.scale().addTo(map);
        
        console.log('地图初始化完成，图层控制已添加');
    </script>
</body>
</html>
