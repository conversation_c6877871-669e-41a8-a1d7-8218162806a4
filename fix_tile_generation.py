# -*- coding: utf-8 -*-
"""
修复瓦片生成 - 使用正确的开源方案
"""

import os
import sys
import math
import shutil
from pathlib import Path
import subprocess

class FixedTileGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        
        # 中国地区正确的坐标范围
        self.china_bounds = {
            "min_lat": 18.0,  # 南海
            "max_lat": 54.0,  # 黑龙江
            "min_lon": 73.0,  # 新疆
            "max_lon": 135.0  # 黑龙江
        }
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def get_china_tile_bounds(self, zoom):
        """获取中国地区的瓦片边界"""
        min_x, max_y = self.deg2num(self.china_bounds["min_lat"], self.china_bounds["min_lon"], zoom)
        max_x, min_y = self.deg2num(self.china_bounds["max_lat"], self.china_bounds["max_lon"], zoom)
        
        return {
            "min_x": min_x,
            "max_x": max_x,
            "min_y": min_y,
            "max_y": max_y
        }
    
    def clean_invalid_tiles(self):
        """清理无效的瓦片"""
        print("🧹 清理现有无效瓦片...")
        if self.tiles_dir.exists():
            shutil.rmtree(self.tiles_dir)
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_with_osm_data(self):
        """使用OSM数据生成真实瓦片"""
        print("🗺️ 使用OSM数据生成真实瓦片...")
        
        # 检查OSM文件
        osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        if not osm_file.exists():
            print(f"❌ OSM文件不存在: {osm_file}")
            return False
        
        # 尝试使用 osm2pgsql + Mapnik 生成瓦片
        try:
            # 安装必要依赖
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "requests", "pillow", "mercantile"
            ], check=True, capture_output=True)
            
            # 运行真实的OSM瓦片生成器
            result = subprocess.run([
                sys.executable, "real_osm_tile_generator.py"
            ], capture_output=True, text=True, timeout=1800)
            
            if result.returncode == 0:
                print("✅ OSM瓦片生成成功")
                return True
            else:
                print(f"❌ OSM瓦片生成失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 瓦片生成出错: {e}")
            return False
    
    def verify_tiles(self):
        """验证生成的瓦片"""
        print("🔍 验证瓦片质量...")
        
        valid_count = 0
        total_count = 0
        
        for zoom_dir in self.tiles_dir.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom = int(zoom_dir.name)
                bounds = self.get_china_tile_bounds(zoom)
                
                for x_dir in zoom_dir.iterdir():
                    if x_dir.is_dir() and x_dir.name.isdigit():
                        x = int(x_dir.name)
                        
                        # 检查X坐标是否在中国范围内
                        if bounds["min_x"] <= x <= bounds["max_x"]:
                            for tile_file in x_dir.glob("*.png"):
                                y = int(tile_file.stem)
                                total_count += 1
                                
                                # 检查Y坐标是否在中国范围内
                                if bounds["min_y"] <= y <= bounds["max_y"]:
                                    valid_count += 1
        
        print(f"📊 瓦片验证结果: {valid_count}/{total_count} 个瓦片覆盖中国地区")
        return valid_count > 0
    
    def run(self):
        """运行修复流程"""
        print("🚀 开始修复瓦片生成...")
        print("=" * 50)
        
        # 1. 清理无效瓦片
        self.clean_invalid_tiles()
        
        # 2. 使用OSM数据生成真实瓦片
        success = self.generate_with_osm_data()
        
        if success:
            # 3. 验证瓦片
            valid = self.verify_tiles()
            if valid:
                print("\n✅ 瓦片修复完成！现在应该可以显示真实的中国地形了。")
            else:
                print("\n⚠️ 瓦片生成完成，但可能仍需要调整坐标范围。")
        else:
            print("\n❌ 瓦片生成失败，请检查OSM数据和依赖。")
        
        return success

if __name__ == "__main__":
    generator = FixedTileGenerator()
    generator.run()