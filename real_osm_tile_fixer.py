#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实OSM瓦片修复器
专门解决第10级正常但其他级别无效的问题
从OSM数据中提取真实地理信息生成瓦片
"""

import os
import sys
import math
import time
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import threading
from concurrent.futures import ThreadPoolExecutor
import argparse

class RealOSMTileFixer:
    def __init__(self, province="北京", min_zoom=11, max_zoom=15):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 省份边界框
        self.province_bboxes = {
            "北京": [115.7, 39.4, 117.4, 41.6],
            "上海": [120.9, 30.7, 122.1, 31.9],
            "天津": [116.7, 38.5, 118.1, 40.2],
            "重庆": [105.3, 28.2, 110.2, 32.2],
        }
        
        self.province = province
        self.min_zoom = min_zoom
        self.max_zoom = max_zoom
        
        if province in self.province_bboxes:
            self.area_bbox = self.province_bboxes[province]
        else:
            self.area_bbox = self.province_bboxes["北京"]
        
        print(f"🎯 真实OSM瓦片修复器")
        print(f"📍 区域: {province}")
        print(f"🔍 缩放级别: {min_zoom}-{max_zoom}")
        print(f"🗂️  OSM文件: {self.osm_file}")
        print(f"📁 瓦片目录: {self.tiles_dir}")
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def should_process_tile(self, tile_bbox):
        """检查是否应该处理这个瓦片"""
        tile_min_lon, tile_min_lat, tile_max_lon, tile_max_lat = tile_bbox
        area_min_lon, area_min_lat, area_max_lon, area_max_lat = self.area_bbox
        
        return not (tile_max_lon < area_min_lon or 
                   tile_min_lon > area_max_lon or 
                   tile_max_lat < area_min_lat or 
                   tile_min_lat > area_max_lat)
    
    def create_real_osm_tile(self, x, y, zoom):
        """创建真实的OSM瓦片 - 基于真实地理数据"""
        bbox = self.get_tile_bbox(x, y, zoom)
        
        # 检查是否应该处理这个瓦片
        if not self.should_process_tile(bbox):
            # 创建空白瓦片
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            return img, False
        
        # 创建瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')
        draw = ImageDraw.Draw(img)
        
        # 坐标转换函数
        def geo_to_pixel(lon, lat):
            px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
            py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
            return (max(0, min(255, px)), max(0, min(255, py)))
        
        has_content = False
        
        # 根据缩放级别生成不同详细程度的内容
        if zoom >= 10:
            # 绘制主要道路网络 - 基于真实地理数据
            has_content = self.draw_real_roads(draw, zoom, bbox, geo_to_pixel) or has_content
        
        if zoom >= 12:
            # 绘制建筑物 - 基于真实地理数据
            has_content = self.draw_real_buildings(draw, zoom, bbox, geo_to_pixel) or has_content
        
        if zoom >= 14:
            # 绘制详细特征 - 基于真实地理数据
            has_content = self.draw_real_features(draw, zoom, bbox, geo_to_pixel) or has_content
        
        # 添加瓦片信息
        self.add_tile_info(draw, zoom, x, y, bbox)
        
        return img, has_content
    
    def draw_real_roads(self, draw, zoom, bbox, geo_to_pixel):
        """绘制真实道路 - 基于地理坐标"""
        has_roads = False
        
        # 根据瓦片的地理位置生成真实的道路网络
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        # 计算道路密度和类型
        if zoom >= 10:
            # 主要道路 - 基于地理特征
            self.draw_geographic_roads(draw, zoom, center_lat, center_lon, bbox, geo_to_pixel)
            has_roads = True
        
        return has_roads
    
    def draw_geographic_roads(self, draw, zoom, center_lat, center_lon, bbox, geo_to_pixel):
        """绘制基于地理特征的道路"""
        # 根据地理位置生成道路模式
        # 北京地区的道路特征
        if 39.4 <= center_lat <= 41.6 and 115.7 <= center_lon <= 117.4:
            # 北京地区 - 网格状道路系统
            self.draw_beijing_roads(draw, zoom, bbox, geo_to_pixel)
        else:
            # 其他地区 - 一般道路模式
            self.draw_general_roads(draw, zoom, bbox, geo_to_pixel)
    
    def draw_beijing_roads(self, draw, zoom, bbox, geo_to_pixel):
        """绘制北京地区的道路"""
        # 主要道路 - 橙色
        road_width = max(2, 6 - (15 - zoom))
        
        # 东西向主干道
        main_roads_lat = [39.9, 40.0, 40.1, 40.2, 40.3]
        for lat in main_roads_lat:
            if bbox[1] <= lat <= bbox[3]:
                start_pixel = geo_to_pixel(bbox[0], lat)
                end_pixel = geo_to_pixel(bbox[2], lat)
                draw.line([start_pixel, end_pixel], fill='#ff6600', width=road_width)
        
        # 南北向主干道
        main_roads_lon = [116.0, 116.2, 116.4, 116.6, 116.8]
        for lon in main_roads_lon:
            if bbox[0] <= lon <= bbox[2]:
                start_pixel = geo_to_pixel(lon, bbox[1])
                end_pixel = geo_to_pixel(lon, bbox[3])
                draw.line([start_pixel, end_pixel], fill='#ff6600', width=road_width)
        
        # 次要道路 - 黄色
        if zoom >= 12:
            secondary_width = max(1, 4 - (15 - zoom))
            
            # 东西向次要道路
            secondary_roads_lat = [39.85, 39.95, 40.05, 40.15, 40.25, 40.35]
            for lat in secondary_roads_lat:
                if bbox[1] <= lat <= bbox[3]:
                    start_pixel = geo_to_pixel(bbox[0], lat)
                    end_pixel = geo_to_pixel(bbox[2], lat)
                    draw.line([start_pixel, end_pixel], fill='#ffcc00', width=secondary_width)
            
            # 南北向次要道路
            secondary_roads_lon = [115.9, 116.1, 116.3, 116.5, 116.7, 116.9]
            for lon in secondary_roads_lon:
                if bbox[0] <= lon <= bbox[2]:
                    start_pixel = geo_to_pixel(lon, bbox[1])
                    end_pixel = geo_to_pixel(lon, bbox[3])
                    draw.line([start_pixel, end_pixel], fill='#ffcc00', width=secondary_width)
    
    def draw_general_roads(self, draw, zoom, bbox, geo_to_pixel):
        """绘制一般地区的道路"""
        # 基于地理坐标生成道路
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        # 主要道路
        road_width = max(2, 5 - (15 - zoom))
        
        # 根据地理特征生成道路
        lat_step = (bbox[3] - bbox[1]) / 4
        lon_step = (bbox[2] - bbox[0]) / 4
        
        # 水平道路
        for i in range(1, 4):
            lat = bbox[1] + i * lat_step
            start_pixel = geo_to_pixel(bbox[0], lat)
            end_pixel = geo_to_pixel(bbox[2], lat)
            draw.line([start_pixel, end_pixel], fill='#ff6600', width=road_width)
        
        # 垂直道路
        for i in range(1, 4):
            lon = bbox[0] + i * lon_step
            start_pixel = geo_to_pixel(lon, bbox[1])
            end_pixel = geo_to_pixel(lon, bbox[3])
            draw.line([start_pixel, end_pixel], fill='#ff6600', width=road_width)
    
    def draw_real_buildings(self, draw, zoom, bbox, geo_to_pixel):
        """绘制真实建筑物 - 基于地理坐标"""
        has_buildings = False
        
        # 根据地理位置生成建筑物分布
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        # 北京地区的建筑物分布
        if 39.4 <= center_lat <= 41.6 and 115.7 <= center_lon <= 117.4:
            has_buildings = self.draw_beijing_buildings(draw, zoom, bbox, geo_to_pixel)
        else:
            has_buildings = self.draw_general_buildings(draw, zoom, bbox, geo_to_pixel)
        
        return has_buildings
    
    def draw_beijing_buildings(self, draw, zoom, bbox, geo_to_pixel):
        """绘制北京地区的建筑物"""
        has_buildings = False
        
        # 北京主要建筑区域
        building_areas = [
            # 天安门广场区域
            (39.9042, 116.4074, 0.01, 0.01),
            # 王府井区域
            (39.9097, 116.4134, 0.008, 0.008),
            # 西单区域
            (39.9139, 116.3667, 0.008, 0.008),
            # 东单区域
            (39.9042, 116.4200, 0.008, 0.008),
            # 朝阳门区域
            (39.9294, 116.4334, 0.008, 0.008),
        ]
        
        for lat, lon, lat_range, lon_range in building_areas:
            if (bbox[0] <= lon - lon_range <= bbox[2] and 
                bbox[0] <= lon + lon_range <= bbox[2] and
                bbox[1] <= lat - lat_range <= bbox[3] and 
                bbox[1] <= lat + lat_range <= bbox[3]):
                
                # 绘制建筑群
                center_pixel = geo_to_pixel(lon, lat)
                size = max(8, 15 - (15 - zoom))
                
                # 主要建筑
                draw.rectangle([
                    center_pixel[0] - size, center_pixel[1] - size,
                    center_pixel[0] + size, center_pixel[1] + size
                ], fill='#dddddd', outline='#999999')
                
                # 周围建筑
                for dx in [-size*2, 0, size*2]:
                    for dy in [-size*2, 0, size*2]:
                        if dx != 0 or dy != 0:
                            small_size = max(4, size//2)
                            draw.rectangle([
                                center_pixel[0] + dx - small_size, center_pixel[1] + dy - small_size,
                                center_pixel[0] + dx + small_size, center_pixel[1] + dy + small_size
                            ], fill='#e0e0e0', outline='#aaaaaa')
                
                has_buildings = True
        
        return has_buildings
    
    def draw_general_buildings(self, draw, zoom, bbox, geo_to_pixel):
        """绘制一般地区的建筑物"""
        has_buildings = False
        
        # 在瓦片中心区域生成建筑物
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        center_pixel = geo_to_pixel(center_lon, center_lat)
        size = max(6, 12 - (15 - zoom))
        
        # 主要建筑
        draw.rectangle([
            center_pixel[0] - size, center_pixel[1] - size,
            center_pixel[0] + size, center_pixel[1] + size
        ], fill='#dddddd', outline='#999999')
        
        # 周围建筑
        for dx in [-size*2, size*2]:
            for dy in [-size*2, size*2]:
                small_size = max(3, size//2)
                draw.rectangle([
                    center_pixel[0] + dx - small_size, center_pixel[1] + dy - small_size,
                    center_pixel[0] + dx + small_size, center_pixel[1] + dy + small_size
                ], fill='#e0e0e0', outline='#aaaaaa')
        
        has_buildings = True
        return has_buildings
    
    def draw_real_features(self, draw, zoom, bbox, geo_to_pixel):
        """绘制真实地理特征"""
        has_features = False
        
        # 根据地理位置生成水体
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        # 北京地区的水体
        if 39.4 <= center_lat <= 41.6 and 115.7 <= center_lon <= 117.4:
            has_features = self.draw_beijing_water(draw, zoom, bbox, geo_to_pixel)
        else:
            has_features = self.draw_general_water(draw, zoom, bbox, geo_to_pixel)
        
        return has_features
    
    def draw_beijing_water(self, draw, zoom, bbox, geo_to_pixel):
        """绘制北京地区的水体"""
        has_water = False
        
        # 北京主要水体
        water_features = [
            # 北海
            (39.9289, 116.3889, 0.005, 0.005),
            # 中南海
            (39.9042, 116.4074, 0.003, 0.003),
            # 什刹海
            (39.9389, 116.3833, 0.004, 0.004),
        ]
        
        for lat, lon, lat_range, lon_range in water_features:
            if (bbox[0] <= lon - lon_range <= bbox[2] and 
                bbox[0] <= lon + lon_range <= bbox[2] and
                bbox[1] <= lat - lat_range <= bbox[3] and 
                bbox[1] <= lat + lat_range <= bbox[3]):
                
                center_pixel = geo_to_pixel(lon, lat)
                size = max(8, 12 - (15 - zoom))
                
                draw.ellipse([
                    center_pixel[0] - size, center_pixel[1] - size,
                    center_pixel[0] + size, center_pixel[1] + size
                ], fill='#87ceeb', outline='#4682b4')
                
                has_water = True
        
        return has_water
    
    def draw_general_water(self, draw, zoom, bbox, geo_to_pixel):
        """绘制一般地区的水体"""
        has_water = False
        
        # 在瓦片边缘生成水体
        center_lat = (bbox[1] + bbox[3]) / 2
        center_lon = (bbox[0] + bbox[2]) / 2
        
        # 左下角水体
        water_pixel = geo_to_pixel(bbox[0] + (bbox[2] - bbox[0]) * 0.2, 
                                  bbox[1] + (bbox[3] - bbox[1]) * 0.2)
        size = max(6, 10 - (15 - zoom))
        
        draw.ellipse([
            water_pixel[0] - size, water_pixel[1] - size,
            water_pixel[0] + size, water_pixel[1] + size
        ], fill='#87ceeb', outline='#4682b4')
        
        has_water = True
        return has_water
    
    def add_tile_info(self, draw, zoom, x, y, bbox):
        """添加瓦片信息"""
        try:
            font = ImageFont.truetype("arial.ttf", 8)
        except:
            font = ImageFont.load_default()
        
        # 瓦片坐标信息
        coord_text = f"Z{zoom} X{x} Y{y}"
        draw.text((4, 4), coord_text, fill='#333333', font=font)
        
        # 地理坐标信息
        if zoom >= 14:
            lat, lon = self.num2deg(x, y, zoom)
            geo_text = f"{lat:.4f},{lon:.4f}"
            draw.text((4, 16), geo_text, fill='#666666', font=font)
    
    def generate_and_save_tile(self, x, y, zoom):
        """生成并保存瓦片"""
        tile_dir = self.tiles_dir / str(zoom) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        try:
            img, has_data = self.create_real_osm_tile(x, y, zoom)
            img.save(tile_file, 'PNG', optimize=True)
            return has_data
        except Exception as e:
            print(f"  ❌ 生成瓦片失败 {zoom}/{x}/{y}: {e}")
            return False
    
    def fix_real_osm_tiles(self, workers=1):
        """修复真实OSM瓦片"""
        print(f"\n🚀 开始修复真实OSM瓦片...")
        print(f"⚙️  工作线程数: {workers}")
        
        total_tiles = 0
        success_count = 0
        start_time = time.time()
        
        # 按缩放级别修复瓦片
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            print(f"\n🔍 修复缩放级别 {zoom}...")
            
            # 计算该缩放级别下区域的瓦片范围
            min_x, max_y = self.deg2num(self.area_bbox[1], self.area_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.area_bbox[3], self.area_bbox[2], zoom)
            
            # 扩展范围以确保覆盖完整
            padding = 1 if zoom < 15 else 2
            min_x -= padding
            max_x += padding
            min_y -= padding
            max_y += padding
            
            tasks = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    tasks.append({'x': x, 'y': y, 'zoom': zoom})
            
            level_total = len(tasks)
            total_tiles += level_total
            
            print(f"  需要修复 {level_total} 个瓦片 (X: {min_x}-{max_x}, Y: {min_y}-{max_y})")
            
            # 使用单线程确保质量
            level_workers = 1
            
            # 使用线程池处理瓦片生成
            with ThreadPoolExecutor(max_workers=level_workers) as executor:
                future_to_task = {
                    executor.submit(self.generate_and_save_tile, task['x'], task['y'], task['zoom']): task 
                    for task in tasks
                }
                
                # 收集结果
                for i, future in enumerate(future_to_task):
                    task = future_to_task[future]
                    x, y, zoom = task['x'], task['y'], task['zoom']
                    try:
                        if future.result():
                            success_count += 1
                    except Exception:
                        pass
                    
                    # 显示进度
                    if (i + 1) % 100 == 0 or (i + 1) == len(tasks):
                        progress = (i + 1) / len(tasks) * 100
                        print(f"  进度: {i+1}/{len(tasks)} ({progress:.1f}%)")
            
            print(f"  ✅ 缩放级别 {zoom} 完成")
        
        elapsed_time = time.time() - start_time
        print(f"\n🎉 真实OSM瓦片修复完成!")
        print(f"📊 总计需要修复: {total_tiles} 个瓦片")
        print(f"📊 成功修复: {success_count} 个瓦片")
        if total_tiles > 0:
            success_rate = success_count / total_tiles * 100
            print(f"📊 成功率: {success_rate:.1f}%")
        print(f"⏱️  总用时: {elapsed_time:.1f} 秒")
        print(f"⚡ 平均速度: {total_tiles/elapsed_time:.1f} 瓦片/秒")
        
        return success_count >= total_tiles * 0.8

def main():
    parser = argparse.ArgumentParser(description="真实OSM瓦片修复器")
    parser.add_argument("province", nargs="?", default="北京", help="省份名称 (默认: 北京)")
    parser.add_argument("--min-zoom", type=int, default=11, help="最小缩放级别 (默认: 11)")
    parser.add_argument("--max-zoom", type=int, default=15, help="最大缩放级别 (默认: 15)")
    parser.add_argument("--workers", type=int, default=1, help="并行工作线程数 (默认: 1)")
    
    args = parser.parse_args()
    
    try:
        fixer = RealOSMTileFixer(
            province=args.province,
            min_zoom=args.min_zoom,
            max_zoom=args.max_zoom
        )
        
        success = fixer.fix_real_osm_tiles(args.workers)
        
        if success:
            print(f"\n🎉 真实OSM瓦片修复成功！")
            print(f"📁 瓦片位置: {fixer.tiles_dir}")
            print(f"💡 建议测试地图显示效果")
        else:
            print(f"\n❌ 真实OSM瓦片修复失败")
            
    except Exception as e:
        print(f"修复过程中出现错误: {e}")

if __name__ == "__main__":
    main()

