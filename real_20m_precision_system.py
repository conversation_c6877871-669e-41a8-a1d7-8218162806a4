#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的20米精度离线地图系统
使用本地OSM文件生成高精度瓦片和矢量数据
"""

import os
import sys
import json
import math
import time
import osmium
from pathlib import Path
from PIL import Image, ImageDraw
from flask import Flask, render_template, jsonify, request, send_file
import threading
from concurrent.futures import ThreadPoolExecutor
import sqlite3

class HighPrecisionOSMProcessor(osmium.SimpleHandler):
    """高精度OSM数据处理器"""
    
    def __init__(self, bbox, zoom_level):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox  # [min_lon, min_lat, max_lon, max_lat]
        self.zoom = zoom_level
        self.nodes = {}
        self.ways = []
        self.buildings = []
        self.roads = []
        self.water_features = []
        self.pois = []
        
    def node(self, n):
        """处理节点数据 - 高精度版本"""
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            
            # 保存高精度坐标（8位小数，约1.1米精度）
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 8),
                'lat': round(n.location.lat, 8),
                'tags': dict(n.tags)
            }
            
            # 如果是POI节点，添加到POI列表
            if n.tags:
                poi_tags = ['amenity', 'shop', 'tourism', 'leisure', 'office']
                if any(tag in n.tags for tag in poi_tags):
                    self.pois.append({
                        'id': n.id,
                        'lon': round(n.location.lon, 8),
                        'lat': round(n.location.lat, 8),
                        'tags': dict(n.tags)
                    })
    
    def way(self, w):
        """处理路径数据 - 高精度版本"""
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if relevant_nodes:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            # 分类存储
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)
            
            self.ways.append(way_data)

class Real20mPrecisionSystem:
    """真正的20米精度系统"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.tiles_dir = self.base_dir / "static" / "precision_tiles"
        self.vector_cache_dir = self.base_dir / "static" / "precision_vector_cache"
        self.db_path = self.base_dir / "precision_data.db"
        
        # 创建目录
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        self.vector_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        self.setup_routes()
        
    def init_database(self):
        """初始化SQLite数据库存储高精度数据"""
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()
        
        # 创建瓦片表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tiles (
                z INTEGER,
                x INTEGER,
                y INTEGER,
                data BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (z, x, y)
            )
        ''')
        
        # 创建矢量数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vector_data (
                z INTEGER,
                x INTEGER,
                y INTEGER,
                layer TEXT,
                data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (z, x, y, layer)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('real_20m_precision.html')
        
        @self.app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
        def get_tile(z, x, y):
            """获取高精度瓦片"""
            # 首先检查数据库缓存
            tile_data = self.get_cached_tile(z, x, y)
            if tile_data:
                return send_file(tile_data, mimetype='image/png')
            
            # 生成新瓦片
            tile_path = self.generate_precision_tile(z, x, y)
            if tile_path and tile_path.exists():
                return send_file(str(tile_path), mimetype='image/png')
            
            # 返回空瓦片
            return self.create_empty_tile()
        
        @self.app.route('/api/vector-data/<int:z>/<int:x>/<int:y>')
        def get_vector_data(z, x, y):
            """获取矢量数据"""
            vector_data = self.get_precision_vector_data(z, x, y)
            return jsonify(vector_data)
        
        @self.app.route('/api/generate-area')
        def generate_area():
            """生成指定区域的高精度数据"""
            lat = request.args.get('lat', type=float)
            lon = request.args.get('lon', type=float)
            radius = request.args.get('radius', 1000, type=int)  # 默认1000米
            
            if lat is None or lon is None:
                return jsonify({'error': '缺少坐标参数'}), 400
            
            # 异步生成区域数据
            threading.Thread(
                target=self.generate_area_data,
                args=(lat, lon, radius)
            ).start()
            
            return jsonify({
                'status': 'started',
                'message': f'开始生成 ({lat}, {lon}) 周围 {radius}米 的高精度数据'
            })
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def generate_precision_tile(self, z, x, y):
        """生成高精度瓦片"""
        try:
            bbox = self.get_tile_bbox(x, y, z)
            
            # 使用OSM处理器提取数据
            processor = HighPrecisionOSMProcessor(bbox, z)
            processor.apply_file(str(self.osm_file))
            
            # 创建高分辨率图像（512x512用于更高精度）
            img_size = 512 if z >= 16 else 256
            img = Image.new('RGB', (img_size, img_size), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换函数
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * img_size)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * img_size)
                return (max(0, min(img_size-1, px)), max(0, min(img_size-1, py)))
            
            # 绘制水体
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4')
            
            # 绘制建筑物
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#dddddd', outline='#999999')
            
            # 绘制道路
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 根据道路类型设置样式
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = max(2, 8 - (18 - z))
                elif highway_type in ['primary', 'secondary']:
                    color = '#ffcc00'
                    width = max(1, 6 - (18 - z))
                elif highway_type in ['tertiary', 'residential']:
                    color = '#ffffff'
                    width = max(1, 4 - (18 - z))
                else:
                    color = '#cccccc'
                    width = 1
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路线段
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 绘制POI
            for poi in processor.pois:
                px, py = geo_to_pixel(poi['lon'], poi['lat'])
                draw.ellipse([px-2, py-2, px+2, py+2], fill='#ff0000')
            
            # 保存瓦片
            tile_path = self.tiles_dir / str(z) / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            # 如果是高精度瓦片，缩放到标准尺寸
            if img_size > 256:
                img = img.resize((256, 256), Image.LANCZOS)
            
            img.save(tile_file, 'PNG', optimize=True)
            
            # 缓存到数据库
            self.cache_tile(z, x, y, tile_file)
            
            return tile_file
            
        except Exception as e:
            print(f"生成瓦片失败 {z}/{x}/{y}: {e}")
            return None
    
    def get_cached_tile(self, z, x, y):
        """从数据库获取缓存的瓦片"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute('SELECT data FROM tiles WHERE z=? AND x=? AND y=?', (z, x, y))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                import io
                return io.BytesIO(result[0])
            return None
        except:
            return None
    
    def cache_tile(self, z, x, y, tile_file):
        """缓存瓦片到数据库"""
        try:
            with open(tile_file, 'rb') as f:
                tile_data = f.read()
            
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute(
                'INSERT OR REPLACE INTO tiles (z, x, y, data) VALUES (?, ?, ?, ?)',
                (z, x, y, tile_data)
            )
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"缓存瓦片失败: {e}")
    
    def create_empty_tile(self):
        """创建空瓦片"""
        img = Image.new('RGB', (256, 256), color='#f0f0f0')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), "No Data", fill='#999999')

        import io
        img_io = io.BytesIO()
        img.save(img_io, 'PNG')
        img_io.seek(0)
        return img_io

    def get_precision_vector_data(self, z, x, y):
        """获取高精度矢量数据"""
        try:
            bbox = self.get_tile_bbox(x, y, z)

            # 检查缓存
            cached_data = self.get_cached_vector_data(z, x, y)
            if cached_data:
                return cached_data

            # 生成新的矢量数据
            processor = HighPrecisionOSMProcessor(bbox, z)
            processor.apply_file(str(self.osm_file))

            vector_data = {
                'roads': self.convert_to_geojson(processor.roads, processor.nodes, 'LineString'),
                'buildings': self.convert_to_geojson(processor.buildings, processor.nodes, 'Polygon'),
                'pois': self.convert_pois_to_geojson(processor.pois),
                'water': self.convert_to_geojson(processor.water_features, processor.nodes, 'Polygon')
            }

            # 缓存数据
            self.cache_vector_data(z, x, y, vector_data)

            return vector_data

        except Exception as e:
            print(f"获取矢量数据失败 {z}/{x}/{y}: {e}")
            return {
                'roads': {'type': 'FeatureCollection', 'features': []},
                'buildings': {'type': 'FeatureCollection', 'features': []},
                'pois': {'type': 'FeatureCollection', 'features': []},
                'water': {'type': 'FeatureCollection', 'features': []}
            }

    def convert_to_geojson(self, ways, nodes, geometry_type):
        """转换为GeoJSON格式"""
        features = []

        for way in ways:
            coordinates = []
            for node_id in way['nodes']:
                if node_id in nodes:
                    node = nodes[node_id]
                    coordinates.append([node['lon'], node['lat']])

            if len(coordinates) < 2:
                continue

            if geometry_type == 'Polygon':
                if coordinates[0] != coordinates[-1]:
                    coordinates.append(coordinates[0])
                geometry = {
                    'type': 'Polygon',
                    'coordinates': [coordinates]
                }
            else:  # LineString
                geometry = {
                    'type': 'LineString',
                    'coordinates': coordinates
                }

            feature = {
                'type': 'Feature',
                'geometry': geometry,
                'properties': way['tags']
            }
            features.append(feature)

        return {
            'type': 'FeatureCollection',
            'features': features
        }

    def convert_pois_to_geojson(self, pois):
        """转换POI为GeoJSON格式"""
        features = []

        for poi in pois:
            feature = {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': [poi['lon'], poi['lat']]
                },
                'properties': poi['tags']
            }
            features.append(feature)

        return {
            'type': 'FeatureCollection',
            'features': features
        }

    def get_cached_vector_data(self, z, x, y):
        """获取缓存的矢量数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            result = {}
            for layer in ['roads', 'buildings', 'pois', 'water']:
                cursor.execute(
                    'SELECT data FROM vector_data WHERE z=? AND x=? AND y=? AND layer=?',
                    (z, x, y, layer)
                )
                row = cursor.fetchone()
                if row:
                    result[layer] = json.loads(row[0])
                else:
                    result[layer] = {'type': 'FeatureCollection', 'features': []}

            conn.close()
            return result if any(result[layer]['features'] for layer in result) else None

        except Exception as e:
            print(f"获取缓存矢量数据失败: {e}")
            return None

    def cache_vector_data(self, z, x, y, vector_data):
        """缓存矢量数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            for layer, data in vector_data.items():
                cursor.execute(
                    'INSERT OR REPLACE INTO vector_data (z, x, y, layer, data) VALUES (?, ?, ?, ?, ?)',
                    (z, x, y, layer, json.dumps(data))
                )

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"缓存矢量数据失败: {e}")

    def generate_area_data(self, lat, lon, radius):
        """生成指定区域的高精度数据"""
        print(f"开始生成区域数据: ({lat}, {lon}) 半径 {radius}米")

        # 计算需要生成的瓦片范围
        zoom_levels = [16, 17, 18]  # 高精度级别

        for zoom in zoom_levels:
            # 计算中心瓦片
            center_x, center_y = self.deg2num(lat, lon, zoom)

            # 计算半径对应的瓦片数量
            tile_radius = max(1, int(radius / (40075000 / (2 ** zoom) / 256)))

            print(f"缩放级别 {zoom}: 生成 {(tile_radius*2+1)**2} 个瓦片")

            # 生成瓦片
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []

                for dx in range(-tile_radius, tile_radius + 1):
                    for dy in range(-tile_radius, tile_radius + 1):
                        tile_x = center_x + dx
                        tile_y = center_y + dy

                        # 检查瓦片坐标有效性
                        max_coord = 2 ** zoom
                        if 0 <= tile_x < max_coord and 0 <= tile_y < max_coord:
                            future = executor.submit(self.generate_precision_tile, zoom, tile_x, tile_y)
                            futures.append(future)

                # 等待完成
                for future in futures:
                    try:
                        future.result(timeout=30)
                    except Exception as e:
                        print(f"生成瓦片失败: {e}")

        print(f"区域数据生成完成: ({lat}, {lon})")
    
    def run(self, host='127.0.0.1', port=5003, debug=False):
        """运行系统"""
        print("🎯 真正的20米精度离线地图系统")
        print("=" * 60)
        print(f"🌐 系统地址: http://{host}:{port}")
        print(f"📁 OSM文件: {self.osm_file}")
        print(f"📁 瓦片目录: {self.tiles_dir}")
        print("🎯 特性:")
        print("  - 真正的20米精度")
        print("  - 本地OSM数据处理")
        print("  - 高精度瓦片生成")
        print("  - 8位小数坐标精度")
        print("  - SQLite数据缓存")
        print("=" * 60)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return
        
        self.app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    system = Real20mPrecisionSystem()
    system.run()
