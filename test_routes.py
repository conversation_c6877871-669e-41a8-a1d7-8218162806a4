#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路由是否正确注册
"""

from integrated_vector_system import IntegratedVectorSystem

def test_routes():
    """测试路由注册"""
    print("🔍 测试路由注册")
    print("=" * 50)
    
    # 创建系统实例
    system = IntegratedVectorSystem()
    
    # 获取所有路由
    routes = []
    for rule in system.app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': rule.rule
        })
    
    print(f"📊 总共注册了 {len(routes)} 个路由:")
    print()
    
    for route in routes:
        if 'api' in route['rule']:
            print(f"  ✅ {route['rule']} -> {route['endpoint']} {route['methods']}")
    
    # 检查特定路由
    api_routes = [r for r in routes if 'api' in r['rule']]
    expected_routes = [
        '/api/regions',
        '/api/search-poi', 
        '/api/quick-search',
        '/api/famous-places',
        '/api/statistics'
    ]
    
    print(f"\n🔍 检查关键API路由:")
    for expected in expected_routes:
        found = any(expected in route['rule'] for route in api_routes)
        status = "✅" if found else "❌"
        print(f"  {status} {expected}")

if __name__ == "__main__":
    test_routes()
