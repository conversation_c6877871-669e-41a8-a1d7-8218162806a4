#!/usr/bin/env python3

# 手动创建一个测试瓦片
from PIL import Image, ImageDraw
from pathlib import Path

# 创建一个简单的测试瓦片
img = Image.new('RGB', (512, 512), color='#f0f0f0')
draw = ImageDraw.Draw(img)

# 绘制一些测试内容
draw.rectangle([50, 50, 462, 462], fill='#e0e0e0', outline='#999999', width=2)
draw.text((256, 256), "Level 20 Test", fill='#333333', anchor='mm')

# 保存到20级瓦片目录
level20_dir = Path("static/tiles/20")
tile_dir = level20_dir / "863312"
tile_dir.mkdir(parents=True, exist_ok=True)

tile_file = tile_dir / "397328.png"
img.save(tile_file, 'PNG')

print(f"创建测试瓦片: {tile_file}")
print(f"文件大小: {tile_file.stat().st_size} bytes")
print("完成！")
