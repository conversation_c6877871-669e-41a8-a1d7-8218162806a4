<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #ccc;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🗺️ 简单地图测试</h1>
    
    <div class="info">
        <h3>测试信息:</h3>
        <p>📍 中心点: 北京 (39.9042, 116.4074)</p>
        <p>🔍 缩放级别: 10-18</p>
        <p>🌐 瓦片服务器: http://127.0.0.1:8080</p>
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <h3>说明:</h3>
        <p>如果地图显示正常，说明瓦片服务器工作正常。</p>
        <p>如果地图不显示，请检查:</p>
        <ul>
            <li>瓦片服务器是否运行在 http://127.0.0.1:8080</li>
            <li>浏览器控制台是否有错误信息</li>
            <li>网络连接是否正常</li>
        </ul>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京坐标
            zoom: 15,
            minZoom: 10,
            maxZoom: 18
        });

        // 添加离线瓦片图层
        const offlineLayer = L.tileLayer('http://127.0.0.1:8080/tiles/{z}/{x}/{y}.png', {
            attribution: '© 本地离线地图',
            minZoom: 10,
            maxZoom: 18
        });

        // 添加图层到地图
        offlineLayer.addTo(map);

        // 添加标记
        const marker = L.marker([39.9042, 116.4074]).addTo(map);
        marker.bindPopup('北京天安门').openPopup();

        // 监听地图事件
        map.on('load', function() {
            console.log('地图加载完成');
        });

        map.on('tileload', function(e) {
            console.log('瓦片加载成功:', e.tile.src);
        });

        map.on('tileerror', function(e) {
            console.error('瓦片加载失败:', e.tile.src);
        });

        // 显示地图信息
        map.on('zoomend', function() {
            console.log('当前缩放级别:', map.getZoom());
        });

        map.on('moveend', function() {
            const center = map.getCenter();
            console.log('地图中心:', center.lat, center.lng);
        });
    </script>
</body>
</html>
