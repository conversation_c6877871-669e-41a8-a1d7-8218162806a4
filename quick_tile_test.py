#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试瓦片下载修复
"""

from download_china_18 import ChinaTileDownloader

def test_baidu_tile_download():
    """测试百度地图瓦片下载"""
    print("🧪 测试百度地图瓦片下载")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    downloader.set_source('baidu')
    
    # 测试下载一个北京市的瓦片
    test_x, test_y = 215837, 99333  # 北京市中心附近的瓦片
    test_z = 18
    
    print(f"测试瓦片: {test_z}/{test_x}/{test_y}")
    
    success, message = downloader.download_tile(test_x, test_y, test_z)
    print(f"结果: {message}")
    
    if success:
        print("✅ 百度地图瓦片下载成功!")
    else:
        print("❌ 百度地图瓦片下载失败")
    
    return success

def test_amap_tile_download():
    """测试高德地图瓦片下载"""
    print("\n🧪 测试高德地图瓦片下载")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    downloader.set_source('amap')
    
    # 测试下载一个北京市的瓦片
    test_x, test_y = 215837, 99333  # 北京市中心附近的瓦片
    test_z = 18
    
    print(f"测试瓦片: {test_z}/{test_x}/{test_y}")
    
    success, message = downloader.download_tile(test_x, test_y, test_z)
    print(f"结果: {message}")
    
    if success:
        print("✅ 高德地图瓦片下载成功!")
    else:
        print("❌ 高德地图瓦片下载失败")
    
    return success

def main():
    """主函数"""
    print("🧪 快速瓦片下载测试")
    print("=" * 50)
    
    # 测试百度地图
    baidu_success = test_baidu_tile_download()
    
    # 测试高德地图
    amap_success = test_amap_tile_download()
    
    print(f"\n📊 测试结果:")
    print(f"   百度地图: {'✅ 成功' if baidu_success else '❌ 失败'}")
    print(f"   高德地图: {'✅ 成功' if amap_success else '❌ 失败'}")
    
    if baidu_success and amap_success:
        print("\n🎉 所有测试通过! 可以开始下载省市级瓦片了")
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
