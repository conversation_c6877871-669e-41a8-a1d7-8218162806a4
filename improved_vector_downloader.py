#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的矢量数据下载器 - 解决超时问题
支持重试机制、断点续传、并发控制
"""

import os
import time
import json
import math
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import xml.etree.ElementTree as ET

class ImprovedVectorDownloader:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.timeout = 120  # 增加超时时间到120秒
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 5  # 重试延迟
        self.max_workers = 8  # 增加并发数，提高下载速度
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建进度记录文件
        self.progress_file = "download_progress.json"
        self.load_progress()
    
    def load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                self.progress = json.load(f)
        else:
            self.progress = {}
    
    def save_progress(self):
        """保存下载进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def get_overpass_query(self, bbox, data_type):
        """构建Overpass查询语句"""
        if data_type == "roads":
            return f"""
            [out:xml][timeout:300];
            (
              way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)"]({bbox});
              way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)_link"]({bbox});
            );
            out geom;
            """
        elif data_type == "buildings":
            return f"""
            [out:xml][timeout:300];
            (
              way["building"]({bbox});
              way["building:part"]({bbox});
              relation["building"]({bbox});
              way["amenity"~"^(school|hospital|university|restaurant|hotel|bank|police|park|garden)"]({bbox});
              way["shop"]({bbox});
              way["office"]({bbox});
              way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool)"]({bbox});
            );
            out geom;
            """
        elif data_type == "pois":
            return f"""
            [out:xml][timeout:300];
            (
              node["amenity"]({bbox});
              node["shop"]({bbox});
              node["tourism"]({bbox});
              node["leisure"]({bbox});
              node["office"]({bbox});
              node["historic"]({bbox});
              node["natural"]({bbox});
            );
            out;
            """
        else:
            return f"""
            [out:xml][timeout:300];
            (
              way["highway"]({bbox});
              way["building"]({bbox});
              node["amenity"]({bbox});
            );
            out geom;
            """
    
    def download_with_retry(self, url, data, max_retries=None):
        """带重试机制的下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt + 1}次)...")
                
                # 使用更长的超时时间
                response = requests.post(
                    url, 
                    data=data, 
                    timeout=self.timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/xml, text/xml, */*',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                )
                
                if response.status_code == 200:
                    return response
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                print(f"  等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        return None
    
    def download_region_data(self, region_name, bbox, data_types):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:  # 文件大小大于1KB
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            # 构建查询
            query = self.get_overpass_query(bbox, data_type)
            
            # 下载数据
            response = self.download_with_retry(self.base_url, {"data": query})
            
            if response and response.status_code == 200:
                # 保存数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                file_size = file_path.stat().st_size
                print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                results[data_type] = "成功"
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "completed"
                self.save_progress()
                
            else:
                print(f"  ❌ {data_type} 数据下载失败")
                results[data_type] = "失败"
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "failed"
                self.save_progress()
        
        return results
    
    def download_provinces(self, provinces, data_types):
        """下载多个省份数据"""
        print(f"🚀 开始下载 {len(provinces)} 个省份的矢量数据")
        print(f"📊 数据类型: {', '.join(data_types)}")
        print(f"⚙️ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_region = {}
            for region_name, bbox in provinces.items():
                future = executor.submit(self.download_region_data, region_name, bbox, data_types)
                future_to_region[future] = region_name
            
            # 处理结果
            for future in as_completed(future_to_region):
                region_name = future_to_region[future]
                try:
                    result = future.result()
                    results[region_name] = result
                except Exception as e:
                    print(f"❌ {region_name} 下载异常: {e}")
                    results[region_name] = {"error": str(e)}
        
        return results
    
    def get_download_status(self):
        """获取下载状态"""
        status = {
            "completed": 0,
            "failed": 0,
            "pending": 0,
            "details": {}
        }
        
        for key, value in self.progress.items():
            if value == "completed":
                status["completed"] += 1
            elif value == "failed":
                status["failed"] += 1
            else:
                status["pending"] += 1
            
            status["details"][key] = value
        
        return status
    
    def retry_failed_downloads(self, provinces, data_types):
        """重试失败的下载"""
        print("🔄 重试失败的下载...")
        
        failed_downloads = []
        for key, value in self.progress.items():
            if value == "failed":
                failed_downloads.append(key)
        
        if not failed_downloads:
            print("✅ 没有失败的下载需要重试")
            return
        
        print(f"📋 发现 {len(failed_downloads)} 个失败的下载")
        
        for failed_key in failed_downloads:
            # 解析失败的下载
            parts = failed_key.split('_')
            if len(parts) >= 2:
                region_name = parts[0]
                data_type = parts[1]
                
                if region_name in provinces:
                    print(f"\n🔄 重试 {region_name} 的 {data_type} 数据...")
                    
                    # 删除失败的文件
                    file_path = self.data_dir / region_name / f"{data_type}.osm"
                    if file_path.exists():
                        file_path.unlink()
                    
                    # 重新下载
                    bbox = provinces[region_name]
                    result = self.download_region_data(region_name, bbox, [data_type])
                    
                    if result.get(data_type) == "成功":
                        print(f"✅ {region_name} 的 {data_type} 数据重试成功")
                    else:
                        print(f"❌ {region_name} 的 {data_type} 数据重试失败")

def main():
    """主函数"""
    downloader = ImprovedVectorDownloader()
    
    # 省份配置
    provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0",
        "湖北": "108.0,29.0,117.0,33.0",
        "湖南": "108.0,24.0,114.0,30.0"
    }
    
    # 数据类型
    data_types = ["roads", "buildings", "pois"]
    
    print("🎯 改进的矢量数据下载器")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 下载省份数据")
        print("2. 查看下载状态")
        print("3. 重试失败下载")
        print("4. 清理进度文件")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            print(f"\n🚀 开始下载 {len(provinces)} 个省份的数据...")
            results = downloader.download_provinces(provinces, data_types)
            
            print("\n📊 下载结果汇总:")
            for region, result in results.items():
                print(f"\n{region}:")
                for data_type, status in result.items():
                    if status == "成功":
                        print(f"  ✅ {data_type}: {status}")
                    elif status == "失败":
                        print(f"  ❌ {data_type}: {status}")
                    else:
                        print(f"  ⏭️ {data_type}: {status}")
        
        elif choice == "2":
            status = downloader.get_download_status()
            print(f"\n📊 下载状态:")
            print(f"✅ 已完成: {status['completed']}")
            print(f"❌ 失败: {status['failed']}")
            print(f"⏳ 待处理: {status['pending']}")
            
            if status['details']:
                print(f"\n📋 详细信息:")
                for key, value in status['details'].items():
                    print(f"  {key}: {value}")
        
        elif choice == "3":
            downloader.retry_failed_downloads(provinces, data_types)
        
        elif choice == "4":
            if os.path.exists(downloader.progress_file):
                os.remove(downloader.progress_file)
                print("✅ 进度文件已清理")
            else:
                print("ℹ️ 进度文件不存在")
        
        elif choice == "5":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
