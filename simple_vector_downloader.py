#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化矢量数据下载工具
专门用于下载省市级矢量地图数据，避免超时问题
"""

import os
import requests
import json
import time
from pathlib import Path

class SimpleVectorDownloader:
    def __init__(self, output_dir="static/vector_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 简化的数据源配置
        self.overpass_url = 'https://overpass-api.de/api/interpreter'
        
        # 省市级区域配置
        self.provinces = {
            '1': {'name': '北京市', 'bbox': (115.7, 39.4, 117.4, 41.6)},
            '2': {'name': '上海市', 'bbox': (120.9, 30.7, 122.1, 31.9)},
            '3': {'name': '天津市', 'bbox': (116.7, 38.6, 118.1, 40.2)},
            '4': {'name': '重庆市', 'bbox': (105.3, 28.2, 110.2, 32.2)},
            '5': {'name': '广东省', 'bbox': (109.7, 20.2, 117.2, 25.5)},
            '6': {'name': '江苏省', 'bbox': (116.2, 30.7, 121.9, 35.2)},
            '7': {'name': '浙江省', 'bbox': (118.0, 27.0, 123.0, 31.5)},
            '8': {'name': '山东省', 'bbox': (114.5, 34.4, 122.7, 38.4)},
            '9': {'name': '河南省', 'bbox': (110.4, 31.2, 116.7, 36.4)},
            '10': {'name': '四川省', 'bbox': (97.3, 26.0, 108.5, 34.3)}
        }
        
        self.cities = {
            '1': {'name': '北京市', 'bbox': (116.0, 39.4, 117.0, 40.2)},
            '2': {'name': '上海市', 'bbox': (121.2, 31.0, 121.8, 31.4)},
            '3': {'name': '广州市', 'bbox': (113.0, 22.7, 113.6, 23.4)},
            '4': {'name': '深圳市', 'bbox': (113.7, 22.4, 114.4, 22.8)},
            '5': {'name': '杭州市', 'bbox': (119.8, 30.0, 120.5, 30.5)},
            '6': {'name': '南京市', 'bbox': (118.4, 31.8, 119.2, 32.3)},
            '7': {'name': '成都市', 'bbox': (103.8, 30.4, 104.3, 30.8)},
            '8': {'name': '武汉市', 'bbox': (114.0, 30.4, 114.6, 30.8)},
            '9': {'name': '西安市', 'bbox': (108.7, 34.0, 109.2, 34.4)},
            '10': {'name': '重庆市', 'bbox': (106.2, 29.2, 107.0, 29.8)}
        }
    
    def download_roads_data(self, bbox, output_file):
        """下载高精度道路数据"""
        print(f"🛣️  下载高精度道路数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 高精度道路查询 - 包含所有道路类型
        query = f"""
        [out:xml][timeout:60];
        (
          way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["highway"~"^(motorway_link|trunk_link|primary_link|secondary_link|tertiary_link)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "高精度道路数据")
    
    def download_buildings_data(self, bbox, output_file):
        """下载高精度建筑物数据"""
        print(f"🏢 下载高精度建筑物数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 高精度建筑物查询 - 包含所有建筑物类型和详细信息
        query = f"""
        [out:xml][timeout:60];
        (
          way["building"]({min_lat},{min_lon},{max_lat},{max_lon});
          relation["building"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["building:part"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["amenity"~"^(school|hospital|university|college|library|museum|theatre|cinema|restaurant|hotel|bank|post_office|police|fire_station|townhall)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["shop"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["office"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool|golf_course)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "高精度建筑物数据")
    
    def download_water_data(self, bbox, output_file):
        """下载高精度水体数据"""
        print(f"💧 下载高精度水体数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 高精度水体查询 - 包含所有水体类型
        query = f"""
        [out:xml][timeout:60];
        (
          way["waterway"~"^(river|stream|canal|drain|ditch|brook|tributary|riverbank|rapids|waterfall|lock|weir|dam)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["natural"~"^(water|coastline|bay|strait|channel|reef)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["water"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["landuse"~"^(reservoir|basin|pond|lagoon)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["leisure"~"^(marina|swimming_pool|water_park)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["amenity"~"^(fountain|drinking_water)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "高精度水体数据")
    
    def _execute_query(self, query, output_file, data_type):
        """执行Overpass查询"""
        try:
            print(f"   📡 正在查询{data_type}...")
            
            response = requests.post(
                self.overpass_url,
                data=query,
                timeout=30,  # 短超时时间
                headers={'User-Agent': 'SimpleVectorDownloader/1.0'},
                stream=True
            )
            response.raise_for_status()
            
            # 流式保存数据
            total_size = 0
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        total_size += len(chunk)
            
            print(f"   ✅ {data_type}下载完成: {total_size:,} bytes")
            return True, f"{data_type}下载成功 ({total_size:,} bytes)"
            
        except requests.exceptions.Timeout:
            return False, f"{data_type}下载超时"
        except Exception as e:
            return False, f"{data_type}下载失败: {e}"
    
    def download_poi_data(self, bbox, output_file):
        """下载高精度POI数据"""
        print(f"📍 下载高精度POI数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 高精度POI查询 - 包含所有兴趣点
        query = f"""
        [out:xml][timeout:60];
        (
          node["amenity"~"^(restaurant|fast_food|cafe|bar|pub|hospital|clinic|pharmacy|school|university|college|library|museum|theatre|cinema|bank|atm|post_office|police|fire_station|townhall|embassy|place_of_worship|fuel|parking|toilets|drinking_water|fountain|bench|waste_basket|recycling|vending_machine)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["shop"~"^(supermarket|convenience|bakery|butcher|clothes|shoes|electronics|furniture|hardware|books|gift|jewelry|beauty|hairdresser|car|bicycle|motorcycle|mobile_phone|computer|optician|florist|toy|sports|outdoor|pet|art|craft|stationery|newsagent|tobacco|alcohol|seafood|organic|frozen_food|confectionery|tea|coffee|wine|cheese|deli|farm)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["tourism"~"^(hotel|motel|hostel|guest_house|apartment|camp_site|caravan_site|attraction|museum|gallery|zoo|theme_park|aquarium|information|artwork|viewpoint|picnic_site)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["leisure"~"^(park|garden|playground|sports_centre|swimming_pool|golf_course|stadium|pitch|track|fitness_centre|bowling_alley|dance|hackerspace|ice_rink|marina|miniature_golf|outdoor_seating|park|pitch|sports_centre|stadium|swimming_pool|water_park)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["office"~"^(company|corporation|ngo|political_party|religion|research|tax_advisor|travel_agent|estate_agent|insurance|lawyer|accountant|government|diplomatic|educational|financial|it|telecommunication|consulting|advertising|architect|notary|quango)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "高精度POI数据")
    
    def download_transport_data(self, bbox, output_file):
        """下载高精度交通数据"""
        print(f"🚌 下载高精度交通数据")
        print(f"   边界框: {bbox}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 高精度交通查询
        query = f"""
        [out:xml][timeout:60];
        (
          node["public_transport"~"^(station|stop_position|platform|stop_area)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["railway"~"^(station|halt|tram_stop|subway_entrance|platform)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["highway"~"^(bus_stop|tram_stop)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["aeroway"~"^(aerodrome|helipad|heliport)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["amenity"~"^(fuel|parking|taxi|car_rental|car_wash|car_repair|bicycle_rental|bicycle_repair_station)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["railway"~"^(rail|tram|subway|light_rail|monorail|narrow_gauge|preserved|funicular|miniature)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["aeroway"~"^(runway|taxiway|apron|helipad)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "高精度交通数据")
    
    def download_region_data(self, region_name, bbox, data_types=None):
        """下载区域数据"""
        if data_types is None:
            data_types = ['roads', 'buildings', 'water', 'poi', 'transport']
        
        print(f"🗺️  下载{region_name}高精度矢量数据")
        
        # 创建区域目录
        region_dir = self.output_dir / region_name
        region_dir.mkdir(parents=True, exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            if data_type == 'roads':
                output_file = region_dir / f"{region_name}_roads.xml"
                success, message = self.download_roads_data(bbox, output_file)
            elif data_type == 'buildings':
                output_file = region_dir / f"{region_name}_buildings.xml"
                success, message = self.download_buildings_data(bbox, output_file)
            elif data_type == 'water':
                output_file = region_dir / f"{region_name}_water.xml"
                success, message = self.download_water_data(bbox, output_file)
            elif data_type == 'poi':
                output_file = region_dir / f"{region_name}_poi.xml"
                success, message = self.download_poi_data(bbox, output_file)
            elif data_type == 'transport':
                output_file = region_dir / f"{region_name}_transport.xml"
                success, message = self.download_transport_data(bbox, output_file)
            else:
                continue
            
            results[data_type] = (success, message)
            
            if success:
                print(f"   ✅ {message}")
            else:
                print(f"   ❌ {message}")
            
            # 避免请求过快
            time.sleep(2)
        
        return results
    
    def show_provinces(self):
        """显示省份列表"""
        print("\n📋 请选择省份/直辖市:")
        print("-" * 40)
        
        for key, province in self.provinces.items():
            print(f"{key:2}. {province['name']}")
        
        print("0. 返回主菜单")
    
    def show_cities(self):
        """显示城市列表"""
        print("\n📋 请选择城市:")
        print("-" * 40)
        
        for key, city in self.cities.items():
            print(f"{key:2}. {city['name']}")
        
        print("0. 返回主菜单")
    
    def show_data_types(self):
        """显示数据类型选项"""
        print("\n📋 请选择数据类型:")
        print("-" * 40)
        print("1. 道路数据 (高精度)")
        print("2. 建筑物数据 (高精度)")
        print("3. 水体数据 (高精度)")
        print("4. POI数据 (兴趣点)")
        print("5. 交通数据 (高精度)")
        print("6. 全部数据 (最高精度)")
        print("0. 返回主菜单")
    
    def get_data_types(self, choice):
        """根据选择获取数据类型"""
        if choice == '1':
            return ['roads']
        elif choice == '2':
            return ['buildings']
        elif choice == '3':
            return ['water']
        elif choice == '4':
            return ['poi']
        elif choice == '5':
            return ['transport']
        elif choice == '6':
            return ['roads', 'buildings', 'water', 'poi', 'transport']
        else:
            return ['roads', 'buildings', 'water', 'poi', 'transport']

def main():
    """主函数"""
    print("🗺️  简化矢量数据下载工具")
    print("=" * 50)
    
    downloader = SimpleVectorDownloader()
    
    while True:
        print("\n" + "="*60)
        print("🗺️  简化矢量数据下载工具")
        print("="*60)
        print("1. 下载省级矢量数据")
        print("2. 下载市级矢量数据")
        print("3. 测试矢量数据下载")
        print("0. 退出")
        print("="*60)
        
        choice = input("请选择功能 (0-3): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            # 下载省级矢量数据
            downloader.show_provinces()
            province_choice = input("\n请选择省份 (输入编号): ").strip()
            
            if province_choice == '0':
                continue
            
            if province_choice not in downloader.provinces:
                print("❌ 无效选择")
                continue
            
            province = downloader.provinces[province_choice]
            print(f"\n已选择: {province['name']}")
            
            # 选择数据类型
            downloader.show_data_types()
            data_choice = input("\n请选择数据类型 (默认: 4): ").strip() or "4"
            data_types = downloader.get_data_types(data_choice)
            
            downloader.download_region_data(province['name'], province['bbox'], data_types)
            
        elif choice == '2':
            # 下载市级矢量数据
            downloader.show_cities()
            city_choice = input("\n请选择城市 (输入编号): ").strip()
            
            if city_choice == '0':
                continue
            
            if city_choice not in downloader.cities:
                print("❌ 无效选择")
                continue
            
            city = downloader.cities[city_choice]
            print(f"\n已选择: {city['name']}")
            
            # 选择数据类型
            downloader.show_data_types()
            data_choice = input("\n请选择数据类型 (默认: 4): ").strip() or "4"
            data_types = downloader.get_data_types(data_choice)
            
            downloader.download_region_data(city['name'], city['bbox'], data_types)
            
        elif choice == '3':
            # 测试矢量数据下载
            print("🧪 测试矢量数据下载")
            test_bbox = (116.0, 39.4, 117.0, 40.2)  # 北京市
            test_file = downloader.output_dir / "test_beijing_roads.xml"
            success, message = downloader.download_roads_data(test_bbox, test_file)
            print(f"测试结果: {message}")
            
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")
    
    print(f"\n✅ 完成! 数据保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
