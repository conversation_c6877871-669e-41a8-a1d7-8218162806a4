# 离线GPS机井监控系统使用指南

## 系统概述

本系统专为内网环境下的野外机井作业监控设计，完全支持离线运行，使用本地瓦片地图，无需互联网连接。

## 离线模式特点

### 🗺️ 本地地图系统
- **完全离线**: 不依赖互联网连接
- **本地瓦片**: 使用预下载的瓦片文件
- **快速加载**: 本地文件访问速度快
- **数据安全**: 所有数据在内网环境运行

### 🎯 GPS定位功能
- **精确GPS定位**: 支持6位小数精度的GPS坐标定位
- **当前位置获取**: 自动获取浏览器当前位置
- **附近机井搜索**: 根据GPS坐标搜索指定范围内的机井
- **实时轨迹追踪**: 记录和显示机井GPS历史轨迹

### 🔧 机井监控功能
- **实时状态监控**: 监控机井运行状态（正常/警告/故障）
- **气体浓度检测**: 实时显示气体检测仪数据
- **送风机控制**: 监控送风机开关状态
- **历史数据记录**: 保存GPS轨迹和传感器数据历史

## 系统架构

```
离线GPS机井监控系统
├── 瓦片服务器 (端口: 8080)
│   ├── 本地瓦片文件 (static/tiles/)
│   ├── 地图配置 (map_config.json)
│   └── 瓦片服务接口
├── GPS监控系统 (端口: 5000)
│   ├── 机井数据管理
│   ├── GPS定位服务
│   ├── 传感器数据处理
│   └── Web界面服务
└── 数据存储
    ├── 机井信息
    ├── GPS历史轨迹
    └── 传感器数据
```

## 系统启动

### 1. 启动离线系统
```bash
python start_offline_gps_monitoring.py
```

此脚本会：
- 自动启动瓦片服务器 (端口: 8080)
- 启动GPS监控系统 (端口: 5000)
- 自动打开浏览器访问系统

### 2. 手动启动（可选）
如果需要分别启动服务：

```bash
# 启动瓦片服务器
python tileserver.py

# 启动GPS监控系统
python start_gps_monitoring.py
```

### 3. 启动数据模拟器（可选）
```bash
python gps_data_simulator.py
```

用于模拟PLC设备发送GPS和传感器数据。

## 离线地图配置

### 瓦片文件结构
```
static/tiles/
├── 8/
│   ├── 100/
│   │   ├── 50.png
│   │   └── 51.png
│   └── 101/
├── 9/
├── 10/
└── ...
```

### 地图配置 (map_config.json)
```json
{
  "tiles_dir": "static\\tiles",
  "min_zoom": 8,
  "max_zoom": 15,
  "center": [116.4074, 39.9042],
  "bounds": {
    "north": 54.0,
    "south": 18.0,
    "east": 135.0,
    "west": 73.0
  },
  "status": "ready",
  "last_updated": "2025-09-04 23:57:42"
}
```

## 使用说明

### GPS定位操作

1. **手动输入GPS坐标**
   - 在左侧面板的"GPS定位"区域
   - 输入纬度和经度坐标
   - 设置搜索半径（默认1000米）
   - 点击"📍 定位到GPS坐标"

2. **获取当前位置**
   - 点击"🌍 获取当前位置"按钮
   - 浏览器会请求位置权限
   - 自动获取并定位到当前位置

3. **搜索附近机井**
   - 定位后系统会自动搜索指定范围内的机井
   - 显示距离和机井信息
   - 在地图上标记附近机井位置

### 机井管理操作

1. **添加新机井**
   - 填写机井ID、名称
   - 输入GPS坐标（纬度、经度）
   - 点击"➕ 添加机井"

2. **更新机井GPS**
   - 输入机井ID
   - 输入新的GPS坐标
   - 点击"🔄 更新GPS"

3. **搜索机井**
   - 在搜索框输入机井名称或ID
   - 系统会实时显示搜索结果
   - 点击结果可定位到对应机井

### 地图操作

1. **离线地图特点**
   - 使用本地瓦片文件，加载速度快
   - 支持缩放级别 8-15
   - 覆盖中国全境范围
   - 无需网络连接

2. **机井标记说明**
   - 🟢 绿色：正常状态（气体浓度<50%）
   - 🟡 黄色：警告状态（气体浓度50-80%）
   - 🔴 红色：危险状态（气体浓度>80%）

3. **机井信息查看**
   - 点击地图上的机井标记
   - 弹出窗口显示详细信息
   - 包括坐标、状态、气体浓度、送风机状态等

## API接口说明

### 地图配置接口
- `GET /api/map-config` - 获取地图配置信息

### 机井管理接口
- `GET /api/wells` - 获取所有机井信息
- `POST /api/wells` - 添加新机井
- `PUT /api/wells/<well_id>` - 更新机井信息
- `POST /api/wells/<well_id>/gps` - 更新机井GPS坐标

### 定位搜索接口
- `POST /api/locate` - 根据GPS坐标定位
- `POST /api/search-location` - 搜索机井位置
- `GET /api/wells/<well_id>/gps-history` - 获取GPS历史轨迹

### 统计信息接口
- `GET /api/statistics` - 获取系统统计信息

## 数据格式

### 机井数据格式
```json
{
    "id": "WELL001",
    "name": "机井001",
    "lat": 39.9042,
    "lon": 116.4074,
    "status": "active",
    "gas_level": 25,
    "fan_status": "on",
    "last_update": "2024-01-01T12:00:00",
    "description": "机井描述"
}
```

### GPS定位请求格式
```json
{
    "lat": 39.9042,
    "lon": 116.4074,
    "radius": 1000
}
```

## 系统优势

### 🎯 完全离线
- 无需互联网连接
- 数据安全可控
- 适合内网环境

### 🗺️ 本地地图
- 快速加载瓦片
- 支持高精度缩放
- 覆盖全国范围

### 🔄 实时监控
- 30秒自动刷新数据
- 实时状态更新
- 异常状态自动告警

### 📱 响应式设计
- 支持桌面和移动设备
- 自适应界面布局
- 触摸友好的操作体验

## 故障排除

### 常见问题

1. **地图无法显示**
   - 检查瓦片服务器是否运行 (http://127.0.0.1:8080)
   - 确认瓦片文件是否存在 (static/tiles/)
   - 检查 map_config.json 配置

2. **GPS定位不准确**
   - 检查坐标格式是否正确
   - 确认坐标系统（WGS84）
   - 检查浏览器位置权限

3. **机井数据不更新**
   - 检查GPS监控系统连接状态
   - 确认API接口正常
   - 查看浏览器控制台错误信息

### 服务检查

1. **检查瓦片服务器**
   ```bash
   curl http://127.0.0.1:8080/config
   ```

2. **检查GPS监控系统**
   ```bash
   curl http://127.0.0.1:5000/api/statistics
   ```

3. **检查瓦片文件**
   ```bash
   ls static/tiles/8/100/
   ```

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 10GB以上（用于瓦片文件）
- 网络: 内网环境

### 软件要求
- Python 3.7+
- Flask
- Leaflet.js
- 现代浏览器（Chrome、Firefox、Edge）

### 文件结构
```
monitor1/
├── static/tiles/          # 瓦片文件目录
├── templates/             # HTML模板
├── tileserver.py          # 瓦片服务器
├── gps_well_monitoring_system.py  # GPS监控系统
├── start_offline_gps_monitoring.py  # 离线启动器
├── map_config.json        # 地图配置
└── gps_data_simulator.py  # 数据模拟器
```

## 扩展功能

系统支持以下扩展：
- 多用户权限管理
- 数据导出功能
- 报警通知系统
- 移动端APP
- 云端数据同步（可选）

---

**注意**: 本系统专为内网环境设计，确保在安全环境下使用，并定期备份重要数据。所有地图数据均为本地存储，无需互联网连接。
