#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查瓦片覆盖范围
"""

import os
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def check_tile_coverage():
    """检查瓦片覆盖范围"""
    print("🗺️ 检查瓦片覆盖范围")
    print("=" * 50)
    
    tiles_dir = Path("static/tiles/18")
    if not tiles_dir.exists():
        print("❌ 瓦片目录不存在")
        return
    
    # 获取所有X坐标
    x_coords = []
    for x_dir in tiles_dir.iterdir():
        if x_dir.is_dir():
            try:
                x_coords.append(int(x_dir.name))
            except ValueError:
                continue
    
    if not x_coords:
        print("❌ 没有找到瓦片文件")
        return
    
    x_coords.sort()
    print(f"📊 X坐标范围: {min(x_coords)} - {max(x_coords)}")
    
    # 获取Y坐标范围
    y_coords = []
    for x_dir in tiles_dir.iterdir():
        if x_dir.is_dir():
            for y_file in x_dir.iterdir():
                if y_file.suffix == '.png':
                    try:
                        y_coords.append(int(y_file.stem))
                    except ValueError:
                        continue
    
    if y_coords:
        y_coords.sort()
        print(f"📊 Y坐标范围: {min(y_coords)} - {max(y_coords)}")
    
    # 计算对应的经纬度范围
    print("\n🌍 对应的经纬度范围:")
    print("=" * 50)
    
    # 计算边界
    x_min, y_max = min(x_coords), min(y_coords)
    x_max, y_min = max(x_coords), max(y_coords)
    
    # 转换为经纬度
    def num2deg(xtile, ytile, zoom):
        n = 2.0 ** zoom
        lon_deg = xtile / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    lat_min, lon_min = num2deg(x_min, y_max, 18)
    lat_max, lon_max = num2deg(x_max, y_min, 18)
    
    print(f"📍 纬度范围: {lat_min:.6f} - {lat_max:.6f}")
    print(f"📍 经度范围: {lon_min:.6f} - {lon_max:.6f}")
    print(f"📍 中心点: {((lat_min + lat_max) / 2):.6f}, {((lon_min + lon_max) / 2):.6f}")
    
    # 检查北京坐标是否在范围内
    beijing_lat, beijing_lon = 39.9042, 116.4074
    beijing_x, beijing_y = deg2num(beijing_lat, beijing_lon, 18)
    
    print(f"\n🏛️ 北京坐标检查:")
    print(f"📍 北京坐标: {beijing_lat}, {beijing_lon}")
    print(f"🗺️ 对应瓦片: {beijing_x}, {beijing_y}")
    
    if x_min <= beijing_x <= x_max and y_min <= beijing_y <= y_max:
        print("✅ 北京坐标在瓦片覆盖范围内")
    else:
        print("❌ 北京坐标不在瓦片覆盖范围内")
        print(f"   需要X坐标: {x_min}-{x_max}, 实际: {beijing_x}")
        print(f"   需要Y坐标: {y_min}-{y_max}, 实际: {beijing_y}")
    
    # 建议的地图配置
    print(f"\n💡 建议的地图配置:")
    print("=" * 50)
    print(f"center: [{((lat_min + lat_max) / 2):.6f}, {((lon_min + lon_max) / 2):.6f}]")
    print(f"bounds: {{")
    print(f"  north: {lat_max:.6f},")
    print(f"  south: {lat_min:.6f},")
    print(f"  east: {lon_max:.6f},")
    print(f"  west: {lon_min:.6f}")
    print(f"}}")

if __name__ == "__main__":
    check_tile_coverage()
