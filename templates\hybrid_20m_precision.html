<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混合20米精度离线地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            padding: 20px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .control-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #2196F3;
        }
        
        .control-panel h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            margin-top: 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn.hybrid {
            background: #FF5722;
        }
        
        .btn.hybrid:hover {
            background: #D84315;
        }
        
        .status {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .status.error {
            background: #ffeaea;
            border-color: #f44336;
        }
        
        .status.success {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        
        .precision-info {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .precision-info h4 {
            margin: 0 0 10px 0;
            color: #1976D2;
            font-size: 16px;
        }
        
        .precision-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .precision-info li {
            font-size: 13px;
            margin-bottom: 5px;
        }
        
        .coordinates {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 8px;
            border-radius: 3px;
            margin-top: 5px;
            border: 1px solid #ddd;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(33, 150, 243, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 1000;
            display: none;
            text-align: center;
        }
        
        .layer-control {
            background: white;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .layer-control label {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        .layer-control input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .quick-actions .btn {
            margin-top: 0;
            font-size: 12px;
            padding: 8px 10px;
        }
        
        .hybrid-info {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .hybrid-info h4 {
            margin: 0 0 5px 0;
            color: #F57C00;
        }
        
        .hybrid-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .hybrid-info li {
            font-size: 12px;
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 混合20米精度离线地图系统</h1>
        <div class="subtitle">在线底图 + 本地OSM矢量数据 = 立即可用的高精度地图</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="precision-info">
                <h4>🎯 混合精度规格</h4>
                <ul>
                    <li>底图: 在线瓦片服务 (立即可用)</li>
                    <li>矢量: 本地OSM数据 (20米精度)</li>
                    <li>坐标精度: 8位小数 (约1.1米)</li>
                    <li>叠加精度: ±20米</li>
                    <li>响应速度: 即时显示</li>
                </ul>
            </div>
            
            <div class="hybrid-info">
                <h4>🔄 混合模式优势</h4>
                <ul>
                    <li>地图立即显示，无需等待</li>
                    <li>本地数据保证精度</li>
                    <li>在线底图提供背景</li>
                    <li>矢量叠加显示细节</li>
                    <li>离线缓存提高性能</li>
                </ul>
            </div>
            
            <div class="control-panel">
                <h3>📍 精确定位</h3>
                <div class="input-group">
                    <label>纬度 (8位小数)</label>
                    <input type="number" id="lat-input" step="0.00000001" placeholder="39.90420000">
                </div>
                <div class="input-group">
                    <label>经度 (8位小数)</label>
                    <input type="number" id="lon-input" step="0.00000001" placeholder="116.40740000">
                </div>
                <div class="quick-actions">
                    <button class="btn" onclick="gotoLocation()">定位</button>
                    <button class="btn" onclick="getCurrentLocation()">GPS</button>
                </div>
            </div>
            
            <div class="control-panel">
                <h3>🔄 精度数据生成</h3>
                <button class="btn hybrid" id="hybrid-generate-btn" onclick="generatePrecisionData()">
                    生成精度矢量数据
                </button>
                <div class="status">
                    <strong>说明:</strong> 生成本地OSM矢量数据叠加到在线底图上
                </div>
                <div id="hybrid-generation-status"></div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 图层控制</h3>
                <div class="layer-control">
                    <label>
                        <input type="checkbox" id="base-layer" checked>
                        在线底图
                    </label>
                    <label>
                        <input type="checkbox" id="roads-overlay" checked>
                        道路叠加 (本地OSM)
                    </label>
                    <label>
                        <input type="checkbox" id="buildings-overlay" checked>
                        建筑叠加 (本地OSM)
                    </label>
                    <label>
                        <input type="checkbox" id="pois-overlay" checked>
                        POI叠加 (本地OSM)
                    </label>
                    <label>
                        <input type="checkbox" id="water-overlay" checked>
                        水体叠加 (本地OSM)
                    </label>
                </div>
            </div>
            
            <div class="control-panel">
                <h3>📊 当前状态</h3>
                <div id="current-coordinates" class="coordinates">
                    点击地图获取坐标
                </div>
                <div id="zoom-level" class="coordinates">
                    缩放级别: -
                </div>
                <div id="tile-info" class="coordinates">
                    瓦片: -
                </div>
                <div id="overlay-info" class="coordinates">
                    叠加: 等待加载
                </div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 快速操作</h3>
                <div class="quick-actions">
                    <button class="btn" onclick="gotoBeijing()">北京</button>
                    <button class="btn" onclick="gotoShanghai()">上海</button>
                    <button class="btn" onclick="gotoGuangzhou()">广州</button>
                    <button class="btn" onclick="gotoShenzhen()">深圳</button>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="loading" id="loading">
                <div>🔄 正在生成精度矢量数据...</div>
                <div style="font-size: 12px; margin-top: 10px;">基于本地OSM文件</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京天安门
            zoom: 16,
            maxZoom: 18,
            minZoom: 8
        });

        // 在线底图图层
        const baseLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors | 本地OSM矢量叠加',
            maxZoom: 18
        }).addTo(map);

        // 矢量叠加图层
        let roadsLayer = L.layerGroup().addTo(map);
        let buildingsLayer = L.layerGroup().addTo(map);
        let poisLayer = L.layerGroup().addTo(map);
        let waterLayer = L.layerGroup().addTo(map);

        // 当前位置标记
        let currentMarker = null;
        let lastGenerationTime = 0;

        // 地图事件
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(8);
            const lng = e.latlng.lng.toFixed(8);
            
            document.getElementById('current-coordinates').innerHTML = 
                `纬度: ${lat}<br>经度: ${lng}`;
            
            // 更新输入框
            document.getElementById('lat-input').value = lat;
            document.getElementById('lon-input').value = lng;
            
            // 添加标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`精确坐标:<br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
        });

        map.on('zoomend moveend', function() {
            const zoom = map.getZoom();
            document.getElementById('zoom-level').innerHTML = `缩放级别: ${zoom}`;
            
            // 更新瓦片信息
            const center = map.getCenter();
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            document.getElementById('tile-info').innerHTML = 
                `瓦片: ${zoom}/${tileCoord.x}/${tileCoord.y}`;
            
            // 自动加载矢量叠加
            if (zoom >= 14) {
                loadVectorOverlay(tileCoord.x, tileCoord.y, zoom);
            }
        });

        // 工具函数
        function getTileCoordinates(lat, lng, zoom) {
            const latRad = lat * Math.PI / 180;
            const n = Math.pow(2, zoom);
            const x = Math.floor((lng + 180) / 360 * n);
            const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
            return { x, y };
        }

        function loadVectorOverlay(x, y, z) {
            fetch(`/api/vector-overlay/${z}/${x}/${y}`)
                .then(response => response.json())
                .then(data => {
                    // 清除现有叠加
                    roadsLayer.clearLayers();
                    buildingsLayer.clearLayers();
                    poisLayer.clearLayers();
                    waterLayer.clearLayers();
                    
                    // 添加道路
                    if (data.roads && data.roads.features) {
                        data.roads.features.forEach(feature => {
                            const road = L.geoJSON(feature, {
                                style: {
                                    color: '#ff6600',
                                    weight: 3,
                                    opacity: 0.8
                                }
                            });
                            roadsLayer.addLayer(road);
                        });
                    }
                    
                    // 添加建筑
                    if (data.buildings && data.buildings.features) {
                        data.buildings.features.forEach(feature => {
                            const building = L.geoJSON(feature, {
                                style: {
                                    color: '#999999',
                                    fillColor: '#dddddd',
                                    weight: 1,
                                    opacity: 0.8,
                                    fillOpacity: 0.6
                                }
                            });
                            buildingsLayer.addLayer(building);
                        });
                    }
                    
                    // 添加POI
                    if (data.pois && data.pois.features) {
                        data.pois.features.forEach(feature => {
                            const poi = L.geoJSON(feature, {
                                pointToLayer: function(feature, latlng) {
                                    return L.circleMarker(latlng, {
                                        radius: 4,
                                        fillColor: '#ff0000',
                                        color: '#ffffff',
                                        weight: 1,
                                        opacity: 1,
                                        fillOpacity: 0.8
                                    });
                                }
                            });
                            poisLayer.addLayer(poi);
                        });
                    }
                    
                    // 添加水体
                    if (data.water && data.water.features) {
                        data.water.features.forEach(feature => {
                            const water = L.geoJSON(feature, {
                                style: {
                                    color: '#4682b4',
                                    fillColor: '#87ceeb',
                                    weight: 1,
                                    opacity: 0.8,
                                    fillOpacity: 0.6
                                }
                            });
                            waterLayer.addLayer(water);
                        });
                    }
                    
                    const totalFeatures = (data.roads?.features?.length || 0) +
                                        (data.buildings?.features?.length || 0) +
                                        (data.pois?.features?.length || 0) +
                                        (data.water?.features?.length || 0);
                    
                    document.getElementById('overlay-info').innerHTML = 
                        `叠加: ${totalFeatures} 个要素`;
                })
                .catch(error => {
                    console.error('加载矢量叠加失败:', error);
                    document.getElementById('overlay-info').innerHTML = '叠加: 加载失败';
                });
        }

        function gotoLocation() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请输入有效的坐标');
                return;
            }
            
            map.setView([lat, lng], 17);
            
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`目标位置:<br>纬度: ${lat.toFixed(8)}<br>经度: ${lng.toFixed(8)}`)
                .openPopup();
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude.toFixed(8);
                    const lng = position.coords.longitude.toFixed(8);
                    
                    document.getElementById('lat-input').value = lat;
                    document.getElementById('lon-input').value = lng;
                    
                    map.setView([lat, lng], 17);
                    
                    if (currentMarker) {
                        map.removeLayer(currentMarker);
                    }
                    currentMarker = L.marker([lat, lng]).addTo(map)
                        .bindPopup(`当前位置:<br>纬度: ${lat}<br>经度: ${lng}<br>精度: ±${position.coords.accuracy}米`)
                        .openPopup();
                }, function(error) {
                    alert('无法获取当前位置: ' + error.message);
                });
            } else {
                alert('浏览器不支持地理定位');
            }
        }

        function generatePrecisionData() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请先设置坐标');
                return;
            }
            
            // 防止频繁生成
            const now = Date.now();
            if (now - lastGenerationTime < 30000) {
                alert('请等待30秒后再次生成');
                return;
            }
            lastGenerationTime = now;
            
            const btn = document.getElementById('hybrid-generate-btn');
            const status = document.getElementById('hybrid-generation-status');
            const loading = document.getElementById('loading');
            
            btn.disabled = true;
            btn.textContent = '生成中...';
            loading.style.display = 'block';
            
            fetch(`/api/generate-precision-data?lat=${lat}&lon=${lng}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        status.innerHTML = `<div class="status success">${data.message}</div>`;
                        
                        // 30秒后刷新叠加
                        setTimeout(() => {
                            const center = map.getCenter();
                            const zoom = map.getZoom();
                            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
                            loadVectorOverlay(tileCoord.x, tileCoord.y, zoom);
                            
                            btn.disabled = false;
                            btn.textContent = '生成精度矢量数据';
                            loading.style.display = 'none';
                            status.innerHTML = '<div class="status success">精度数据生成完成，矢量叠加已更新</div>';
                        }, 30000);
                    } else {
                        throw new Error(data.error || '生成失败');
                    }
                })
                .catch(error => {
                    status.innerHTML = `<div class="status error">错误: ${error.message}</div>`;
                    btn.disabled = false;
                    btn.textContent = '生成精度矢量数据';
                    loading.style.display = 'none';
                });
        }

        // 快速定位函数
        function gotoBeijing() {
            document.getElementById('lat-input').value = '39.90420000';
            document.getElementById('lon-input').value = '116.40740000';
            gotoLocation();
        }

        function gotoShanghai() {
            document.getElementById('lat-input').value = '31.23040000';
            document.getElementById('lon-input').value = '121.47370000';
            gotoLocation();
        }

        function gotoGuangzhou() {
            document.getElementById('lat-input').value = '23.12910000';
            document.getElementById('lon-input').value = '113.26440000';
            gotoLocation();
        }

        function gotoShenzhen() {
            document.getElementById('lat-input').value = '22.54280000';
            document.getElementById('lon-input').value = '114.05950000';
            gotoLocation();
        }

        // 图层控制
        document.getElementById('base-layer').addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(baseLayer);
            } else {
                map.removeLayer(baseLayer);
            }
        });

        document.getElementById('roads-overlay').addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(roadsLayer);
            } else {
                map.removeLayer(roadsLayer);
            }
        });

        document.getElementById('buildings-overlay').addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(buildingsLayer);
            } else {
                map.removeLayer(buildingsLayer);
            }
        });

        document.getElementById('pois-overlay').addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(poisLayer);
            } else {
                map.removeLayer(poisLayer);
            }
        });

        document.getElementById('water-overlay').addEventListener('change', function() {
            if (this.checked) {
                map.addLayer(waterLayer);
            } else {
                map.removeLayer(waterLayer);
            }
        });

        // 初始化显示
        document.getElementById('zoom-level').innerHTML = `缩放级别: ${map.getZoom()}`;
        document.getElementById('overlay-info').innerHTML = '叠加: 系统就绪';
    </script>
</body>
</html>
