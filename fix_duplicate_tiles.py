#!/usr/bin/env python3
"""
修复重复瓦片问题 - 为每个瓦片坐标生成不同的内容
"""

import os
import math
from PIL import Image, ImageDraw, ImageFont
import random

def deg2num(lat_deg, lon_deg, zoom):
    """经纬度转瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def num2deg(x, y, zoom):
    """瓦片坐标转经纬度"""
    n = 2.0 ** zoom
    lon_deg = x / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def create_unique_tile(x, y, zoom):
    """为特定坐标创建唯一的瓦片"""
    # 使用坐标作为随机种子，确保相同坐标总是生成相同内容
    random.seed(x * 1000000 + y)
    
    # 创建基础图像
    base_colors = [
        '#e8f4f8',  # 浅蓝
        '#f0f8e8',  # 浅绿
        '#f8f0e8',  # 浅黄
        '#f8e8f0',  # 浅粉
        '#e8e8f8',  # 浅紫
    ]
    
    base_color = random.choice(base_colors)
    img = Image.new('RGB', (256, 256), color=base_color)
    draw = ImageDraw.Draw(img)
    
    # 获取地理坐标
    lat, lon = num2deg(x, y, zoom)
    
    # 绘制网格
    grid_color = '#cccccc'
    for i in range(0, 256, 32):
        draw.line([(i, 0), (i, 256)], fill=grid_color, width=1)
        draw.line([(0, i), (256, i)], fill=grid_color, width=1)
    
    # 添加一些随机的地理特征
    feature_colors = ['#4a90e2', '#7ed321', '#f5a623', '#d0021b', '#9013fe']
    
    # 绘制随机形状来模拟地理特征
    num_features = random.randint(2, 6)
    for i in range(num_features):
        feature_type = random.choice(['circle', 'rect', 'line'])
        color = random.choice(feature_colors)
        
        if feature_type == 'circle':
            cx, cy = random.randint(20, 236), random.randint(20, 236)
            radius = random.randint(10, 30)
            draw.ellipse([cx-radius, cy-radius, cx+radius, cy+radius], 
                        fill=color, outline='black')
        elif feature_type == 'rect':
            x1, y1 = random.randint(10, 200), random.randint(10, 200)
            w, h = random.randint(20, 50), random.randint(20, 50)
            draw.rectangle([x1, y1, x1+w, y1+h], fill=color, outline='black')
        elif feature_type == 'line':
            x1, y1 = random.randint(0, 256), random.randint(0, 256)
            x2, y2 = random.randint(0, 256), random.randint(0, 256)
            draw.line([x1, y1, x2, y2], fill=color, width=3)
    
    # 添加坐标信息
    try:
        font = ImageFont.load_default()
        
        # 瓦片坐标
        coord_text = f"Tile: {x},{y}"
        draw.text((5, 5), coord_text, fill='black', font=font)
        
        # 地理坐标
        geo_text = f"Geo: {lat:.4f},{lon:.4f}"
        draw.text((5, 20), geo_text, fill='black', font=font)
        
        # 缩放级别
        zoom_text = f"Zoom: {zoom}"
        draw.text((5, 35), zoom_text, fill='black', font=font)
        
        # 添加一个唯一标识符
        unique_id = (x * 1000000 + y) % 999999
        id_text = f"ID: {unique_id:06d}"
        draw.text((5, 235), id_text, fill='red', font=font)
        
    except Exception as e:
        # 如果字体加载失败，使用简单文本
        draw.text((5, 5), f"{x},{y}", fill='black')
    
    return img

def fix_level20_tiles():
    """修复20级瓦片的重复问题"""
    
    # 北京地区范围
    north = 40.1
    south = 39.8
    west = 116.2
    east = 116.6
    
    # 获取20级瓦片边界
    x_min, y_max = deg2num(north, west, 20)
    x_max, y_min = deg2num(south, east, 20)
    
    print(f"🔧 修复20级瓦片重复问题")
    print(f"📍 范围: x:{x_min}-{x_max}, y:{y_min}-{y_max}")
    
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"📊 需要处理的瓦片总数: {total_tiles}")
    
    fixed_count = 0
    
    for x in range(x_min, x_max + 1):
        # 确保目录存在
        tile_dir = f"static/tiles/20/{x}"
        os.makedirs(tile_dir, exist_ok=True)
        
        for y in range(y_min, y_max + 1):
            tile_path = f"{tile_dir}/{y}.png"
            
            try:
                # 创建唯一的瓦片内容
                img = create_unique_tile(x, y, 20)
                img.save(tile_path, 'PNG')
                fixed_count += 1
                
                if fixed_count % 50 == 0:
                    print(f"✅ 已修复 {fixed_count}/{total_tiles} 个瓦片...")
                    
            except Exception as e:
                print(f"❌ 修复瓦片失败 {tile_path}: {e}")
    
    print(f"🎉 瓦片修复完成!")
    print(f"📈 总共修复瓦片: {fixed_count}")
    print(f"💡 现在每个瓦片都有唯一的内容和标识符")

if __name__ == "__main__":
    fix_level20_tiles()
