#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超简单测试工具
使用最基本的查询格式测试
"""

import requests
import time

def test_ultra_simple_query():
    """测试超简单查询"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 最简单的查询 - 只查询一个元素
    ultra_simple_query = "[out:xml];(way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5););out geom;"
    
    print("🧪 测试超简单查询...")
    print(f"📋 查询语句: {ultra_simple_query}")
    print("-" * 50)
    
    try:
        response = requests.post(
            base_url,
            data={"data": ultra_simple_query},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"✅ 查询成功: {len(response.content)} 字节")
            print(f"响应内容前100字符: {response.text[:100]}")
            return True
        else:
            print(f"❌ 查询失败: HTTP {response.status_code}")
            print(f"错误内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        return False

def test_different_query_formats():
    """测试不同的查询格式"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同的查询格式
    query_formats = {
        "格式1": "[out:xml];(way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5););out geom;",
        "格式2": "[out:xml][timeout:30];(way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5););out geom;",
        "格式3": "[out:xml][timeout:30];\n(\n  way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5);\n);\nout geom;",
        "格式4": "[out:xml][timeout:30];\n(\n  way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5);\n);\nout geom;",
        "格式5": "[out:xml][timeout:30];\n(\n  way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5);\n);\nout geom;"
    }
    
    print("\n📋 测试不同查询格式...")
    print("=" * 50)
    
    for name, query in query_formats.items():
        print(f"\n🧪 测试 {name}:")
        print(f"查询: {query}")
        print("-" * 30)
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 成功: {len(response.content)} 字节")
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)

def test_different_servers():
    """测试不同的API服务器"""
    servers = [
        "https://overpass-api.de/api/interpreter",
        "https://lz4.overpass-api.de/api/interpreter",
        "https://z.overpass-api.de/api/interpreter"
    ]
    
    # 使用最简单的查询
    simple_query = "[out:xml];(way[\"highway\"=\"primary\"](39.9,116.4,40.0,116.5););out geom;"
    
    print("\n🌐 测试不同API服务器...")
    print("=" * 50)
    
    for server in servers:
        print(f"\n📡 测试服务器: {server}")
        
        try:
            response = requests.post(
                server,
                data={"data": simple_query},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 服务器正常: {len(response.content)} 字节")
            else:
                print(f"❌ 服务器异常: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
        
        time.sleep(2)

def test_different_bbox():
    """测试不同的边界框"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同的边界框
    bbox_tests = [
        ("小范围", "39.9,116.4,40.0,116.5"),
        ("中范围", "39.9,116.4,40.1,116.6"),
        ("大范围", "39.8,116.3,40.2,116.7"),
        ("超大范围", "39.5,116.0,40.5,117.0")
    ]
    
    print("\n📐 测试不同边界框...")
    print("=" * 50)
    
    for name, bbox in bbox_tests:
        print(f"\n🧪 测试 {name}: {bbox}")
        
        query = f"[out:xml];(way[\"highway\"=\"primary\"]({bbox}););out geom;"
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 成功: {len(response.content)} 字节")
            else:
                print(f"❌ 失败: HTTP {response.status_code}")
                print(f"错误内容: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)

def main():
    """主函数"""
    print("🔧 超简单测试工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择测试类型:")
        print("1. 测试超简单查询")
        print("2. 测试不同查询格式")
        print("3. 测试不同API服务器")
        print("4. 测试不同边界框")
        print("5. 全部测试")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            test_ultra_simple_query()
        
        elif choice == "2":
            test_different_query_formats()
        
        elif choice == "3":
            test_different_servers()
        
        elif choice == "4":
            test_different_bbox()
        
        elif choice == "5":
            test_ultra_simple_query()
            test_different_query_formats()
            test_different_servers()
            test_different_bbox()
        
        elif choice == "6":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
