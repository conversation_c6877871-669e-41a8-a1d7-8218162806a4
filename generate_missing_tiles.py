#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成缺失的11级瓦片（离线模式）
"""

import os
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def create_offline_tile(z, x, y, tiles_dir):
    """创建离线瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    if tile_file.exists():
        return True
    
    try:
        # 创建256x256的瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')
        draw = ImageDraw.Draw(img)
        
        # 绘制网格
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
            draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
        
        # 添加瓦片信息
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"Z{z} X{x} Y{y}"
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width, text_height = 60, 12
        
        text_x = (256 - text_width) // 2
        text_y = (256 - text_height) // 2
        
        draw.rectangle([text_x-3, text_y-3, text_x+text_width+3, text_y+text_height+3], 
                      fill='white', outline='black')
        
        if font:
            draw.text((text_x, text_y), text, fill='black', font=font)
        else:
            draw.text((text_x, text_y), text, fill='black')
        
        # 根据缩放级别添加不同特征
        if z >= 11:
            # 模拟道路网络
            draw.line([(50, 50), (200, 200)], fill='#ffff00', width=3)
            draw.line([(200, 50), (50, 200)], fill='#ffff00', width=2)
            draw.line([(128, 0), (128, 256)], fill='#ffff00', width=2)
            draw.line([(0, 128), (256, 128)], fill='#ffff00', width=2)
        
        if z >= 12:
            # 模拟建筑区域
            draw.rectangle([80, 80, 120, 120], fill='#d3d3d3', outline='black')
            draw.rectangle([140, 140, 180, 180], fill='#d3d3d3', outline='black')
            draw.rectangle([60, 160, 100, 200], fill='#d3d3d3', outline='black')
        
        img.save(tile_file, 'PNG')
        return True
        
    except Exception as e:
        print(f"  ❌ 创建瓦片失败: {z}/{x}/{y} - {e}")
        return False

def generate_missing_zoom11_tiles():
    """生成缺失的11级瓦片"""
    print("🔧 开始生成缺失的11级瓦片...")
    
    # 北京坐标
    center_lat, center_lng = 39.9042, 116.4074
    zoom = 11
    
    # 计算中心瓦片坐标
    center_x, center_y = deg2num(center_lat, center_lng, zoom)
    print(f"📍 中心瓦片坐标: {center_x}, {center_y}")
    
    # 扩大覆盖范围 - 从中心向外扩展
    tile_range = 12  # 减少范围，避免生成过多瓦片
    
    tiles_dir = Path("static/tiles")
    
    total_tiles = (2 * tile_range + 1) ** 2
    generated = 0
    skipped = 0
    
    print(f"📦 计划生成 {total_tiles} 个瓦片...")
    
    for dx in range(-tile_range, tile_range + 1):
        for dy in range(-tile_range, tile_range + 1):
            x = center_x + dx
            y = center_y + dy
            
            if create_offline_tile(zoom, x, y, tiles_dir):
                generated += 1
                if generated % 25 == 0:
                    print(f"  ✅ 已生成 {generated} 个瓦片...")
            else:
                skipped += 1
    
    print(f"\n✅ 生成完成!")
    print(f"📊 总计: {total_tiles} 个瓦片")
    print(f"✅ 新生成: {generated} 个")
    print(f"⏭️ 已存在: {skipped} 个")

if __name__ == "__main__":
    generate_missing_zoom11_tiles()