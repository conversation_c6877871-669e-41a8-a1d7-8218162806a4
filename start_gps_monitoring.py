#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS机井监控系统启动器
"""

import webbrowser
import threading
import time
from gps_well_monitoring_system import GPSWellMonitoringSystem

def start_system():
    """启动GPS监控系统"""
    print("🔧 GPS机井监控地图系统启动器")
    print("=" * 60)
    
    # 创建系统实例
    system = GPSWellMonitoringSystem()
    
    # 延迟启动浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
        print("🌐 浏览器已自动打开")
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🚀 启动GPS机井监控地图系统...")
    print("🌐 系统将在 http://127.0.0.1:5000 启动")
    print("📱 请在浏览器中访问该地址")
    print("🎯 系统功能:")
    print("  - GPS坐标定位")
    print("  - 机井实时监控")
    print("  - 气体检测数据")
    print("  - 送风机状态监控")
    print("  - 历史轨迹追踪")
    print("  - 附近机井搜索")
    print("=" * 60)
    
    # 启动Flask应用
    system.run(host='127.0.0.1', port=5000, debug=False)

if __name__ == "__main__":
    start_system()
