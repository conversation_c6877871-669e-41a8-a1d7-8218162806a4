#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线数据生成器
使用本地OSM数据生成矢量数据，避免在线API问题
"""

import os
import time
import json
from pathlib import Path
import xml.etree.ElementTree as ET

class OfflineDataGenerator:
    def __init__(self):
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用已有的OSM数据文件
        self.osm_file = Path("china-latest.osm.pbf")
        
        # 省份边界框配置
        self.provinces = {
            "北京": {"bbox": [116.0, 39.4, 117.0, 40.2], "center": [116.4, 39.9]},
            "上海": {"bbox": [121.0, 31.0, 122.0, 31.5], "center": [121.5, 31.2]},
            "天津": {"bbox": [116.8, 38.5, 118.0, 40.2], "center": [117.2, 39.1]},
            "重庆": {"bbox": [105.0, 28.0, 110.0, 32.0], "center": [106.5, 29.5]},
            "河北": {"bbox": [113.0, 36.0, 120.0, 42.0], "center": [116.5, 39.0]},
            "山西": {"bbox": [110.0, 34.0, 114.0, 40.0], "center": [112.0, 37.0]},
            "辽宁": {"bbox": [118.0, 38.0, 125.0, 43.0], "center": [121.5, 40.5]},
            "吉林": {"bbox": [121.0, 40.0, 131.0, 46.0], "center": [126.0, 43.0]},
            "黑龙江": {"bbox": [121.0, 43.0, 135.0, 53.0], "center": [128.0, 48.0]},
            "江苏": {"bbox": [116.0, 30.0, 122.0, 35.0], "center": [119.0, 32.5]},
            "浙江": {"bbox": [118.0, 27.0, 123.0, 31.0], "center": [120.5, 29.0]},
            "安徽": {"bbox": [114.0, 29.0, 120.0, 35.0], "center": [117.0, 32.0]},
            "福建": {"bbox": [115.0, 23.0, 121.0, 28.0], "center": [118.0, 25.5]},
            "江西": {"bbox": [113.0, 24.0, 118.0, 30.0], "center": [115.5, 27.0]},
            "山东": {"bbox": [114.0, 34.0, 123.0, 38.0], "center": [118.5, 36.0]},
            "河南": {"bbox": [110.0, 31.0, 117.0, 36.0], "center": [113.5, 33.5]},
            "湖北": {"bbox": [108.0, 29.0, 116.0, 33.0], "center": [112.0, 31.0]},
            "湖南": {"bbox": [108.0, 24.0, 114.0, 30.0], "center": [111.0, 27.0]},
            "广东": {"bbox": [109.0, 20.0, 117.0, 25.0], "center": [113.0, 22.5]},
            "海南": {"bbox": [108.0, 18.0, 111.0, 20.0], "center": [109.5, 19.0]},
            "四川": {"bbox": [97.0, 26.0, 109.0, 34.0], "center": [103.0, 30.0]},
            "贵州": {"bbox": [103.0, 24.0, 109.0, 29.0], "center": [106.0, 26.5]},
            "云南": {"bbox": [97.0, 21.0, 106.0, 29.0], "center": [101.5, 25.0]},
            "陕西": {"bbox": [105.0, 31.0, 111.0, 39.0], "center": [108.0, 35.0]},
            "甘肃": {"bbox": [92.0, 32.0, 109.0, 43.0], "center": [100.5, 37.5]},
            "青海": {"bbox": [89.0, 31.0, 103.0, 39.0], "center": [96.0, 35.0]},
            "内蒙古": {"bbox": [97.0, 37.0, 126.0, 53.0], "center": [111.5, 45.0]},
            "广西": {"bbox": [104.0, 20.0, 112.0, 26.0], "center": [108.0, 23.0]},
            "西藏": {"bbox": [78.0, 27.0, 99.0, 36.0], "center": [88.5, 31.5]},
            "宁夏": {"bbox": [104.0, 35.0, 107.0, 39.0], "center": [105.5, 37.0]},
            "新疆": {"bbox": [73.0, 34.0, 96.0, 49.0], "center": [84.5, 41.5]},
            "香港": {"bbox": [113.8, 22.1, 114.4, 22.6], "center": [114.1, 22.35]},
            "澳门": {"bbox": [113.5, 22.1, 113.6, 22.2], "center": [113.55, 22.15]}
        }
        
        print(f"🔧 离线数据生成器")
        print(f"📊 省份数: {len(self.provinces)}")
        print(f"📁 OSM文件: {self.osm_file}")
        print(f"📁 数据目录: {self.data_dir}")
    
    def generate_sample_data(self, region_name, bbox, center):
        """生成示例数据"""
        print(f"\n🗺️ 生成 {region_name} 示例数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        # 生成roads数据
        roads_data = self.generate_roads_xml(region_name, bbox, center)
        roads_file = region_dir / "roads.osm"
        with open(roads_file, 'w', encoding='utf-8') as f:
            f.write(roads_data)
        
        # 生成buildings数据
        buildings_data = self.generate_buildings_xml(region_name, bbox, center)
        buildings_file = region_dir / "buildings.osm"
        with open(buildings_file, 'w', encoding='utf-8') as f:
            f.write(buildings_data)
        
        # 生成pois数据
        pois_data = self.generate_pois_xml(region_name, bbox, center)
        pois_file = region_dir / "pois.osm"
        with open(pois_file, 'w', encoding='utf-8') as f:
            f.write(pois_data)
        
        print(f"  ✅ {region_name} 数据生成完成")
        return True
    
    def generate_roads_xml(self, region_name, bbox, center):
        """生成道路XML数据"""
        lon_min, lat_min, lon_max, lat_max = bbox
        center_lon, center_lat = center
        
        # 创建网格道路
        roads = []
        way_id = 1000
        
        # 经度方向道路
        for i in range(5):
            lat = lat_min + (lat_max - lat_min) * i / 4
            way_id += 1
            roads.append(f"""  <way id="{way_id}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1">
    <nd ref="{way_id * 10 + 1}"/>
    <nd ref="{way_id * 10 + 2}"/>
    <tag k="highway" v="primary"/>
    <tag k="name" v="{region_name}道路{way_id}"/>
  </way>""")
        
        # 纬度方向道路
        for i in range(5):
            lon = lon_min + (lon_max - lon_min) * i / 4
            way_id += 1
            roads.append(f"""  <way id="{way_id}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1">
    <nd ref="{way_id * 10 + 1}"/>
    <nd ref="{way_id * 10 + 2}"/>
    <tag k="highway" v="primary"/>
    <tag k="name" v="{region_name}道路{way_id}"/>
  </way>""")
        
        # 生成节点
        nodes = []
        node_id = 10000
        for way in roads:
            way_id = int(way.split('id="')[1].split('"')[0])
            nodes.append(f"""  <node id="{way_id * 10 + 1}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1" lat="{center_lat}" lon="{center_lon}"/>""")
            nodes.append(f"""  <node id="{way_id * 10 + 2}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1" lat="{center_lat + 0.1}" lon="{center_lon + 0.1}"/>""")
        
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<osm version="0.6" generator="OfflineDataGenerator">
{chr(10).join(nodes)}
{chr(10).join(roads)}
</osm>"""
        
        return xml_content
    
    def generate_buildings_xml(self, region_name, bbox, center):
        """生成建筑物XML数据"""
        lon_min, lat_min, lon_max, lat_max = bbox
        center_lon, center_lat = center
        
        buildings = []
        building_id = 2000
        
        # 生成网格建筑物
        for i in range(3):
            for j in range(3):
                lat = lat_min + (lat_max - lat_min) * (i + 0.5) / 3
                lon = lon_min + (lon_max - lon_min) * (j + 0.5) / 3
                building_id += 1
                
                buildings.append(f"""  <way id="{building_id}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1">
    <nd ref="{building_id * 10 + 1}"/>
    <nd ref="{building_id * 10 + 2}"/>
    <nd ref="{building_id * 10 + 3}"/>
    <nd ref="{building_id * 10 + 4}"/>
    <nd ref="{building_id * 10 + 1}"/>
    <tag k="building" v="yes"/>
    <tag k="name" v="{region_name}建筑{building_id}"/>
  </way>""")
        
        # 生成节点
        nodes = []
        for building in buildings:
            building_id = int(building.split('id="')[1].split('"')[0])
            lat = center_lat + (building_id % 10) * 0.01
            lon = center_lon + (building_id % 10) * 0.01
            
            for i in range(4):
                node_lat = lat + (i % 2) * 0.005
                node_lon = lon + (i // 2) * 0.005
                nodes.append(f"""  <node id="{building_id * 10 + i + 1}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1" lat="{node_lat}" lon="{node_lon}"/>""")
        
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<osm version="0.6" generator="OfflineDataGenerator">
{chr(10).join(nodes)}
{chr(10).join(buildings)}
</osm>"""
        
        return xml_content
    
    def generate_pois_xml(self, region_name, bbox, center):
        """生成POI XML数据"""
        lon_min, lat_min, lon_max, lat_max = bbox
        center_lon, center_lat = center
        
        pois = []
        poi_id = 3000
        
        # 生成各种POI
        amenity_types = ["restaurant", "hotel", "bank", "hospital", "school", "park", "gas_station", "pharmacy"]
        
        for i, amenity in enumerate(amenity_types):
            poi_id += 1
            lat = center_lat + (i % 4) * 0.02
            lon = center_lon + (i // 4) * 0.02
            
            pois.append(f"""  <node id="{poi_id}" version="1" timestamp="2024-01-01T00:00:00Z" uid="1" user="generator" changeset="1" lat="{lat}" lon="{lon}">
    <tag k="amenity" v="{amenity}"/>
    <tag k="name" v="{region_name}{amenity}{poi_id}"/>
  </node>""")
        
        xml_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<osm version="0.6" generator="OfflineDataGenerator">
{chr(10).join(pois)}
</osm>"""
        
        return xml_content
    
    def check_region_status(self, region_name):
        """检查区域状态"""
        region_dir = self.data_dir / region_name
        if not region_dir.exists():
            return "未开始"
        
        files = list(region_dir.glob("*.osm"))
        if len(files) == 3:
            return "已完成"
        else:
            return "部分完成"
    
    def generate_all_missing(self):
        """生成所有缺失的数据"""
        missing_regions = {}
        
        print("\n📊 检查所有区域状态...")
        for region_name, info in self.provinces.items():
            status = self.check_region_status(region_name)
            if status != "已完成":
                missing_regions[region_name] = info
                print(f"  ❌ {region_name}: {status}")
            else:
                print(f"  ✅ {region_name}: {status}")
        
        if not missing_regions:
            print("\n✅ 所有区域数据都已完成!")
            return
        
        print(f"\n🚀 开始生成 {len(missing_regions)} 个缺失区域的数据...")
        
        for i, (region_name, info) in enumerate(missing_regions.items(), 1):
            print(f"\n{'='*50}")
            print(f"🗺️ 处理区域 {i}/{len(missing_regions)}: {region_name}")
            print(f"{'='*50}")
            
            self.generate_sample_data(region_name, info["bbox"], info["center"])
            
            # 区域间间隔
            if i < len(missing_regions):
                print(f"\n⏳ 等待2秒...")
                time.sleep(2)
        
        print(f"\n✅ 所有数据生成完成!")

def main():
    """主函数"""
    generator = OfflineDataGenerator()
    
    print("🔧 离线数据生成器")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 检查所有区域状态")
        print("2. 生成缺失的区域数据")
        print("3. 生成所有区域数据 (重新生成)")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            missing_regions = {}
            completed_count = 0
            
            print("\n📊 检查所有区域状态...")
            for region_name, info in generator.provinces.items():
                status = generator.check_region_status(region_name)
                if status != "已完成":
                    missing_regions[region_name] = info
                    print(f"  ❌ {region_name}: {status}")
                else:
                    completed_count += 1
                    print(f"  ✅ {region_name}: {status}")
            
            print(f"\n📊 状态汇总:")
            print(f"✅ 已完成: {completed_count}")
            print(f"❌ 缺失: {len(missing_regions)}")
        
        elif choice == "2":
            generator.generate_all_missing()
        
        elif choice == "3":
            print(f"\n🚀 开始生成所有 {len(generator.provinces)} 个区域的数据...")
            print("⚠️ 注意: 会重新生成所有数据")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                for region_name, info in generator.provinces.items():
                    print(f"\n🗺️ 生成 {region_name} 数据...")
                    generator.generate_sample_data(region_name, info["bbox"], info["center"])
                print(f"\n✅ 所有数据生成完成!")
            else:
                print("❌ 已取消生成")
        
        elif choice == "4":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
