#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试区域检测功能
"""

def getRegionByCoordinates(lat, lon):
    """根据坐标确定区域"""
    # 具体城市坐标范围映射（优先匹配）
    cities = {
        '恩施': {'north': 30.5, 'south': 30.0, 'east': 109.8, 'west': 109.0},
        '武汉': {'north': 30.8, 'south': 30.4, 'east': 114.6, 'west': 114.0},
        '北京': {'north': 40.2, 'south': 39.4, 'east': 117.4, 'west': 115.7},
        '上海': {'north': 31.9, 'south': 30.7, 'east': 122.1, 'west': 120.9},
        '重庆': {'north': 32.2, 'south': 28.2, 'east': 110.2, 'west': 105.3}
    }
    
    # 先检查具体城市
    for city, bounds in cities.items():
        if bounds['south'] <= lat <= bounds['north'] and bounds['west'] <= lon <= bounds['east']:
            return city
    
    # 中国各省市坐标范围映射
    regions = {
        '湖北': {'north': 33.3, 'south': 29.0, 'east': 116.1, 'west': 108.3},
        '北京': {'north': 40.2, 'south': 39.4, 'east': 117.4, 'west': 115.7},
        '上海': {'north': 31.9, 'south': 30.7, 'east': 122.1, 'west': 120.9},
        '重庆': {'north': 32.2, 'south': 28.2, 'east': 110.2, 'west': 105.3}
    }
    
    for region, bounds in regions.items():
        if bounds['south'] <= lat <= bounds['north'] and bounds['west'] <= lon <= bounds['east']:
            return region
    
    return None

def test_coordinates():
    """测试坐标检测"""
    test_cases = [
        (30.295, 109.486, "恩施"),
        (39.9042, 116.4074, "北京"),
        (31.2304, 121.4737, "上海"),
        (29.5647, 106.5507, "重庆"),
        (30.5928, 114.3055, "武汉")
    ]
    
    print("🎯 区域检测测试")
    print("=" * 50)
    
    for lat, lon, expected in test_cases:
        result = getRegionByCoordinates(lat, lon)
        status = "✅" if result == expected else "❌"
        print(f"{status} 坐标: {lat}, {lon}")
        print(f"   期望: {expected}")
        print(f"   实际: {result}")
        print()

if __name__ == "__main__":
    test_coordinates()
