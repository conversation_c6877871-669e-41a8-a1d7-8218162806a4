@echo off
chcp 65001
echo 🗺️ OpenMapTiles中文版集成 - 基于本地OSM数据
echo ================================================

echo 📦 激活Python虚拟环境...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
) else (
    echo ⚠️ 虚拟环境不存在，使用系统Python
)

echo 🔍 检查OSM数据文件...
if exist "china-latest.osm.pbf" (
    echo ✅ 找到OSM数据文件: china-latest.osm.pbf
) else (
    echo ❌ 未找到OSM数据文件: china-latest.osm.pbf
    echo 请确保文件存在后重新运行
    pause
    exit /b 1
)

echo 🔧 安装必要依赖...
pip install Pillow mercantile requests osmium psycopg2-binary

echo 🚀 开始集成OpenMapTiles...
python openmaptiles_cn_integration.py

echo ✅ 集成完成!
echo 📋 可以运行以下命令测试:
echo    python tileserver.py
echo    python app.py
pause