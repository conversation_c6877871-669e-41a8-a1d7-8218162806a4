#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提供测试页面的HTTP服务器
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

def serve_test_page():
    """提供测试页面"""
    PORT = 8081
    
    # 切换到当前目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"🧪 功能测试服务器启动")
        print(f"🌐 访问地址: http://localhost:{PORT}/test_complete_functionality.html")
        print(f"📱 请在浏览器中打开该地址测试完整功能")
        print(f"⏹️ 按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}/test_complete_functionality.html')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print(f"\n🛑 服务器已停止")

if __name__ == "__main__":
    serve_test_page()
