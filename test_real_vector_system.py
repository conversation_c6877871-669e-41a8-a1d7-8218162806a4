#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试真实矢量数据系统
"""

import requests
import json
import time

def test_system():
    """测试系统功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("🧪 真实矢量数据系统测试")
    print("=" * 50)
    
    # 测试1: 系统连接
    print("🔧 测试1: 系统连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 系统连接正常")
        else:
            print(f"❌ 系统连接失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 系统连接失败: {e}")
        return
    
    # 测试2: 获取区域列表
    print("\n🔧 测试2: 获取区域列表...")
    try:
        response = requests.get(f"{base_url}/api/regions", timeout=10)
        if response.status_code == 200:
            regions = response.json()
            print(f"✅ 区域列表获取成功: {len(regions)}个区域")
            for region in regions[:5]:  # 显示前5个区域
                print(f"   - {region['name']}: {len(region['files'])}个文件")
            if len(regions) > 5:
                print(f"   ... 还有 {len(regions) - 5} 个区域")
        else:
            print(f"❌ 获取区域列表失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 获取区域列表失败: {e}")
        return
    
    # 测试3: 获取矢量数据
    if regions:
        test_region = regions[0]['name']
        print(f"\n🔧 测试3: 获取 {test_region} 矢量数据...")
        try:
            response = requests.get(f"{base_url}/api/vector-data/{test_region}", timeout=15)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {test_region} 数据获取成功")
                print(f"   - 总要素数: {data['metadata']['total_features']}")
                print(f"   - 道路要素: {len(data['roads']['features'])}")
                print(f"   - 建筑要素: {len(data['buildings']['features'])}")
                print(f"   - POI要素: {len(data['pois']['features'])}")
                
                # 检查数据质量
                if data['metadata']['total_features'] > 0:
                    print("✅ 数据质量良好")
                else:
                    print("⚠️ 数据为空")
            else:
                print(f"❌ 获取矢量数据失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ 获取矢量数据失败: {e}")
    
    # 测试4: 地图配置
    print("\n🔧 测试4: 获取地图配置...")
    try:
        response = requests.get(f"{base_url}/api/map-config", timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 地图配置获取成功")
            print(f"   - 中心坐标: {config['center']}")
            print(f"   - 缩放级别: {config['zoom']}")
            print(f"   - 最大缩放: {config['max_zoom']}")
        else:
            print(f"❌ 获取地图配置失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 获取地图配置失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("🌐 请在浏览器中访问: http://127.0.0.1:5001")
    print("📱 系统功能:")
    print("  - 选择区域查看真实矢量数据")
    print("  - 输入GPS坐标进行定位")
    print("  - 控制图层显示/隐藏")

if __name__ == "__main__":
    test_system()
