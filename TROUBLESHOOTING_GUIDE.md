# 高精度矢量地图系统 - 问题解决指南

## 🚨 常见问题及解决方案

### 1. 数据下载超时问题

#### 问题现象
```
道路数据下载失败: HTTPSConnectionPool(host='overpass-api.de', port=443): Read timed out.
```

#### 原因分析
- Overpass API服务器响应慢
- 网络连接不稳定
- 查询数据量过大
- 并发请求过多

#### 解决方案

##### 方案1: 使用改进的下载器
```bash
python improved_vector_downloader.py
```

**改进特性:**
- 增加超时时间到60秒
- 自动重试机制（最多3次）
- 减少并发数到2个
- 断点续传支持
- 进度记录和恢复

##### 方案2: 手动调整参数
```python
# 在 simple_vector_downloader.py 中调整
timeout = 60  # 增加超时时间
max_workers = 2  # 减少并发数
retry_count = 3  # 增加重试次数
```

##### 方案3: 分批下载
```bash
# 先下载小范围数据测试
python simple_vector_downloader.py
# 选择单个城市，如北京
# 选择单个数据类型，如道路数据
```

### 2. 网络连接问题

#### 问题现象
- 连接被拒绝
- 网络超时
- DNS解析失败

#### 解决方案

##### 检查网络连接
```bash
# 测试网络连接
ping overpass-api.de
ping 8.8.8.8
```

##### 使用代理（如需要）
```python
# 在下载器中添加代理支持
proxies = {
    'http': 'http://proxy:port',
    'https': 'https://proxy:port'
}
response = requests.post(url, data=data, proxies=proxies)
```

##### 更换API服务器
```python
# 使用其他Overpass API服务器
base_urls = [
    "https://overpass-api.de/api/interpreter",
    "https://lz4.overpass-api.de/api/interpreter",
    "https://z.overpass-api.de/api/interpreter"
]
```

### 3. 数据文件损坏问题

#### 问题现象
- 文件大小为0
- XML解析错误
- 数据不完整

#### 解决方案

##### 验证文件完整性
```python
def validate_osm_file(file_path):
    """验证OSM文件完整性"""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 检查基本结构
        if root.tag != 'osm':
            return False
        
        # 检查是否有数据
        if len(root) == 0:
            return False
        
        return True
    except Exception as e:
        print(f"文件验证失败: {e}")
        return False
```

##### 重新下载损坏文件
```bash
# 删除损坏文件
rm static/vector_data/北京/roads.osm

# 重新下载
python improved_vector_downloader.py
# 选择重试失败下载
```

### 4. 内存不足问题

#### 问题现象
- 系统运行缓慢
- 内存使用率过高
- 程序崩溃

#### 解决方案

##### 减少并发数
```python
# 在下载器中调整
max_workers = 1  # 减少到1个并发
```

##### 分批处理
```python
# 分批下载数据
def download_in_batches(regions, batch_size=5):
    """分批下载数据"""
    for i in range(0, len(regions), batch_size):
        batch = regions[i:i+batch_size]
        download_batch(batch)
        time.sleep(10)  # 批次间休息
```

##### 清理临时文件
```bash
# 清理临时文件
rm -rf __pycache__/
rm -rf *.pyc
```

### 5. 定位精度问题

#### 问题现象
- 定位不准确
- 坐标偏差大
- 搜索无结果

#### 解决方案

##### 检查数据质量
```python
def check_data_quality(region):
    """检查数据质量"""
    data_dir = Path(f"static/vector_data/{region}")
    
    for data_type in ["roads", "buildings", "pois"]:
        file_path = data_dir / f"{data_type}.osm"
        if file_path.exists():
            # 检查文件大小
            size = file_path.stat().st_size
            if size < 1000:  # 小于1KB
                print(f"警告: {data_type} 数据文件过小")
            
            # 检查数据内容
            try:
                tree = ET.parse(file_path)
                root = tree.getroot()
                count = len(root)
                print(f"{data_type}: {count} 个元素")
            except Exception as e:
                print(f"{data_type} 解析失败: {e}")
```

##### 重新下载高精度数据
```bash
# 使用高精度下载器
python high_precision_system.py
```

### 6. 地图显示问题

#### 问题现象
- 地图不显示
- 数据不加载
- 界面异常

#### 解决方案

##### 检查数据文件
```bash
# 检查数据文件是否存在
ls -la static/vector_data/北京/
```

##### 重启服务
```bash
# 停止服务
Ctrl+C

# 重新启动
python vector_map_viewer.py
```

##### 清除浏览器缓存
- 按 F12 打开开发者工具
- 右键刷新按钮
- 选择"清空缓存并硬性重新加载"

### 7. 性能优化问题

#### 问题现象
- 系统运行缓慢
- 响应时间长
- 资源占用高

#### 解决方案

##### 系统优化
```python
# 调整系统参数
import gc
import psutil

def optimize_system():
    """系统优化"""
    # 垃圾回收
    gc.collect()
    
    # 检查内存使用
    memory = psutil.virtual_memory()
    if memory.percent > 80:
        print("警告: 内存使用率过高")
    
    # 限制并发数
    max_workers = min(2, psutil.cpu_count())
```

##### 数据优化
```python
# 数据压缩
import gzip

def compress_data(file_path):
    """压缩数据文件"""
    with open(file_path, 'rb') as f_in:
        with gzip.open(f"{file_path}.gz", 'wb') as f_out:
            f_out.writelines(f_in)
```

## 🔧 诊断工具

### 1. 系统诊断脚本
```python
#!/usr/bin/env python3
# system_diagnosis.py

import os
import psutil
import requests
import xml.etree.ElementTree as ET
from pathlib import Path

def diagnose_system():
    """系统诊断"""
    print("🔍 系统诊断开始...")
    
    # 检查系统资源
    print(f"CPU使用率: {psutil.cpu_percent()}%")
    print(f"内存使用率: {psutil.virtual_memory().percent}%")
    print(f"磁盘使用率: {psutil.disk_usage('/').percent}%")
    
    # 检查网络连接
    try:
        response = requests.get("https://overpass-api.de", timeout=10)
        print(f"网络连接: 正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"网络连接: 异常 ({e})")
    
    # 检查数据文件
    data_dir = Path("static/vector_data")
    if data_dir.exists():
        regions = list(data_dir.iterdir())
        print(f"数据目录: 存在 ({len(regions)} 个区域)")
        
        for region in regions:
            if region.is_dir():
                files = list(region.glob("*.osm"))
                print(f"  {region.name}: {len(files)} 个文件")
    else:
        print("数据目录: 不存在")

if __name__ == "__main__":
    diagnose_system()
```

### 2. 数据验证脚本
```python
#!/usr/bin/env python3
# data_validation.py

import xml.etree.ElementTree as ET
from pathlib import Path

def validate_all_data():
    """验证所有数据"""
    data_dir = Path("static/vector_data")
    
    for region_dir in data_dir.iterdir():
        if region_dir.is_dir():
            print(f"\n📁 验证 {region_dir.name}:")
            
            for osm_file in region_dir.glob("*.osm"):
                try:
                    tree = ET.parse(osm_file)
                    root = tree.getroot()
                    
                    if root.tag == 'osm' and len(root) > 0:
                        print(f"  ✅ {osm_file.name}: {len(root)} 个元素")
                    else:
                        print(f"  ❌ {osm_file.name}: 数据为空或格式错误")
                        
                except Exception as e:
                    print(f"  ❌ {osm_file.name}: 解析失败 ({e})")

if __name__ == "__main__":
    validate_all_data()
```

## 📞 获取帮助

### 1. 查看日志
```bash
# 查看系统日志
python start_national_system.py 2>&1 | tee system.log

# 查看错误日志
grep "ERROR" system.log
```

### 2. 运行诊断
```bash
# 系统诊断
python system_diagnosis.py

# 数据验证
python data_validation.py

# 精度测试
python test_20m_precision.py
```

### 3. 联系支持
- 提供错误信息
- 描述问题现象
- 提供系统环境
- 附上日志文件

## 🎯 预防措施

### 1. 定期维护
- 清理临时文件
- 检查数据完整性
- 更新系统依赖
- 备份重要数据

### 2. 监控系统
- 监控资源使用
- 检查网络状态
- 验证数据质量
- 测试系统功能

### 3. 优化配置
- 调整并发参数
- 优化超时设置
- 配置缓存策略
- 设置重试机制

---

**注意**: 遇到问题时，请先运行诊断工具，然后根据具体错误信息选择相应的解决方案。
