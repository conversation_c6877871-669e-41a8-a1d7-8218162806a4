#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实矢量数据系统
使用F:\monitor1\static\vector_data中的真实OSM数据
"""

import os
import json
import xml.etree.ElementTree as ET
from flask import Flask, render_template, jsonify, request
import math

class RealVectorSystem:
    def __init__(self):
        self.app = Flask(__name__)
        self.vector_data_path = os.path.join(os.path.dirname(__file__), 'static', 'vector_data')
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('real_vector_map.html')
        
        @self.app.route('/api/regions')
        def get_regions():
            """获取所有可用的区域"""
            regions = []
            if os.path.exists(self.vector_data_path):
                for item in os.listdir(self.vector_data_path):
                    item_path = os.path.join(self.vector_data_path, item)
                    if os.path.isdir(item_path):
                        # 检查是否有OSM数据文件
                        osm_files = [f for f in os.listdir(item_path) if f.endswith('.osm') or f.endswith('.xml')]
                        if osm_files:
                            regions.append({
                                'name': item,
                                'path': item,
                                'files': osm_files
                            })
            return jsonify(regions)
        
        @self.app.route('/api/vector-data/<region>')
        def get_vector_data(region):
            """获取指定区域的矢量数据"""
            try:
                region_path = os.path.join(self.vector_data_path, region)
                if not os.path.exists(region_path):
                    return jsonify({'error': f'区域 {region} 不存在'}), 404
                
                # 解析OSM数据
                data = self.parse_osm_data(region_path)
                return jsonify(data)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/map-config')
        def get_map_config():
            """获取地图配置"""
            return jsonify({
                'center': [39.9042, 116.4074],  # 北京中心
                'zoom': 10,
                'max_zoom': 18,
                'min_zoom': 3
            })
    
    def parse_osm_data(self, region_path):
        """解析OSM数据"""
        data = {
            'roads': {'type': 'FeatureCollection', 'features': []},
            'buildings': {'type': 'FeatureCollection', 'features': []},
            'pois': {'type': 'FeatureCollection', 'features': []},
            'metadata': {
                'region': os.path.basename(region_path),
                'total_features': 0
            }
        }
        
        # 解析道路数据
        roads_file = None
        for filename in ['roads.osm', 'roads.xml']:
            file_path = os.path.join(region_path, filename)
            if os.path.exists(file_path):
                roads_file = file_path
                break
        
        if roads_file:
            data['roads'] = self.parse_osm_file(roads_file, 'road')
        
        # 解析建筑数据
        buildings_file = None
        for filename in ['buildings.osm', 'buildings.xml']:
            file_path = os.path.join(region_path, filename)
            if os.path.exists(file_path):
                buildings_file = file_path
                break
        
        if buildings_file:
            data['buildings'] = self.parse_osm_file(buildings_file, 'building')
        
        # 解析POI数据
        pois_file = None
        for filename in ['pois.osm', 'poi.xml', 'pois.xml']:
            file_path = os.path.join(region_path, filename)
            if os.path.exists(file_path):
                pois_file = file_path
                break
        
        if pois_file:
            data['pois'] = self.parse_osm_file(pois_file, 'poi')
        
        # 计算总要素数
        total_features = (len(data['roads']['features']) + 
                         len(data['buildings']['features']) + 
                         len(data['pois']['features']))
        data['metadata']['total_features'] = total_features
        
        return data
    
    def parse_osm_file(self, file_path, data_type):
        """解析单个OSM文件"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            features = []
            nodes = {}
            
            # 首先收集所有节点
            for node in root.findall('node'):
                node_id = node.get('id')
                lat = float(node.get('lat'))
                lon = float(node.get('lon'))
                nodes[node_id] = [lon, lat]  # GeoJSON格式: [lon, lat]
            
            # 处理道路和建筑
            if data_type in ['road', 'building']:
                for way in root.findall('way'):
                    feature = self.create_way_feature(way, nodes, data_type)
                    if feature:
                        features.append(feature)
            
            # 处理POI
            elif data_type == 'poi':
                for node in root.findall('node'):
                    feature = self.create_poi_feature(node)
                    if feature:
                        features.append(feature)
            
            return {
                'type': 'FeatureCollection',
                'features': features
            }
            
        except Exception as e:
            print(f"解析OSM文件失败 {file_path}: {e}")
            return {'type': 'FeatureCollection', 'features': []}
    
    def create_way_feature(self, way, nodes, data_type):
        """创建道路或建筑要素"""
        try:
            # 获取节点坐标
            coordinates = []
            for nd in way.findall('nd'):
                ref = nd.get('ref')
                if ref in nodes:
                    coordinates.append(nodes[ref])
            
            if len(coordinates) < 2:
                return None
            
            # 创建几何
            if data_type == 'road':
                geometry = {
                    'type': 'LineString',
                    'coordinates': coordinates
                }
            else:  # building
                # 建筑通常是多边形，确保闭合
                if coordinates[0] != coordinates[-1]:
                    coordinates.append(coordinates[0])
                geometry = {
                    'type': 'Polygon',
                    'coordinates': [coordinates]
                }
            
            # 创建属性
            properties = {}
            for tag in way.findall('tag'):
                key = tag.get('k')
                value = tag.get('v')
                properties[key] = value
            
            return {
                'type': 'Feature',
                'geometry': geometry,
                'properties': properties
            }
            
        except Exception as e:
            print(f"创建要素失败: {e}")
            return None
    
    def create_poi_feature(self, node):
        """创建POI要素"""
        try:
            lat = float(node.get('lat'))
            lon = float(node.get('lon'))
            
            # 创建几何
            geometry = {
                'type': 'Point',
                'coordinates': [lon, lat]
            }
            
            # 创建属性
            properties = {}
            for tag in node.findall('tag'):
                key = tag.get('k')
                value = tag.get('v')
                properties[key] = value
            
            return {
                'type': 'Feature',
                'geometry': geometry,
                'properties': properties
            }
            
        except Exception as e:
            print(f"创建POI要素失败: {e}")
            return None
    
    def run(self, host='127.0.0.1', port=5001, debug=False):
        """运行系统"""
        print("🌍 真实矢量数据系统")
        print("=" * 50)
        print(f"🌐 系统将在 http://{host}:{port} 启动")
        print(f"📁 矢量数据路径: {self.vector_data_path}")
        print("🎯 功能特点:")
        print("  - 真实OSM矢量数据")
        print("  - 道路、建筑、POI显示")
        print("  - 高精度地理定位")
        print("=" * 50)
        
        self.app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    system = RealVectorSystem()
    system.run()
