# HTTP 400错误解决指南

## 问题现象
- 下载道路数据失败：HTTP 400错误
- 下载建筑物数据失败：HTTP 400错误
- 请求格式错误

## 解决步骤

### 第一步：运行查询格式调试
```bash
python debug_query_format.py
```
选择选项4进行全部测试，找出具体的问题查询。

### 第二步：测试最简化查询
```bash
python minimal_vector_downloader.py
```
选择选项1测试单个查询，验证基本功能。

### 第三步：检查具体错误
如果仍然出现HTTP 400错误，请：

1. **检查查询语句格式**
   - 确保查询语法正确
   - 检查边界框格式
   - 验证超时设置

2. **检查边界框坐标**
   - 确保有4个坐标
   - 检查坐标范围
   - 验证坐标顺序

3. **检查请求编码**
   - 确保UTF-8编码
   - 检查特殊字符
   - 验证请求头

### 第四步：使用备用方案
如果问题持续存在：

1. **更换API服务器**
   ```python
   # 使用其他Overpass API服务器
   base_urls = [
       "https://overpass-api.de/api/interpreter",
       "https://lz4.overpass-api.de/api/interpreter",
       "https://z.overpass-api.de/api/interpreter"
   ]
   ```

2. **使用更简单的查询**
   ```python
   # 最简化的查询
   query = """
   [out:xml][timeout:30];
   (
     way["highway"="primary"](39.9,116.4,40.0,116.5);
   );
   out geom;
   """
   ```

3. **减少查询范围**
   - 使用更小的边界框
   - 减少查询的数据类型
   - 降低并发数

## 常见问题及解决方案

### 问题1：查询语句格式错误
**解决方案：**
- 使用最简化的查询语句
- 检查语法是否正确
- 验证超时设置

### 问题2：边界框格式错误
**解决方案：**
- 确保有4个坐标
- 检查坐标范围
- 移除多余字符

### 问题3：请求编码问题
**解决方案：**
- 使用UTF-8编码
- 检查特殊字符
- 验证请求头

### 问题4：服务器限制
**解决方案：**
- 减少并发数
- 增加重试间隔
- 使用多个服务器

## 测试建议

1. **先测试最简单的查询**
2. **逐步增加复杂度**
3. **监控错误信息**
4. **记录成功的配置**

## 联系支持

如果问题仍然存在，请提供：
1. 具体的错误信息
2. 查询语句内容
3. 边界框坐标
4. 系统环境信息
