#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合矢量地图系统
整合所有下载的矢量数据到高精度矢量地图系统
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from flask import Flask, render_template, jsonify, request
import threading
import time
from complete_county_town_database import search_complete_county_town

class IntegratedVectorSystem:
    def __init__(self):
        self.app = Flask(__name__)
        self.data_dir = Path("static/vector_data")
        self.regions = {}
        self.famous_places = {}
        self.load_regions()
        self.load_famous_places()
        self.setup_routes()
        
        print(f"🔧 整合矢量地图系统初始化完成")
        print(f"📊 已加载 {len(self.regions)} 个区域")
        print(f"🏛️ 已加载 {len(self.famous_places)} 个著名地标")
    
    def load_regions(self):
        """加载所有区域数据"""
        self.regions = {}
        
        # 省份配置（包含省会城市坐标）
        provinces_config = {
            "北京": {"bbox": [116.0, 39.4, 117.0, 40.2], "center": [116.4, 39.9], "capital": {"name": "北京市", "lat": 39.9042, "lon": 116.4074}},
            "上海": {"bbox": [121.0, 31.0, 122.0, 31.5], "center": [121.5, 31.2], "capital": {"name": "上海市", "lat": 31.2304, "lon": 121.4737}},
            "天津": {"bbox": [116.8, 38.5, 118.0, 40.2], "center": [117.2, 39.1], "capital": {"name": "天津市", "lat": 39.3434, "lon": 117.3616}},
            "重庆": {"bbox": [105.0, 28.0, 110.0, 32.0], "center": [106.5, 29.5], "capital": {"name": "重庆市", "lat": 29.5647, "lon": 106.5507}},
            "河北": {"bbox": [113.0, 36.0, 120.0, 42.0], "center": [116.5, 39.0], "capital": {"name": "石家庄市", "lat": 38.0428, "lon": 114.5149}},
            "山西": {"bbox": [110.0, 34.0, 114.0, 40.0], "center": [112.0, 37.0], "capital": {"name": "太原市", "lat": 37.8706, "lon": 112.5489}},
            "辽宁": {"bbox": [118.0, 38.0, 125.0, 43.0], "center": [121.5, 40.5], "capital": {"name": "沈阳市", "lat": 41.8057, "lon": 123.4315}},
            "吉林": {"bbox": [121.0, 40.0, 131.0, 46.0], "center": [126.0, 43.0], "capital": {"name": "长春市", "lat": 43.8171, "lon": 125.3235}},
            "黑龙江": {"bbox": [121.0, 43.0, 135.0, 53.0], "center": [128.0, 48.0], "capital": {"name": "哈尔滨市", "lat": 45.7732, "lon": 126.6577}},
            "江苏": {"bbox": [116.0, 30.0, 122.0, 35.0], "center": [119.0, 32.5], "capital": {"name": "南京市", "lat": 32.0603, "lon": 118.7969}},
            "浙江": {"bbox": [118.0, 27.0, 123.0, 31.0], "center": [120.5, 29.0], "capital": {"name": "杭州市", "lat": 30.2741, "lon": 120.1551}},
            "安徽": {"bbox": [114.0, 29.0, 120.0, 35.0], "center": [117.0, 32.0], "capital": {"name": "合肥市", "lat": 31.8206, "lon": 117.2272}},
            "福建": {"bbox": [115.0, 23.0, 121.0, 28.0], "center": [118.0, 25.5], "capital": {"name": "福州市", "lat": 26.0745, "lon": 119.2965}},
            "江西": {"bbox": [113.0, 24.0, 118.0, 30.0], "center": [115.5, 27.0], "capital": {"name": "南昌市", "lat": 28.6820, "lon": 115.8579}},
            "山东": {"bbox": [114.0, 34.0, 123.0, 38.0], "center": [118.5, 36.0], "capital": {"name": "济南市", "lat": 36.6512, "lon": 117.1201}},
            "河南": {"bbox": [110.0, 31.0, 117.0, 36.0], "center": [113.5, 33.5], "capital": {"name": "郑州市", "lat": 34.7466, "lon": 113.6254}},
            "湖北": {"bbox": [108.0, 29.0, 116.0, 33.0], "center": [112.0, 31.0], "capital": {"name": "武汉市", "lat": 30.5928, "lon": 114.3055}},
            "湖南": {"bbox": [108.0, 24.0, 114.0, 30.0], "center": [111.0, 27.0], "capital": {"name": "长沙市", "lat": 28.2278, "lon": 112.9388}},
            "广东": {"bbox": [109.0, 20.0, 117.0, 25.0], "center": [113.0, 22.5], "capital": {"name": "广州市", "lat": 23.1291, "lon": 113.2644}},
            "海南": {"bbox": [108.0, 18.0, 111.0, 20.0], "center": [109.5, 19.0], "capital": {"name": "海口市", "lat": 20.0444, "lon": 110.1999}},
            "四川": {"bbox": [97.0, 26.0, 109.0, 34.0], "center": [103.0, 30.0], "capital": {"name": "成都市", "lat": 30.5728, "lon": 104.0668}},
            "贵州": {"bbox": [103.0, 24.0, 109.0, 29.0], "center": [106.0, 26.5], "capital": {"name": "贵阳市", "lat": 26.6470, "lon": 106.6302}},
            "云南": {"bbox": [97.0, 21.0, 106.0, 29.0], "center": [101.5, 25.0], "capital": {"name": "昆明市", "lat": 25.0389, "lon": 102.7183}},
            "陕西": {"bbox": [105.0, 31.0, 111.0, 39.0], "center": [108.0, 35.0], "capital": {"name": "西安市", "lat": 34.3416, "lon": 108.9398}},
            "甘肃": {"bbox": [92.0, 32.0, 109.0, 43.0], "center": [100.5, 37.5], "capital": {"name": "兰州市", "lat": 36.0611, "lon": 103.8343}},
            "青海": {"bbox": [89.0, 31.0, 103.0, 39.0], "center": [96.0, 35.0], "capital": {"name": "西宁市", "lat": 36.6171, "lon": 101.7782}},
            "内蒙古": {"bbox": [97.0, 37.0, 126.0, 53.0], "center": [111.5, 45.0], "capital": {"name": "呼和浩特市", "lat": 40.8414, "lon": 111.7519}},
            "广西": {"bbox": [104.0, 20.0, 112.0, 26.0], "center": [108.0, 23.0], "capital": {"name": "南宁市", "lat": 22.8170, "lon": 108.3669}},
            "西藏": {"bbox": [78.0, 27.0, 99.0, 36.0], "center": [88.5, 31.5], "capital": {"name": "拉萨市", "lat": 29.6521, "lon": 91.1721}},
            "宁夏": {"bbox": [104.0, 35.0, 107.0, 39.0], "center": [105.5, 37.0], "capital": {"name": "银川市", "lat": 38.4872, "lon": 106.2309}},
            "新疆": {"bbox": [73.0, 34.0, 96.0, 49.0], "center": [84.5, 41.5], "capital": {"name": "乌鲁木齐市", "lat": 43.8256, "lon": 87.6168}},
            "香港": {"bbox": [113.8, 22.1, 114.4, 22.6], "center": [114.1, 22.35], "capital": {"name": "香港", "lat": 22.3193, "lon": 114.1694}},
            "澳门": {"bbox": [113.5, 22.1, 113.6, 22.2], "center": [113.55, 22.15], "capital": {"name": "澳门", "lat": 22.1987, "lon": 113.5439}}
        }
        
        for region_name, config in provinces_config.items():
            region_dir = self.data_dir / region_name
            if region_dir.exists():
                files = list(region_dir.glob("*.osm"))
                if len(files) >= 3:  # 至少有3个文件
                    self.regions[region_name] = {
                        "bbox": config["bbox"],
                        "center": config["center"],
                        "capital": config["capital"],
                        "data_files": {
                            "roads": str(region_dir / "roads.osm"),
                            "buildings": str(region_dir / "buildings.osm"),
                            "pois": str(region_dir / "pois.osm")
                        },
                        "status": "complete"
                    }
                else:
                    self.regions[region_name] = {
                        "bbox": config["bbox"],
                        "center": config["center"],
                        "capital": config["capital"],
                        "data_files": {},
                        "status": "incomplete"
                    }
            else:
                self.regions[region_name] = {
                    "bbox": config["bbox"],
                    "center": config["center"],
                    "capital": config["capital"],
                    "data_files": {},
                    "status": "missing"
                }
    
    def load_famous_places(self):
        """加载著名地标"""
        self.famous_places = {
            "天安门": {"lat": 39.9042, "lon": 116.4074, "region": "北京"},
            "故宫": {"lat": 39.9163, "lon": 116.3972, "region": "北京"},
            "长城": {"lat": 40.4319, "lon": 116.5704, "region": "北京"},
            "外滩": {"lat": 31.2397, "lon": 121.4998, "region": "上海"},
            "东方明珠": {"lat": 31.2397, "lon": 121.4998, "region": "上海"},
            "西湖": {"lat": 30.2741, "lon": 120.1551, "region": "浙江"},
            "兵马俑": {"lat": 34.3847, "lon": 109.2734, "region": "陕西"},
            "泰山": {"lat": 36.2667, "lon": 117.1000, "region": "山东"},
            "黄山": {"lat": 30.1333, "lon": 118.1667, "region": "安徽"},
            "九寨沟": {"lat": 33.2600, "lon": 103.9200, "region": "四川"},
            "张家界": {"lat": 29.1275, "lon": 110.4792, "region": "湖南"},
            "桂林山水": {"lat": 25.2742, "lon": 110.2901, "region": "广西"},
            "布达拉宫": {"lat": 29.6558, "lon": 91.1406, "region": "西藏"},
            "莫高窟": {"lat": 40.0419, "lon": 94.8081, "region": "甘肃"},
            "天山天池": {"lat": 43.8833, "lon": 88.1333, "region": "新疆"}
        }
    
    def parse_osm_data(self, file_path):
        """解析OSM数据"""
        if not file_path.exists():
            return {"nodes": [], "ways": [], "relations": []}
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            nodes = []
            ways = []
            relations = []
            
            for element in root:
                if element.tag == "node":
                    node_data = {
                        "id": element.get("id"),
                        "lat": float(element.get("lat")),
                        "lon": float(element.get("lon")),
                        "tags": {}
                    }
                    for tag in element.findall("tag"):
                        node_data["tags"][tag.get("k")] = tag.get("v")
                    nodes.append(node_data)
                
                elif element.tag == "way":
                    way_data = {
                        "id": element.get("id"),
                        "nodes": [],
                        "tags": {}
                    }
                    for nd in element.findall("nd"):
                        way_data["nodes"].append(nd.get("ref"))
                    for tag in element.findall("tag"):
                        way_data["tags"][tag.get("k")] = tag.get("v")
                    ways.append(way_data)
                
                elif element.tag == "relation":
                    relation_data = {
                        "id": element.get("id"),
                        "members": [],
                        "tags": {}
                    }
                    for member in element.findall("member"):
                        relation_data["members"].append({
                            "type": member.get("type"),
                            "ref": member.get("ref"),
                            "role": member.get("role")
                        })
                    for tag in element.findall("tag"):
                        relation_data["tags"][tag.get("k")] = tag.get("v")
                    relations.append(relation_data)
            
            return {"nodes": nodes, "ways": ways, "relations": relations}
        
        except Exception as e:
            print(f"解析OSM文件失败: {file_path}, 错误: {e}")
            return {"nodes": [], "ways": [], "relations": []}
    
    def get_region_data(self, region_name, data_type):
        """获取区域数据"""
        if region_name not in self.regions:
            return {"error": "区域不存在"}
        
        region = self.regions[region_name]
        if region["status"] != "complete":
            return {"error": "区域数据不完整"}
        
        if data_type not in region["data_files"]:
            return {"error": "数据类型不存在"}
        
        file_path = Path(region["data_files"][data_type])
        return self.parse_osm_data(file_path)
    
    def search_poi(self, query):
        """搜索POI"""
        results = []
        query_lower = query.lower()
        
        # 首先搜索县级市和乡镇数据
        county_town_results = search_complete_county_town(query)
        for result in county_town_results:
            results.append({
                "name": result["name"],
                "lat": result["lat"],
                "lon": result["lon"],
                "region": result["region"],
                "type": result["type"],
                "level": result["level"],
                "county": result.get("county", "")
            })
        
        # 搜索著名地标
        for name, info in self.famous_places.items():
            if query_lower in name.lower():
                results.append({
                    "name": name,
                    "lat": info["lat"],
                    "lon": info["lon"],
                    "region": info["region"],
                    "type": "famous_place"
                })
        
        # 搜索区域名称（定位到省会城市）
        for region_name, region in self.regions.items():
            if query_lower in region_name.lower():
                # 使用省会城市坐标而不是区域中心
                capital = region.get("capital", {})
                if capital:
                    results.append({
                        "name": f"{region_name} (区域)",
                        "lat": capital["lat"],
                        "lon": capital["lon"],
                        "region": region_name,
                        "type": "region",
                        "capital_name": capital["name"]
                    })
                else:
                    # 如果没有省会信息，使用区域中心
                    results.append({
                        "name": f"{region_name} (区域)",
                        "lat": region["center"][1],
                        "lon": region["center"][0],
                        "region": region_name,
                        "type": "region"
                    })
        
        # 然后搜索各区域的POI数据
        for region_name, region in self.regions.items():
            if region["status"] == "complete":
                try:
                    poi_data = self.get_region_data(region_name, "pois")
                    if "error" not in poi_data:
                        for node in poi_data["nodes"]:
                            if "name" in node["tags"]:
                                name = node["tags"]["name"]
                                if query_lower in name.lower():
                                    results.append({
                                        "name": name,
                                        "lat": node["lat"],
                                        "lon": node["lon"],
                                        "region": region_name,
                                        "type": "poi",
                                        "amenity": node["tags"].get("amenity", "")
                                    })
                except Exception as e:
                    print(f"搜索区域 {region_name} 时出错: {e}")
                    continue
        
        # 去重并排序
        seen = set()
        unique_results = []
        for result in results:
            key = (result["name"], result["lat"], result["lon"])
            if key not in seen:
                seen.add(key)
                unique_results.append(result)
        
        return unique_results[:30]  # 增加结果数量限制
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            return render_template('integrated_vector_map.html')
        
        @self.app.route('/test')
        def test_page():
            return render_template('simple_test.html')
        
        @self.app.route('/api/regions')
        def get_regions():
            return jsonify(self.regions)
        
        @self.app.route('/api/region/<region_name>/<data_type>')
        def get_region_data_api(region_name, data_type):
            return jsonify(self.get_region_data(region_name, data_type))
        
        @self.app.route('/api/search-poi')
        def search_poi_api():
            query = request.args.get('q', '')
            if not query:
                return jsonify([])
            return jsonify(self.search_poi(query))
        
        @self.app.route('/api/famous-places')
        def get_famous_places():
            return jsonify(self.famous_places)
        
        @self.app.route('/api/quick-locate/<place_name>')
        def quick_locate(place_name):
            if place_name in self.famous_places:
                info = self.famous_places[place_name]
                return jsonify({
                    "name": place_name,
                    "lat": info["lat"],
                    "lon": info["lon"],
                    "region": info["region"]
                })
            return jsonify({"error": "地标不存在"})
        
        @self.app.route('/api/quick-search')
        def quick_search():
            query = request.args.get('q', '').strip()
            if not query:
                return jsonify([])
            
            results = []
            query_lower = query.lower()
            
            # 快速搜索县级市和乡镇数据
            county_town_results = search_complete_county_town(query)
            for result in county_town_results[:5]:  # 限制数量
                results.append({
                    "name": result["name"],
                    "lat": result["lat"],
                    "lon": result["lon"],
                    "region": result["region"],
                    "type": result["type"],
                    "level": result["level"],
                    "county": result.get("county", "")
                })
            
            # 快速搜索著名地标
            for name, info in self.famous_places.items():
                if query_lower in name.lower():
                    results.append({
                        "name": name,
                        "lat": info["lat"],
                        "lon": info["lon"],
                        "region": info["region"],
                        "type": "famous_place"
                    })
            
            # 快速搜索区域（定位到省会城市）
            for region_name, region in self.regions.items():
                if query_lower in region_name.lower():
                    # 使用省会城市坐标而不是区域中心
                    capital = region.get("capital", {})
                    if capital:
                        results.append({
                            "name": f"{region_name}",
                            "lat": capital["lat"],
                            "lon": capital["lon"],
                            "region": region_name,
                            "type": "region",
                            "capital_name": capital["name"]
                        })
                    else:
                        # 如果没有省会信息，使用区域中心
                        results.append({
                            "name": f"{region_name}",
                            "lat": region["center"][1],
                            "lon": region["center"][0],
                            "region": region_name,
                            "type": "region"
                        })
            
            return jsonify(results[:15])  # 增加结果数量
        
        @self.app.route('/api/statistics')
        def get_statistics():
            total_regions = len(self.regions)
            complete_regions = sum(1 for r in self.regions.values() if r["status"] == "complete")
            incomplete_regions = sum(1 for r in self.regions.values() if r["status"] == "incomplete")
            missing_regions = sum(1 for r in self.regions.values() if r["status"] == "missing")
            
            return jsonify({
                "total_regions": total_regions,
                "complete_regions": complete_regions,
                "incomplete_regions": incomplete_regions,
                "missing_regions": missing_regions,
                "completion_rate": round(complete_regions / total_regions * 100, 2) if total_regions > 0 else 0,
                "famous_places_count": len(self.famous_places)
            })
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行系统"""
        print(f"🚀 启动整合矢量地图系统")
        print(f"🌐 访问地址: http://{host}:{port}")
        print(f"📊 系统统计:")
        print(f"  - 总区域数: {len(self.regions)}")
        print(f"  - 完整区域: {sum(1 for r in self.regions.values() if r['status'] == 'complete')}")
        print(f"  - 著名地标: {len(self.famous_places)}")
        
        self.app.run(host=host, port=port, debug=debug)

def main():
    """主函数"""
    system = IntegratedVectorSystem()
    
    print("🔧 整合矢量地图系统")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 启动矢量地图系统")
        print("2. 查看系统统计")
        print("3. 测试搜索功能")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 启动矢量地图系统...")
            system.run()
        
        elif choice == "2":
            print("\n📊 系统统计:")
            total_regions = len(system.regions)
            complete_regions = sum(1 for r in system.regions.values() if r["status"] == "complete")
            incomplete_regions = sum(1 for r in system.regions.values() if r["status"] == "incomplete")
            missing_regions = sum(1 for r in system.regions.values() if r["status"] == "missing")
            
            print(f"  - 总区域数: {total_regions}")
            print(f"  - 完整区域: {complete_regions}")
            print(f"  - 不完整区域: {incomplete_regions}")
            print(f"  - 缺失区域: {missing_regions}")
            print(f"  - 完成率: {round(complete_regions / total_regions * 100, 2) if total_regions > 0 else 0}%")
            print(f"  - 著名地标: {len(system.famous_places)}")
        
        elif choice == "3":
            query = input("\n请输入搜索关键词: ").strip()
            if query:
                results = system.search_poi(query)
                print(f"\n🔍 搜索结果 ({len(results)} 个):")
                for result in results[:10]:  # 显示前10个结果
                    print(f"  - {result['name']} ({result['region']}) - {result['type']}")
            else:
                print("❌ 请输入搜索关键词")
        
        elif choice == "4":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
