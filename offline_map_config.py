#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线地图配置模块
管理OpenMapTiles离线地图的配置和服务
"""

import json
import os
from pathlib import Path
from flask import Flask, send_file, abort, jsonify

class OfflineMapConfig:
    """离线地图配置管理"""
    
    def __init__(self, base_dir=None):
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.config_file = self.base_dir / "map_config.json"
        
        # 默认配置
        self.default_config = {
            "name": "野外深机井监控地图",
            "description": "基于OpenMapTiles的离线地图",
            "version": "1.0.0",
            "minzoom": 1,
            "maxzoom": 18,
            "center": [116.4074, 39.9042],  # 北京坐标 [lng, lat]
            "bounds": [115.0, 38.5, 118.0, 41.0],  # 地图边界 [west, south, east, north]
            "format": "png",
            "type": "baselayer",
            "attribution": "© OpenMapTiles © OpenStreetMap contributors",
            "tile_sources": [
                {
                    "name": "local_tiles",
                    "url": "/static/tiles/{z}/{x}/{y}.png",
                    "priority": 1,
                    "description": "本地离线瓦片"
                },
                {
                    "name": "tile_server",
                    "url": "http://localhost:8080/tiles/{z}/{x}/{y}.png",
                    "priority": 2,
                    "description": "本地瓦片服务器"
                },
                {
                    "name": "online_backup",
                    "url": "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                    "priority": 3,
                    "description": "在线备用地图"
                }
            ],
            "regions": {
                "beijing": {
                    "name": "北京地区",
                    "center": [116.4074, 39.9042],
                    "bounds": [115.4, 39.4, 117.4, 40.4],
                    "zoom_levels": [10, 12, 15, 18]
                },
                "shanghai": {
                    "name": "上海地区", 
                    "center": [121.4737, 31.2304],
                    "bounds": [120.8, 30.7, 122.2, 31.8],
                    "zoom_levels": [10, 12, 15, 18]
                }
            }
        }
    
    def load_config(self):
        """加载地图配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return {**self.default_config, **config}
            except Exception as e:
                print(f"配置文件加载失败: {e}")
                return self.default_config
        else:
            return self.default_config
    
    def save_config(self, config=None):
        """保存地图配置"""
        config = config or self.default_config
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"配置文件保存失败: {e}")
            return False
    
    def get_tile_stats(self):
        """获取瓦片统计信息"""
        stats = {
            "total_tiles": 0,
            "zoom_levels": {},
            "size_mb": 0,
            "regions": {}
        }
        
        if not self.tiles_dir.exists():
            return stats
        
        total_size = 0
        for zoom_dir in self.tiles_dir.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom = int(zoom_dir.name)
                zoom_tiles = 0
                zoom_size = 0
                
                for x_dir in zoom_dir.iterdir():
                    if x_dir.is_dir() and x_dir.name.isdigit():
                        for tile_file in x_dir.glob("*.png"):
                            zoom_tiles += 1
                            tile_size = tile_file.stat().st_size
                            zoom_size += tile_size
                            total_size += tile_size
                
                stats["zoom_levels"][zoom] = {
                    "tiles": zoom_tiles,
                    "size_mb": round(zoom_size / 1024 / 1024, 2)
                }
                stats["total_tiles"] += zoom_tiles
        
        stats["size_mb"] = round(total_size / 1024 / 1024, 2)
        return stats
    
    def check_tile_coverage(self, lat, lng, zoom_levels=None):
        """检查指定位置的瓦片覆盖情况"""
        zoom_levels = zoom_levels or [10, 12, 15, 18]
        coverage = {}
        
        for zoom in zoom_levels:
            # 计算瓦片坐标
            import math
            x = int((lng + 180.0) / 360.0 * (1 << zoom))
            y = int((1.0 - math.asinh(math.tan(math.radians(lat))) / math.pi) / 2.0 * (1 << zoom))
            
            tile_path = self.tiles_dir / str(zoom) / str(x) / f"{y}.png"
            coverage[zoom] = {
                "x": x,
                "y": y,
                "exists": tile_path.exists(),
                "path": str(tile_path) if tile_path.exists() else None
            }
        
        return coverage
    
    def create_tile_server_app(self):
        """创建瓦片服务器Flask应用"""
        app = Flask(__name__)
        
        @app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
        def serve_tile(z, x, y):
            """提供地图瓦片"""
            tile_path = self.tiles_dir / str(z) / str(x) / f"{y}.png"
            
            if tile_path.exists():
                return send_file(tile_path, mimetype='image/png')
            else:
                # 返回透明瓦片
                abort(404)
        
        @app.route('/tiles/<int:z>/<int:x>/<int:y>')
        def serve_tile_no_ext(z, x, y):
            """提供地图瓦片 (无扩展名)"""
            return serve_tile(z, x, y)
        
        @app.route('/map/config')
        def get_map_config():
            """获取地图配置"""
            return jsonify(self.load_config())
        
        @app.route('/map/stats')
        def get_tile_stats():
            """获取瓦片统计"""
            return jsonify(self.get_tile_stats())
        
        @app.route('/map/coverage/<float:lat>/<float:lng>')
        def check_coverage(lat, lng):
            """检查位置覆盖"""
            coverage = self.check_tile_coverage(lat, lng)
            return jsonify(coverage)
        
        return app

# 全局配置实例
offline_map = OfflineMapConfig()

def setup_offline_map():
    """设置离线地图"""
    print("🗺️  设置离线地图配置...")
    
    # 创建目录
    offline_map.tiles_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存默认配置
    if offline_map.save_config():
        print("✅ 地图配置已保存")
    
    # 显示统计信息
    stats = offline_map.get_tile_stats()
    print(f"📊 瓦片统计: {stats['total_tiles']} 个瓦片, {stats['size_mb']} MB")
    
    return True

if __name__ == "__main__":
    setup_offline_map()