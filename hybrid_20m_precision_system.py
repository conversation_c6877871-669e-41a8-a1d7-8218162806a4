#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合20米精度系统
使用在线底图 + 本地OSM矢量数据叠加
确保地图立即可用，同时提供20米精度
"""

import os
import sys
import json
import math
import time
import osmium
from pathlib import Path
from flask import Flask, render_template, jsonify, request
import threading

class PrecisionOSMProcessor(osmium.SimpleHandler):
    """精度OSM处理器 - 专门提取矢量数据"""
    
    def __init__(self, bbox):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox
        self.nodes = {}
        self.roads = []
        self.buildings = []
        self.pois = []
        self.water_features = []
        self.node_count = 0
        
    def node(self, n):
        """处理节点"""
        self.node_count += 1
        
        # 限制处理数量
        if self.node_count > 30000:
            return
            
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 8),
                'lat': round(n.location.lat, 8),
                'tags': dict(n.tags)
            }
            
            # 检查POI
            if n.tags:
                poi_tags = ['amenity', 'shop', 'tourism', 'leisure', 'office']
                if any(tag in n.tags for tag in poi_tags):
                    self.pois.append({
                        'id': n.id,
                        'lon': round(n.location.lon, 8),
                        'lat': round(n.location.lat, 8),
                        'tags': dict(n.tags)
                    })
    
    def way(self, w):
        """处理路径"""
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if len(relevant_nodes) >= 2:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)

class Hybrid20mPrecisionSystem:
    """混合20米精度系统"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.cache_dir = self.base_dir / "static" / "vector_cache"
        
        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('hybrid_20m_precision.html')
        
        @self.app.route('/api/vector-overlay/<int:z>/<int:x>/<int:y>')
        def get_vector_overlay(z, x, y):
            """获取矢量叠加数据"""
            try:
                # 检查缓存
                cache_file = self.cache_dir / f"{z}_{x}_{y}.json"
                if cache_file.exists():
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        return jsonify(json.load(f))
                
                # 生成新数据
                bbox = self.get_tile_bbox(x, y, z)
                vector_data = self.extract_vector_data(bbox)
                
                # 缓存数据
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(vector_data, f, ensure_ascii=False, indent=2)
                
                return jsonify(vector_data)
                
            except Exception as e:
                print(f"获取矢量数据失败 {z}/{x}/{y}: {e}")
                return jsonify({
                    'roads': {'type': 'FeatureCollection', 'features': []},
                    'buildings': {'type': 'FeatureCollection', 'features': []},
                    'pois': {'type': 'FeatureCollection', 'features': []},
                    'water': {'type': 'FeatureCollection', 'features': []}
                })
        
        @self.app.route('/api/generate-precision-data')
        def generate_precision_data():
            """生成精度数据"""
            lat = request.args.get('lat', type=float)
            lon = request.args.get('lon', type=float)
            
            if lat is None or lon is None:
                return jsonify({'error': '缺少坐标参数'}), 400
            
            # 异步生成
            threading.Thread(
                target=self.generate_area_precision_data,
                args=(lat, lon)
            ).start()
            
            return jsonify({
                'status': 'started',
                'message': f'开始生成 ({lat}, {lon}) 的精度数据'
            })
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def extract_vector_data(self, bbox):
        """提取矢量数据"""
        try:
            print(f"提取矢量数据: {bbox}")
            start_time = time.time()
            
            processor = PrecisionOSMProcessor(bbox)
            processor.apply_file(str(self.osm_file))
            
            # 转换为GeoJSON
            vector_data = {
                'roads': self.convert_to_geojson(processor.roads, processor.nodes, 'LineString'),
                'buildings': self.convert_to_geojson(processor.buildings, processor.nodes, 'Polygon'),
                'pois': self.convert_pois_to_geojson(processor.pois),
                'water': self.convert_to_geojson(processor.water_features, processor.nodes, 'Polygon')
            }
            
            elapsed = time.time() - start_time
            print(f"矢量数据提取完成，用时 {elapsed:.1f}秒")
            
            return vector_data
            
        except Exception as e:
            print(f"提取矢量数据失败: {e}")
            return {
                'roads': {'type': 'FeatureCollection', 'features': []},
                'buildings': {'type': 'FeatureCollection', 'features': []},
                'pois': {'type': 'FeatureCollection', 'features': []},
                'water': {'type': 'FeatureCollection', 'features': []}
            }
    
    def convert_to_geojson(self, ways, nodes, geometry_type):
        """转换为GeoJSON"""
        features = []
        
        for way in ways:
            coordinates = []
            for node_id in way['nodes']:
                if node_id in nodes:
                    node = nodes[node_id]
                    coordinates.append([node['lon'], node['lat']])
            
            if len(coordinates) < 2:
                continue
            
            if geometry_type == 'Polygon':
                if coordinates[0] != coordinates[-1]:
                    coordinates.append(coordinates[0])
                geometry = {
                    'type': 'Polygon',
                    'coordinates': [coordinates]
                }
            else:  # LineString
                geometry = {
                    'type': 'LineString',
                    'coordinates': coordinates
                }
            
            properties = way['tags'].copy()
            properties['precision'] = '20米'
            properties['coordinate_precision'] = 8
            
            feature = {
                'type': 'Feature',
                'geometry': geometry,
                'properties': properties
            }
            features.append(feature)
        
        return {
            'type': 'FeatureCollection',
            'features': features
        }
    
    def convert_pois_to_geojson(self, pois):
        """转换POI为GeoJSON"""
        features = []
        
        for poi in pois:
            properties = poi['tags'].copy()
            properties['precision'] = '20米'
            properties['coordinate_precision'] = 8
            
            feature = {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': [poi['lon'], poi['lat']]
                },
                'properties': properties
            }
            features.append(feature)
        
        return {
            'type': 'FeatureCollection',
            'features': features
        }
    
    def generate_area_precision_data(self, lat, lon):
        """生成区域精度数据"""
        print(f"生成区域精度数据: ({lat}, {lon})")
        
        # 生成周围的矢量数据
        zoom_levels = [16, 17]
        
        for zoom in zoom_levels:
            center_x, center_y = self.deg2num(lat, lon, zoom)
            
            # 生成3x3区域
            for dx in range(-1, 2):
                for dy in range(-1, 2):
                    tile_x = center_x + dx
                    tile_y = center_y + dy
                    
                    max_coord = 2 ** zoom
                    if 0 <= tile_x < max_coord and 0 <= tile_y < max_coord:
                        bbox = self.get_tile_bbox(tile_x, tile_y, zoom)
                        vector_data = self.extract_vector_data(bbox)
                        
                        # 保存缓存
                        cache_file = self.cache_dir / f"{zoom}_{tile_x}_{tile_y}.json"
                        with open(cache_file, 'w', encoding='utf-8') as f:
                            json.dump(vector_data, f, ensure_ascii=False, indent=2)
        
        print(f"区域精度数据生成完成: ({lat}, {lon})")
    
    def run(self, host='127.0.0.1', port=5005, debug=False):
        """运行系统"""
        print("🎯 混合20米精度离线地图系统")
        print("=" * 60)
        print(f"🌐 系统地址: http://{host}:{port}")
        print(f"📁 OSM文件: {self.osm_file}")
        print(f"📁 缓存目录: {self.cache_dir}")
        print("🎯 特性:")
        print("  - 在线底图 + 本地矢量数据")
        print("  - 立即可用的地图显示")
        print("  - 20米精度矢量叠加")
        print("  - 8位小数坐标精度")
        print("  - 智能缓存机制")
        print("=" * 60)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            print("⚠️  将仅使用在线底图")
        
        self.app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    system = Hybrid20mPrecisionSystem()
    system.run()
