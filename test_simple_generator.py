#!/usr/bin/env python3
"""
测试简化生成器
"""

from simple_level20_generator import SimpleLevel20Generator

def main():
    print("🧪 测试简化生成器")
    
    generator = SimpleLevel20Generator()
    
    # 获取18级瓦片
    tiles = generator.get_level18_tiles()
    print(f"找到 {len(tiles)} 个18级瓦片")
    
    if tiles:
        print("前5个瓦片:")
        for tile in tiles[:5]:
            print(f"  {tile}")
        
        # 测试生成一个瓦片
        print("\n测试生成第一个瓦片...")
        success, message = generator.generate_level20_tile(tiles[0])
        print(f"结果: {success} - {message}")

if __name__ == "__main__":
    main()
