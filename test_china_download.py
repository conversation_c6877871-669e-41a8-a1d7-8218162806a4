#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中国瓦片下载功能
先下载小范围瓦片验证
"""

from download_china_18 import ChinaTileDownloader

def test_download():
    """测试下载功能"""
    print("🧪 测试中国瓦片下载功能")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    
    # 显示可用地图源
    print("可用地图源:")
    for key, config in downloader.sources.items():
        print(f"  {key}: {config['name']}")
    
    # 测试OSM地图源
    print(f"\n🔍 测试OSM地图源")
    downloader.set_source('osm')
    
    # 下载北京天安门附近的小范围瓦片
    print("   下载北京天安门附近18级瓦片...")
    
    # 计算北京天安门对应的瓦片坐标
    x, y = downloader.deg2num(39.9042, 116.4074, 18)
    print(f"   北京天安门18级瓦片坐标: {x}, {y}")
    
    # 下载中心瓦片周围的几个瓦片
    test_coords = [
        (x, y),      # 中心
        (x-1, y),    # 左边
        (x+1, y),    # 右边
        (x, y-1),    # 上边
        (x, y+1),    # 下边
    ]
    
    success_count = 0
    for test_x, test_y in test_coords:
        success, message = downloader.download_tile(test_x, test_y, 18)
        if success:
            success_count += 1
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
    
    print(f"\n   测试结果: {success_count}/{len(test_coords)} 成功")
    
    # 测试百度地图源
    print(f"\n🔍 测试百度地图源")
    downloader.set_source('baidu')
    
    # 重置统计
    downloader.stats = {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}
    
    success_count = 0
    for test_x, test_y in test_coords:
        success, message = downloader.download_tile(test_x, test_y, 18)
        if success:
            success_count += 1
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
    
    print(f"\n   测试结果: {success_count}/{len(test_coords)} 成功")
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    test_download()
