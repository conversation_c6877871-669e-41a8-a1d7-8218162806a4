#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动修复后的离线地图
"""

import subprocess
import sys
import time
from pathlib import Path
import webbrowser

def check_requirements():
    """检查依赖"""
    required_packages = ['flask', 'pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            if package == 'pillow':
                try:
                    __import__('PIL')
                except ImportError:
                    missing_packages.append('pillow')
            else:
                missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def main():
    """主函数"""
    print("🗺️  启动离线地图系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 检查瓦片目录
    tiles_dir = Path("static/tiles")
    if not tiles_dir.exists() or not any(tiles_dir.iterdir()):
        print("⚠️  瓦片目录为空，正在生成瓦片...")
        try:
            subprocess.run([sys.executable, "fix_offline_map.py"], check=True)
        except subprocess.CalledProcessError:
            print("❌ 瓦片生成失败")
            return
    
    # 启动服务器
    print("🚀 启动地图服务器...")
    try:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Flask服务器
        subprocess.run([sys.executable, "improved_tileserver.py"])
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()