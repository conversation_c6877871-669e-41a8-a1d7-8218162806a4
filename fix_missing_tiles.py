#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缺失的瓦片
生成缺失的离线瓦片以确保地图完整性
"""

import os
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import time

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def create_offline_tile(z, x, y, tiles_dir):
    """创建离线瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    if tile_file.exists():
        return True
    
    try:
        # 创建256x256的瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')
        draw = ImageDraw.Draw(img)
        
        # 绘制网格
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
            draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
        
        # 添加瓦片信息
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            try:
                font = ImageFont.truetype("DejaVuSans.ttf", 16)
            except:
                font = ImageFont.load_default()
        
        text = f"Z{z} X{x} Y{y}"
        try:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        except:
            text_width, text_height = 80, 20
        
        text_x = (256 - text_width) // 2
        text_y = (256 - text_height) // 2
        
        # 绘制背景框
        draw.rectangle([text_x-5, text_y-5, text_x+text_width+5, text_y+text_height+5], 
                      fill='white', outline='black')
        
        # 绘制文字
        try:
            draw.text((text_x, text_y), text, fill='black', font=font)
        except:
            draw.text((text_x, text_y), text, fill='black')
        
        # 根据缩放级别添加不同特征
        if z >= 11:
            # 模拟道路网络
            draw.line([(50, 50), (200, 200)], fill='#ffff00', width=3)
            draw.line([(200, 50), (50, 200)], fill='#ffff00', width=2)
            draw.line([(128, 0), (128, 256)], fill='#ffff00', width=2)
            draw.line([(0, 128), (256, 128)], fill='#ffff00', width=2)
        
        if z >= 12:
            # 模拟建筑区域
            draw.rectangle([80, 80, 120, 120], fill='#d3d3d3', outline='black')
            draw.rectangle([140, 140, 180, 180], fill='#d3d3d3', outline='black')
            draw.rectangle([60, 160, 100, 200], fill='#d3d3d3', outline='black')
        
        # 保存图像
        img.save(tile_file, 'PNG')
        return True
        
    except Exception as e:
        print(f"  ❌ 创建瓦片失败: {z}/{x}/{y} - {e}")
        return False

def fix_missing_tiles_for_zoom(zoom, tiles_dir, center_lat=39.9042, center_lon=116.4074, radius_km=50):
    """修复指定缩放级别的缺失瓦片"""
    print(f"🔧 修复缩放级别 {zoom} 的缺失瓦片...")
    
    # 计算中心瓦片坐标
    center_x, center_y = deg2num(center_lat, center_lon, zoom)
    print(f"   📍 中心瓦片坐标: {center_x}, {center_y}")
    
    # 计算需要生成的瓦片范围（基于半径）
    lat_delta = radius_km / 111.0
    lon_delta = radius_km / (111.0 * math.cos(math.radians(center_lat)))
    
    # 计算边界坐标
    min_lat = center_lat - lat_delta
    max_lat = center_lat + lat_delta
    min_lon = center_lon - lon_delta
    max_lon = center_lon + lon_delta
    
    # 转换为瓦片坐标
    min_x, max_y = deg2num(min_lat, min_lon, zoom)  # 注意：y坐标是反的
    max_x, min_y = deg2num(max_lat, max_lon, zoom)
    
    print(f"   📦 瓦片范围: X({min_x}-{max_x}) Y({min_y}-{max_y})")
    
    total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
    generated = 0
    skipped = 0
    failed = 0
    
    print(f"   🎯 计划生成 {total_tiles} 个瓦片...")
    
    # 生成缺失的瓦片
    for x in range(min_x, max_x + 1):
        for y in range(min_y, max_y + 1):
            tile_path = tiles_dir / str(zoom) / str(x) / f"{y}.png"
            
            if not tile_path.exists():
                if create_offline_tile(zoom, x, y, tiles_dir):
                    generated += 1
                    if generated % 50 == 0:
                        print(f"     ✅ 已生成 {generated} 个瓦片...")
                else:
                    failed += 1
            else:
                skipped += 1
    
    print(f"   ✅ 修复完成!")
    print(f"   📊 总计: {total_tiles} 个瓦片")
    print(f"   ✅ 新生成: {generated} 个")
    print(f"   ⏭️ 已存在: {skipped} 个")
    print(f"   ❌ 失败: {failed} 个")
    
    return generated, failed

def main():
    """主函数"""
    print("🛠️  缺失瓦片修复工具")
    print("=" * 50)
    
    # 瓦片目录
    tiles_dir = Path(__file__).parent / "static" / "tiles"
    tiles_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 瓦片目录: {tiles_dir}")
    
    # 需要修复的缩放级别（根据之前的检查结果）
    zoom_levels_to_fix = [17, 18]
    
    total_generated = 0
    total_failed = 0
    
    for zoom in zoom_levels_to_fix:
        print(f"\n🔧 开始修复缩放级别 {zoom}...")
        generated, failed = fix_missing_tiles_for_zoom(zoom, tiles_dir)
        total_generated += generated
        total_failed += failed
        time.sleep(1)  # 避免过于频繁的操作
    
    print(f"\n" + "=" * 50)
    print(f"🎉 修复完成!")
    print(f"📊 总计生成: {total_generated} 个瓦片")
    print(f"❌ 失败: {total_failed} 个")
    
    if total_generated > 0:
        print(f"\n🚀 建议重新运行验证脚本确认修复结果:")
        print(f"   python verify_tile_integrity.py")

if __name__ == "__main__":
    main()