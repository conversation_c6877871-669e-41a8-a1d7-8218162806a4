#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的OSM数据处理器
基于腾讯元宝建议的分阶段测试方案
"""

import osmium
import json
import sqlite3
from pathlib import Path
from PIL import Image, ImageDraw
import math
from tqdm import tqdm

class EnhancedOSMProcessor(osmium.SimpleHandler):
    def __init__(self, bbox, zoom_level):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox  # [min_lon, min_lat, max_lon, max_lat]
        self.zoom = zoom_level
        self.nodes = {}
        self.ways = []
        self.buildings = []
        self.roads = []
        self.water_features = []
        
    def node(self, n):
        """处理节点数据"""
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            self.nodes[n.id] = {
                'lon': n.location.lon,
                'lat': n.location.lat,
                'tags': dict(n.tags)
            }
    
    def way(self, w):
        """处理路径数据 - 关键：不过度过滤高层级数据"""
        if any(n.ref in self.nodes for n in w.nodes):
            way_data = {
                'id': w.id,
                'nodes': [n.ref for n in w.nodes if n.ref in self.nodes],
                'tags': dict(w.tags)
            }
            
            # 分类存储不同类型的要素
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                # 确保高层级道路数据不被过滤
                highway_type = w.tags['highway']
                if (self.zoom <= 11 and highway_type in ['motorway', 'trunk', 'primary']) or \
                   (self.zoom > 11):  # 高层级保留所有道路类型
                    self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)
            
            self.ways.append(way_data)

class RealTileGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def generate_test_tile(self, x, y, zoom):
        """生成测试瓦片 - 微型测试区域方案"""
        bbox = self.get_tile_bbox(x, y, zoom)
        
        # print(f"处理瓦片 {zoom}/{x}/{y}, 边界: {bbox}")
        
        # 从OSM文件中提取数据
        try:
            processor = EnhancedOSMProcessor(bbox, zoom)
            processor.apply_file(str(self.osm_file))
            
            # print(f"提取数据: {len(processor.nodes)}个节点, {len(processor.roads)}条道路, {len(processor.buildings)}个建筑")
            
            # 创建高质量瓦片图像
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换函数
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
                return (max(0, min(255, px)), max(0, min(255, py)))
            
            # 绘制水体（底层）
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4')
            
            # 绘制建筑物（中层）
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    # 根据缩放级别调整建筑物显示
                    if zoom >= 14:
                        draw.polygon(points, fill='#dddddd', outline='#999999')
                    elif zoom >= 12:
                        # 简化显示
                        center_x = sum(p[0] for p in points) // len(points)
                        center_y = sum(p[1] for p in points) // len(points)
                        draw.rectangle([center_x-2, center_y-2, center_x+2, center_y+2], 
                                     fill='#cccccc')
            
            # 绘制道路（顶层）
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 根据道路类型和缩放级别设置样式
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = max(1, 6 - (15 - zoom))
                elif highway_type in ['primary', 'secondary']:
                    color = '#ffcc00'
                    width = max(1, 4 - (15 - zoom))
                elif highway_type in ['tertiary', 'residential'] and zoom >= 12:
                    color = '#ffffff'
                    width = max(1, 3 - (15 - zoom))
                elif highway_type in ['service', 'track'] and zoom >= 14:
                    color = '#cccccc'
                    width = 1
                else:
                    continue  # 跳过不适合当前缩放级别的道路
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路线段
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 添加调试信息
            debug_text = f"Z{zoom}/X{x}/Y{y}\n{len(processor.roads)}R {len(processor.buildings)}B"
            draw.text((5, 5), debug_text, fill='#333333')
            
            return img, len(processor.roads) + len(processor.buildings) > 0
            
        except Exception as e:
            print(f"处理OSM数据失败: {e}")
            # 返回错误指示瓦片
            img = Image.new('RGB', (256, 256), color='#ffcccc')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), f"ERROR: {str(e)[:30]}", fill='red')
            return img, False
    
    def generate_and_save_tile(self, x, y, zoom):
        """生成并保存瓦片"""
        tile_dir = self.tiles_dir / str(zoom) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        img, has_data = self.generate_test_tile(x, y, zoom)
        
        # 保存瓦片
        img.save(tile_file, 'PNG', optimize=True)
        
        # 检查文件大小
        file_size = tile_file.stat().st_size
        # print(f"瓦片 {zoom}/{x}/{y}: {file_size}字节, 包含数据: {has_data}")
        
        return file_size > 5000 and has_data

def main():
    """分阶段测试方案"""
    generator = RealTileGenerator()
    
    # 阶段一：验证原始数据
    print("=== 阶段一：验证OSM数据文件 ===")
    if not generator.osm_file.exists():
        print(f"❌ OSM文件不存在: {generator.osm_file}")
        return
    
    file_size_gb = generator.osm_file.stat().st_size / (1024**3)
    print(f"✅ OSM文件: {file_size_gb:.2f}GB")
    
    # 阶段二：生成简化测试区域（北京中心小范围）
    print("\n=== 阶段二：微型测试区域 ===")
    center_lat, center_lon = 39.9042, 116.4074  # 北京天安门
    
    success_count = 0
    
    tasks = []
    for zoom in [12, 14, 16]:  # 测试不同缩放级别
        center_x, center_y = generator.deg2num(center_lat, center_lon, zoom)
        
        # 只生成中心附近的几个瓦片
        for dx in range(-1, 2):
            for dy in range(-1, 2):
                x, y = center_x + dx, center_y + dy
                tasks.append({'x': x, 'y': y, 'zoom': zoom})

    total_count = len(tasks)
    if total_count == 0:
        print("没有需要生成的瓦片。")
        return
        
    print(f"\n准备生成 {total_count} 个瓦片...")

    with tqdm(tasks, desc="瓦片生成进度", unit="tile") as pbar:
        for task in pbar:
            x, y, zoom = task['x'], task['y'], task['zoom']
            pbar.set_description(f"处理中: Z{zoom}/{x}/{y}")
            if generator.generate_and_save_tile(x, y, zoom):
                success_count += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"成功生成: {success_count}/{total_count} 个瓦片")
    if total_count > 0:
        print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count > 0:
        print(f"\n🎉 真实地图瓦片生成成功！")
        print(f"瓦片位置: {generator.tiles_dir}")
        print(f"建议运行: python start_fixed_map.py")
    else:
        print(f"\n❌ 瓦片生成失败，请检查OSM数据处理逻辑")

if __name__ == "__main__":
    main()