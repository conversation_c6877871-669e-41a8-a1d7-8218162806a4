#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的OSM数据处理器
支持按省份/区域裁剪以提高性能和成功率
优化版本 - 提高生成速度
"""

import osmium
import json
import sqlite3
from pathlib import Path
from PIL import Image, ImageDraw
import math
from tqdm import tqdm
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import threading
import argparse
import time

# 各省份/直辖市的边界框 [min_lon, min_lat, max_lon, max_lat]
PROVINCE_BBOXES = {
    "北京": [115.7, 39.4, 117.4, 41.6],
    "上海": [120.9, 30.7, 122.1, 31.9],
    "天津": [116.7, 38.5, 118.1, 40.2],
    "重庆": [105.3, 28.2, 110.2, 32.2],
    "河北": [113.3, 36.0, 119.9, 42.6],
    "山西": [110.2, 34.6, 114.6, 40.7],
    "辽宁": [118.8, 38.7, 125.8, 43.5],
    "吉林": [121.3, 40.9, 131.3, 46.2],
    "黑龙江": [121.1, 43.4, 135.1, 53.6],
    "江苏": [116.2, 30.7, 121.9, 35.1],
    "浙江": [118.0, 27.1, 123.0, 31.2],
    "安徽": [114.9, 29.4, 119.6, 34.6],
    "福建": [115.8, 23.5, 120.7, 28.3],
    "江西": [113.6, 24.5, 118.5, 30.0],
    "山东": [114.8, 34.4, 122.7, 38.3],
    "河南": [110.3, 31.4, 116.6, 36.4],
    "湖北": [108.4, 29.0, 116.1, 33.2],
    "湖南": [108.8, 24.6, 114.2, 30.1],
    "广东": [109.7, 20.1, 117.3, 25.5],
    "海南": [108.6, 18.2, 111.1, 20.2],
    "四川": [102.1, 26.0, 108.6, 34.2],
    "贵州": [103.6, 24.6, 109.4, 29.2],
    "云南": [97.5, 21.5, 106.0, 29.2],
    "陕西": [105.5, 31.7, 111.2, 39.6],
    "甘肃": [92.1, 32.7, 103.4, 42.8],
    "青海": [89.4, 31.6, 102.4, 39.8],
    "台湾": [120.0, 21.9, 122.0, 25.3],
    "内蒙古": [97.2, 37.4, 126.0, 53.3],
    "广西": [104.5, 20.9, 112.0, 26.4],
    "西藏": [78.4, 26.9, 99.1, 36.1],
    "宁夏": [104.2, 35.2, 107.4, 39.3],
    "新疆": [73.5, 34.3, 96.3, 49.2],
    "香港": [113.8, 22.1, 114.5, 22.6],
    "澳门": [113.5, 22.1, 113.6, 22.2]
}

class EnhancedOSMProcessor(osmium.SimpleHandler):
    def __init__(self, bbox, zoom_level):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox  # [min_lon, min_lat, max_lon, max_lat]
        self.zoom = zoom_level
        self.nodes = {}
        self.ways = []
        self.buildings = []
        self.roads = []
        self.water_features = []
        
    def node(self, n):
        """处理节点数据"""
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            self.nodes[n.id] = {
                'lon': n.location.lon,
                'lat': n.location.lat,
                'tags': dict(n.tags)
            }
    
    def way(self, w):
        """处理路径数据 - 关键：不过度过滤高层级数据"""
        if any(n.ref in self.nodes for n in w.nodes):
            way_data = {
                'id': w.id,
                'nodes': [n.ref for n in w.nodes if n.ref in self.nodes],
                'tags': dict(w.tags)
            }
            
            # 分类存储不同类型的要素
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                # 根据缩放级别和道路类型进行过滤
                highway_type = w.tags['highway']
                # 所有缩放级别都保留主要道路类型
                if highway_type in ['motorway', 'trunk', 'primary', 'secondary']:
                    self.roads.append(way_data)
                # 中低缩放级别也保留三级道路
                elif highway_type in ['tertiary', 'residential'] and self.zoom >= 10:
                    self.roads.append(way_data)
                # 高缩放级别保留所有道路类型
                elif self.zoom >= 12:
                    self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)
            
            self.ways.append(way_data)

class RealTileGenerator:
    def __init__(self, province=None, min_zoom=12, max_zoom=16, center_lat=None, center_lon=None, radius_km=30):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        # 添加缓存以提高性能
        self.processor_cache = {}
        self.cache_lock = threading.Lock()
        
        # 设置区域参数
        self.min_zoom = min_zoom
        self.max_zoom = max_zoom
        
        if province and province in PROVINCE_BBOXES:
            self.province = province
            self.province_bbox = PROVINCE_BBOXES[province]
            # 使用省份中心点
            self.center_lon = (self.province_bbox[0] + self.province_bbox[2]) / 2
            self.center_lat = (self.province_bbox[1] + self.province_bbox[3]) / 2
            self.area_bbox = self.province_bbox
        elif center_lat is not None and center_lon is not None:
            self.province = None
            self.center_lat = center_lat
            self.center_lon = center_lon
            self.radius_km = radius_km
            # 计算指定区域的边界框
            self.area_bbox = self._calculate_area_bbox(center_lat, center_lon, radius_km)
        else:
            # 默认使用北京
            self.province = "北京"
            self.province_bbox = PROVINCE_BBOXES["北京"]
            self.center_lon = (self.province_bbox[0] + self.province_bbox[2]) / 2
            self.center_lat = (self.province_bbox[1] + self.province_bbox[3]) / 2
            self.area_bbox = self.province_bbox
    
    def _calculate_area_bbox(self, center_lat, center_lon, radius_km):
        """计算指定中心点和半径的边界框"""
        # 近似计算
        lat_delta = radius_km / 111.0
        lon_delta = radius_km / (111.0 * math.cos(math.radians(center_lat)))
        
        south = center_lat - lat_delta
        north = center_lat + lat_delta
        west = center_lon - lon_delta
        east = center_lon + lon_delta
        
        return [west, south, east, north]
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def should_process_tile(self, tile_bbox):
        """检查是否应该处理这个瓦片"""
        # 检查瓦片边界框是否与区域边界框相交
        tile_min_lon, tile_min_lat, tile_max_lon, tile_max_lat = tile_bbox
        area_min_lon, area_min_lat, area_max_lon, area_max_lat = self.area_bbox
        
        # 检查两个矩形是否相交
        return not (tile_max_lon < area_min_lon or 
                   tile_min_lon > area_max_lon or 
                   tile_max_lat < area_min_lat or 
                   tile_min_lat > area_max_lat)
    
    def generate_test_tile(self, x, y, zoom):
        """生成测试瓦片 - 微型测试区域方案"""
        bbox = self.get_tile_bbox(x, y, zoom)
        
        print(f"瓦片 {zoom}/{x}/{y} bbox: {bbox}")
        
        # 检查瓦片是否在目标区域内
        if not self.should_process_tile(bbox):
            print(f"瓦片 {zoom}/{x}/{y} 不在目标区域内，跳过处理")
            # 返回空白瓦片
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), f"区域外: Z{zoom}/X{x}/Y{y}", fill='#999999')
            return img, True  # 返回True以增加成功计数
        
        # 从OSM文件中提取数据
        try:
            processor = EnhancedOSMProcessor(bbox, zoom)
            processor.apply_file(str(self.osm_file))
            
            # 检查是否有足够的数据
            if len(processor.nodes) == 0:
                print(f"processor 未获取到，当前瓦片无有效 OSM 数据或处理异常")
                img = Image.new('RGB', (256, 256), color='#f0f0f0')
                draw = ImageDraw.Draw(img)
                draw.text((10, 10), f"无数据: Z{zoom}/X{x}/Y{y}", fill='#999999')
                return img, True  # 返回True以增加成功计数
            
            # 添加调试信息 - 移到这里，确保 processor 已初始化
            print(f"节点数: {len(processor.nodes)}, 道路数: {len(processor.roads)}, 建筑数: {len(processor.buildings)}")
            
            # 创建高质量瓦片图像
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换函数
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
                return (max(0, min(255, px)), max(0, min(255, py)))
            
            # 绘制水体（底层）
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4')
            
            # 绘制建筑物（中层）
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    # 根据缩放级别调整建筑物显示
                    if zoom >= 14:
                        draw.polygon(points, fill='#dddddd', outline='#999999')
                    elif zoom >= 12:
                        # 简化显示
                        center_x = sum(p[0] for p in points) // len(points)
                        center_y = sum(p[1] for p in points) // len(points)
                        draw.rectangle([center_x-2, center_y-2, center_x+2, center_y+2], 
                                     fill='#cccccc')
            
            # 绘制道路（顶层）
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 根据道路类型和缩放级别设置样式
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = max(1, 6 - (15 - zoom))
                elif highway_type in ['primary', 'secondary']:
                    color = '#ffcc00'
                    width = max(1, 4 - (15 - zoom))
                elif highway_type in ['tertiary', 'residential'] and zoom >= 12:
                    color = '#ffffff'
                    width = max(1, 3 - (15 - zoom))
                elif highway_type in ['service', 'track'] and zoom >= 14:
                    color = '#cccccc'
                    width = 1
                else:
                    continue  # 跳过不适合当前缩放级别的道路
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路线段
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 添加调试信息
            debug_text = f"Z{zoom}/X{x}/Y{y}\n{len(processor.roads)}R {len(processor.buildings)}B"
            draw.text((5, 5), debug_text, fill='#333333')
            
            return img, True  # 即使没有道路和建筑，也返回成功
            
        except Exception as e:
            print(f"处理OSM数据失败: {e}")
            # 返回错误指示瓦片
            img = Image.new('RGB', (256, 256), color='#ffcccc')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), f"ERROR: {str(e)[:30]}", fill='red')
            print(f"processor 未获取到，当前瓦片无有效 OSM 数据或处理异常")
            return img, False
    
    def generate_and_save_tile(self, x, y, zoom):
        """生成并保存瓦片"""
        tile_dir = self.tiles_dir / str(zoom) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        try:
            img, has_data = self.generate_test_tile(x, y, zoom)
            
            # 保存瓦片
            img.save(tile_file, 'PNG', optimize=True)
            
            return has_data
        except Exception:
            # 创建错误瓦片
            img = Image.new('RGB', (256, 256), color='#ffcccc')
            img.save(tile_file, 'PNG')
            return False
    
    def generate_province_tiles(self, workers=4):
        """生成指定区域的瓦片 - 优化版本"""
        print(f"开始生成地图瓦片...")
        if self.province:
            print(f"区域: {self.province}")
        else:
            print(f"区域中心: {self.center_lat:.4f}, {self.center_lon:.4f}")
            print(f"覆盖半径: {self.radius_km} 公里")
        print(f"缩放级别: {self.min_zoom}-{self.max_zoom}")
        print(f"区域边界: {self.area_bbox}")
        
        total_tiles = 0
        success_count = 0
        
        start_time = time.time()
        
        # 按缩放级别生成瓦片
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            print(f"\n生成缩放级别 {zoom} 的瓦片...")
            
            # 计算该缩放级别下区域的瓦片范围
            min_x, max_y = self.deg2num(self.area_bbox[1], self.area_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.area_bbox[3], self.area_bbox[2], zoom)
            
            # 扩展范围以确保覆盖完整
            padding = 1 if zoom < 15 else 2
            min_x -= padding
            max_x += padding
            min_y -= padding
            max_y += padding
            
            tasks = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    tasks.append({'x': x, 'y': y, 'zoom': zoom})
            
            level_total = len(tasks)
            total_tiles += level_total
            
            print(f"  需要生成 {level_total} 个瓦片 (X: {min_x}-{max_x}, Y: {min_y}-{max_y})")
            
            # 对于低缩放级别，使用更多线程；对于高缩放级别，使用较少线程以避免内存问题
            level_workers = min(workers, max(2, workers - (zoom - 12)))
            
            # 使用线程池并行处理瓦片生成以提高速度
            with ThreadPoolExecutor(max_workers=level_workers) as executor:
                # 提交所有任务
                future_to_task = {
                    executor.submit(self.generate_and_save_tile, task['x'], task['y'], task['zoom']): task 
                    for task in tasks
                }
                
                # 收集结果
                with tqdm(total=level_total, desc=f"  缩放级别 {zoom}", unit="tile") as pbar:
                    for future in concurrent.futures.as_completed(future_to_task):
                        task = future_to_task[future]
                        x, y, zoom = task['x'], task['y'], task['zoom']
                        try:
                            if future.result():
                                success_count += 1
                        except Exception:
                            pass  # 忽略单个瓦片的错误
                        pbar.update(1)
            
            print(f"  缩放级别 {zoom} 完成")
        
        elapsed_time = time.time() - start_time
        print(f"\n=== 生成完成 ===")
        print(f"总计需要生成: {total_tiles} 个瓦片")
        print(f"成功生成: {success_count} 个瓦片")
        if total_tiles > 0:
            success_rate = success_count / total_tiles * 100
            print(f"成功率: {success_rate:.1f}%")
        print(f"总用时: {elapsed_time:.1f} 秒")
        print(f"平均速度: {total_tiles/elapsed_time:.1f} 瓦片/秒")
        
        return success_count >= total_tiles * 0.8  # 只要成功率超过80%就算成功

def list_provinces():
    """列出所有支持的省份"""
    print("支持的省份和直辖市:")
    provinces = list(PROVINCE_BBOXES.keys())
    for i, province in enumerate(provinces, 1):
        print(f"  {i:2d}. {province}")

def main():
    parser = argparse.ArgumentParser(description="增强的OSM数据处理器 - 优化版")
    parser.add_argument("province", nargs="?", help="省份名称 (例如: 北京)")
    parser.add_argument("--min-zoom", type=int, default=12, help="最小缩放级别 (默认: 12)")
    parser.add_argument("--max-zoom", type=int, default=16, help="最大缩放级别 (默认: 16)")
    parser.add_argument("--center-lat", type=float, help="中心纬度")
    parser.add_argument("--center-lon", type=float, help="中心经度")
    parser.add_argument("--radius", type=float, default=30, help="覆盖半径(公里) (默认: 30)")
    parser.add_argument("--workers", type=int, default=4, help="并行工作线程数 (默认: 4)")
    parser.add_argument("--list", action="store_true", help="列出所有支持的省份")
    
    args = parser.parse_args()
    
    if args.list:
        list_provinces()
        return
    
    try:
        generator = RealTileGenerator(
            province=args.province,
            min_zoom=args.min_zoom,
            max_zoom=args.max_zoom,
            center_lat=args.center_lat,
            center_lon=args.center_lon,
            radius_km=args.radius
        )
        success = generator.generate_province_tiles(args.workers)
        
        if success:
            print(f"\n🎉 地图瓦片生成成功！")
            print(f"瓦片位置: {generator.tiles_dir}")
            print(f"建议运行: python start_fixed_map.py")
        else:
            print(f"\n❌ 地图瓦片生成失败，请检查配置")
            
    except Exception as e:
        print(f"生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()