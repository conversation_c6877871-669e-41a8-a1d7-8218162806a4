# 省市级瓦片下载工具使用指南

## 概述
本工具专门用于下载中国各省市18级瓦片，支持百度地图和高德地图两种地图源，适合创建省市级离线地图。

## 功能特点

### ✅ 支持的地图源
- **百度地图**: 中国本土化，中文标注详细
- **高德地图**: 道路信息详细，适合导航

### ✅ 覆盖范围
- **34个省份/直辖市**: 包括所有省、自治区、直辖市、特别行政区
- **20个主要城市**: 包括北京、上海、广州、深圳等一线城市

### ✅ 智能下载
- 自动跳过已存在的瓦片
- 支持断点续传
- 实时显示下载进度
- 质量检查确保瓦片完整性

## 使用方法

### 方法1: 交互式下载 (推荐)
```bash
python interactive_download.py
```

**菜单选项:**
1. 下载省级瓦片 (百度地图)
2. 下载省级瓦片 (高德地图)  
3. 下载市级瓦片 (百度地图)
4. 下载市级瓦片 (高德地图)
5. 测试瓦片质量
6. 查看瓦片统计

### 方法2: 直接测试
```bash
python test_province_download.py
```

## 省份列表

### 直辖市
- 北京市 (半径: 50km)
- 上海市 (半径: 40km)
- 天津市 (半径: 30km)
- 重庆市 (半径: 60km)

### 省份
- 广东省 (半径: 200km)
- 江苏省 (半径: 150km)
- 浙江省 (半径: 120km)
- 山东省 (半径: 180km)
- 河南省 (半径: 160km)
- 四川省 (半径: 250km)
- 湖北省 (半径: 140km)
- 湖南省 (半径: 150km)
- 河北省 (半径: 180km)
- 福建省 (半径: 120km)
- 安徽省 (半径: 130km)
- 辽宁省 (半径: 150km)
- 江西省 (半径: 120km)
- 黑龙江省 (半径: 200km)
- 吉林省 (半径: 140km)
- 山西省 (半径: 130km)
- 陕西省 (半径: 150km)
- 甘肃省 (半径: 200km)
- 青海省 (半径: 250km)
- 云南省 (半径: 200km)
- 贵州省 (半径: 150km)
- 海南省 (半径: 100km)
- 台湾省 (半径: 80km)

### 自治区
- 内蒙古自治区 (半径: 300km)
- 新疆维吾尔自治区 (半径: 400km)
- 西藏自治区 (半径: 350km)
- 宁夏回族自治区 (半径: 120km)
- 广西壮族自治区 (半径: 180km)

### 特别行政区
- 香港特别行政区 (半径: 20km)
- 澳门特别行政区 (半径: 10km)

## 城市列表

### 一线城市
- 北京市 (半径: 30km)
- 上海市 (半径: 25km)
- 广州市 (半径: 20km)
- 深圳市 (半径: 15km)

### 省会城市
- 杭州市 (半径: 20km)
- 南京市 (半径: 20km)
- 成都市 (半径: 25km)
- 武汉市 (半径: 20km)
- 西安市 (半径: 20km)
- 重庆市 (半径: 30km)
- 天津市 (半径: 20km)

### 重要城市
- 青岛市 (半径: 15km)
- 大连市 (半径: 15km)
- 厦门市 (半径: 15km)
- 苏州市 (半径: 15km)
- 无锡市 (半径: 15km)
- 宁波市 (半径: 15km)
- 长沙市 (半径: 15km)
- 郑州市 (半径: 15km)
- 沈阳市 (半径: 15km)

## 下载参数

### 缩放级别
- **推荐**: 18级 (详细程度高)
- **范围**: 10-19级
- **说明**: 18级适合城市导航和详细查看

### 并发数
- **推荐**: 8个并发
- **范围**: 1-20个
- **说明**: 过高可能导致被限制

### 瓦片数量估算
- **市级**: 约1,000-10,000个瓦片
- **省级**: 约10,000-100,000个瓦片
- **大型省份**: 可达200,000+个瓦片

## 存储空间

### 文件大小
- **单个瓦片**: 2-10KB
- **市级瓦片**: 约10-100MB
- **省级瓦片**: 约100MB-1GB
- **大型省份**: 可达2GB+

### 目录结构
```
static/tiles/
├── 18/
│   ├── 215837/
│   │   ├── 99333.png
│   │   └── 99334.png
│   └── 215838/
│       └── 99333.png
└── ...
```

## 质量检查

### 自动检查
- 文件大小检查 (>2000 bytes)
- 内容完整性验证
- 跳过已存在文件

### 手动验证
```bash
python test_province_download.py
```

## 服务器配置

### 启动瓦片服务器
```bash
python tileserver.py
```

### 访问地址
- 服务器: http://localhost:8080
- 瓦片API: http://localhost:8080/tiles/{z}/{x}/{y}.png
- 统计信息: http://localhost:8080/stats

## 使用建议

### 1. 首次使用
- 先运行测试脚本验证功能
- 选择小范围城市进行测试
- 确认网络连接稳定

### 2. 批量下载
- 建议分批次下载不同省份
- 避免同时下载多个大型省份
- 定期检查下载进度

### 3. 存储管理
- 预留足够的磁盘空间
- 定期清理无效瓦片
- 备份重要的瓦片数据

## 故障排除

### 常见问题

1. **下载失败**
   - 检查网络连接
   - 尝试更换地图源
   - 降低并发数

2. **瓦片空白**
   - 检查文件大小
   - 重新下载问题瓦片
   - 验证地图源可用性

3. **速度慢**
   - 增加并发数
   - 检查网络带宽
   - 选择合适的时间段

### 技术支持
- 查看下载日志
- 检查瓦片统计信息
- 验证服务器状态

## 法律声明

⚠️ **重要提醒**:
- 本工具仅供学习研究使用
- 请遵守地图服务商的使用条款
- 不得用于商业用途
- 尊重知识产权

---

**版本**: 1.0  
**更新日期**: 2025年9月  
**支持**: 百度地图、高德地图
