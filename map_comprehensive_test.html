<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线地图综合测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        #map { 
            flex: 3;
            height: 100%;
        }
        .control-panel {
            flex: 1;
            padding: 20px;
            background: white;
            overflow-y: auto;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }
        .control-panel h2 {
            margin-top: 0;
            color: #333;
        }
        .status-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .status-section h3 {
            margin-top: 0;
            color: #555;
        }
        .status-item {
            margin: 8px 0;
            padding: 8px;
            border-radius: 3px;
            background: white;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
        }
        .status-warning {
            border-left-color: #ffc107;
        }
        .status-error {
            border-left-color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .zoom-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .zoom-btn {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="map"></div>
        <div class="control-panel">
            <h2>离线地图综合测试</h2>
            
            <div class="status-section">
                <h3>地图状态</h3>
                <div class="status-item" id="map-center">中心点: --</div>
                <div class="status-item" id="map-zoom">缩放级别: --</div>
                <div class="status-item" id="map-bounds">边界: --</div>
            </div>
            
            <div class="status-section">
                <h3>瓦片加载状态</h3>
                <div class="status-item" id="tile-stats">瓦片统计: --</div>
                <div class="status-item" id="layer-info">当前图层: --</div>
                <div class="status-item" id="load-status">加载状态: --</div>
            </div>
            
            <div class="status-section">
                <h3>图层控制</h3>
                <button id="offline-btn">离线地图</button>
                <button id="server-btn">瓦片服务器</button>
                <button id="online-btn">在线地图</button>
            </div>
            
            <div class="status-section">
                <h3>缩放控制</h3>
                <div class="zoom-controls">
                    <button class="zoom-btn" data-zoom="8">8级</button>
                    <button class="zoom-btn" data-zoom="10">10级</button>
                    <button class="zoom-btn" data-zoom="11">11级</button>
                    <button class="zoom-btn" data-zoom="12">12级</button>
                    <button class="zoom-btn" data-zoom="13">13级</button>
                    <button class="zoom-btn" data-zoom="14">14级</button>
                    <button class="zoom-btn" data-zoom="15">15级</button>
                    <button class="zoom-btn" data-zoom="16">16级</button>
                    <button class="zoom-btn" data-zoom="17">17级</button>
                    <button class="zoom-btn" data-zoom="18">18级</button>
                </div>
            </div>
            
            <div class="status-section">
                <h3>测试操作</h3>
                <button id="test-tiles">测试瓦片加载</button>
                <button id="reset-view">重置视图</button>
                <button id="clear-stats">清除统计</button>
            </div>
            
            <div class="status-section">
                <h3>日志信息</h3>
                <div id="log-messages" style="max-height: 200px; overflow-y: auto; font-size: 12px;"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let currentLayer = 'offline';
        let tileStats = {
            requested: 0,
            loaded: 0,
            errors: 0
        };
        
        // 图层定义
        const layers = {
            offline: L.tileLayer('./static/tiles/{z}/{x}/{y}.png', {
                attribution: '© OpenMapTiles © OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 8,
                errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            }),
            server: L.tileLayer('http://localhost:8080/tiles/{z}/{x}/{y}.png', {
                attribution: '© OpenMapTiles © OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 8
            }),
            online: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 8
            })
        };
        
        // 初始化地图
        function initMap() {
            map = L.map('map').setView([39.9042, 116.4074], 15);
            
            // 添加默认图层
            layers.offline.addTo(map);
            
            // 添加标记
            const marker = L.marker([39.9042, 116.4074])
                .addTo(map)
                .bindPopup('深机井监控点<br>北京市中心')
                .openPopup();
            
            // 地图事件监听
            map.on('moveend', updateMapStatus);
            map.on('zoomend', updateMapStatus);
            
            // 瓦片事件监听
            layers.offline.on('tileloadstart', function() {
                tileStats.requested++;
                updateTileStats();
            });
            
            layers.offline.on('tileload', function() {
                tileStats.loaded++;
                updateTileStats();
                addLog('离线瓦片加载成功');
            });
            
            layers.offline.on('tileerror', function(e) {
                tileStats.errors++;
                updateTileStats();
                addLog(`离线瓦片加载失败: ${e.url}`, 'error');
            });
            
            // 更新初始状态
            updateMapStatus();
            updateTileStats();
            addLog('地图初始化完成');
        }
        
        // 更新地图状态显示
        function updateMapStatus() {
            if (!map) return;
            
            const center = map.getCenter();
            const zoom = map.getZoom();
            const bounds = map.getBounds();
            
            document.getElementById('map-center').textContent = 
                `中心点: ${center.lat.toFixed(6)}, ${center.lng.toFixed(6)}`;
            document.getElementById('map-zoom').textContent = 
                `缩放级别: ${zoom}`;
            document.getElementById('map-bounds').textContent = 
                `边界: ${bounds.getSouthWest().lat.toFixed(4)}, ${bounds.getSouthWest().lng.toFixed(4)} - ${bounds.getNorthEast().lat.toFixed(4)}, ${bounds.getNorthEast().lng.toFixed(4)}`;
        }
        
        // 更新瓦片统计显示
        function updateTileStats() {
            document.getElementById('tile-stats').textContent = 
                `瓦片统计: 请求${tileStats.requested}, 成功${tileStats.loaded}, 失败${tileStats.errors}`;
            
            document.getElementById('layer-info').textContent = 
                `当前图层: ${currentLayer}`;
            
            const successRate = tileStats.requested > 0 ? 
                (tileStats.loaded / tileStats.requested * 100).toFixed(1) : 0;
            
            const statusEl = document.getElementById('load-status');
            statusEl.textContent = `加载状态: 成功率 ${successRate}%`;
            
            // 根据成功率设置样式
            statusEl.className = 'status-item';
            if (successRate >= 90) {
                statusEl.classList.add('status-success');
            } else if (successRate >= 50) {
                statusEl.classList.add('status-warning');
            } else {
                statusEl.classList.add('status-error');
            }
        }
        
        // 添加日志信息
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-messages');
            const logEntry = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            logEntry.textContent = `[${timestamp}] ${message}`;
            logEntry.style.padding = '2px 0';
            
            if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'warning') {
                logEntry.style.color = '#ffc107';
            }
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 切换图层
        function switchLayer(layerName) {
            if (currentLayer === layerName) return;
            
            // 移除当前图层
            map.removeLayer(layers[currentLayer]);
            
            // 添加新图层
            layers[layerName].addTo(map);
            
            currentLayer = layerName;
            updateTileStats();
            addLog(`切换到${layerName}图层`);
        }
        
        // 测试瓦片加载
        function testTileLoad() {
            addLog('开始测试瓦片加载...');
            
            // 重置统计
            tileStats = { requested: 0, loaded: 0, errors: 0 };
            
            // 强制重新加载瓦片
            layers[currentLayer].redraw();
            
            setTimeout(() => {
                addLog(`测试完成: 成功${tileStats.loaded}, 失败${tileStats.errors}`);
            }, 3000);
        }
        
        // 重置视图
        function resetView() {
            map.setView([39.9042, 116.4074], 15);
            addLog('视图已重置');
        }
        
        // 清除统计
        function clearStats() {
            tileStats = { requested: 0, loaded: 0, errors: 0 };
            updateTileStats();
            document.getElementById('log-messages').innerHTML = '';
            addLog('统计信息已清除');
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 图层切换按钮
            document.getElementById('offline-btn').addEventListener('click', () => switchLayer('offline'));
            document.getElementById('server-btn').addEventListener('click', () => switchLayer('server'));
            document.getElementById('online-btn').addEventListener('click', () => switchLayer('online'));
            
            // 缩放按钮
            document.querySelectorAll('.zoom-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const zoom = parseInt(btn.dataset.zoom);
                    map.setZoom(zoom);
                });
            });
            
            // 测试按钮
            document.getElementById('test-tiles').addEventListener('click', testTileLoad);
            document.getElementById('reset-view').addEventListener('click', resetView);
            document.getElementById('clear-stats').addEventListener('click', clearStats);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initMap();
            setupEventListeners();
        });
    </script>
</body>
</html>