<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        #map {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🗺️ 离线地图测试</h1>
    
    <div class="info">
        <h3>测试信息</h3>
        <p><strong>瓦片服务器:</strong> http://127.0.0.1:8080</p>
        <p><strong>缩放级别:</strong> 18级</p>
        <p><strong>中心坐标:</strong> 北京 (116.4074, 39.9042)</p>
        <p><strong>状态:</strong> <span id="status">加载中...</span></p>
    </div>
    
    <div id="map"></div>
    
    <script>
        let map;
        let statusElement = document.getElementById('status');
        
        function updateStatus(message) {
            statusElement.textContent = message;
            console.log(message);
        }
        
        async function initMap() {
            try {
                updateStatus('正在初始化地图...');
                
                // 初始化地图
                map = L.map('map', {
                    center: [39.9042, 116.4074], // 北京
                    zoom: 18,
                    minZoom: 18,
                    maxZoom: 18
                });
                
                updateStatus('正在添加离线瓦片图层...');
                
                // 添加离线瓦片图层
                const offlineLayer = L.tileLayer('http://127.0.0.1:8080/tiles/{z}/{x}/{y}.png', {
                    attribution: '© 本地离线地图',
                    minZoom: 18,
                    maxZoom: 18
                });
                
                offlineLayer.addTo(map);
                
                // 监听瓦片加载事件
                offlineLayer.on('tileload', function(e) {
                    updateStatus('瓦片加载成功: ' + e.tile.src);
                });
                
                offlineLayer.on('tileerror', function(e) {
                    updateStatus('瓦片加载失败: ' + e.tile.src);
                });
                
                // 添加标记
                L.marker([39.9042, 116.4074])
                    .addTo(map)
                    .bindPopup('北京天安门<br>GPS坐标: 39.9042, 116.4074')
                    .openPopup();
                
                updateStatus('地图初始化完成！');
                
            } catch (error) {
                updateStatus('地图初始化失败: ' + error.message);
                console.error('地图初始化失败:', error);
            }
        }
        
        // 页面加载完成后初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
    </script>
</body>
</html>
