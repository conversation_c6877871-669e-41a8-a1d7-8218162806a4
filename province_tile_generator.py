#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按省份生成离线地图瓦片
支持用户选择特定省份或直辖市以提高生成效率
"""

import osmium
import json
from pathlib import Path
from PIL import Image, ImageDraw
import math
from tqdm import tqdm
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import threading
import argparse

# 各省份/直辖市的边界框 [min_lon, min_lat, max_lon, max_lat]
PROVINCE_BBOXES = {
    "北京": [115.7, 39.4, 117.4, 41.6],
    "上海": [120.9, 30.7, 122.1, 31.9],
    "天津": [116.7, 38.5, 118.1, 40.2],
    "重庆": [105.3, 28.2, 110.2, 32.2],
    "河北": [113.3, 36.0, 119.9, 42.6],
    "山西": [110.2, 34.6, 114.6, 40.7],
    "辽宁": [118.8, 38.7, 125.8, 43.5],
    "吉林": [121.3, 40.9, 131.3, 46.2],
    "黑龙江": [121.1, 43.4, 135.1, 53.6],
    "江苏": [116.2, 30.7, 121.9, 35.1],
    "浙江": [118.0, 27.1, 123.0, 31.2],
    "安徽": [114.9, 29.4, 119.6, 34.6],
    "福建": [115.8, 23.5, 120.7, 28.3],
    "江西": [113.6, 24.5, 118.5, 30.0],
    "山东": [114.8, 34.4, 122.7, 38.3],
    "河南": [110.3, 31.4, 116.6, 36.4],
    "湖北": [108.4, 29.0, 116.1, 33.2],
    "湖南": [108.8, 24.6, 114.2, 30.1],
    "广东": [109.7, 20.1, 117.3, 25.5],
    "海南": [108.6, 18.2, 111.1, 20.2],
    "四川": [102.1, 26.0, 108.6, 34.2],
    "贵州": [103.6, 24.6, 109.4, 29.2],
    "云南": [97.5, 21.5, 106.0, 29.2],
    "陕西": [105.5, 31.7, 111.2, 39.6],
    "甘肃": [92.1, 32.7, 103.4, 42.8],
    "青海": [89.4, 31.6, 102.4, 39.8],
    "台湾": [120.0, 21.9, 122.0, 25.3],
    "内蒙古": [97.2, 37.4, 126.0, 53.3],
    "广西": [104.5, 20.9, 112.0, 26.4],
    "西藏": [78.4, 26.9, 99.1, 36.1],
    "宁夏": [104.2, 35.2, 107.4, 39.3],
    "新疆": [73.5, 34.3, 96.3, 49.2],
    "香港": [113.8, 22.1, 114.5, 22.6],
    "澳门": [113.5, 22.1, 113.6, 22.2]
}

class ProvinceTileGenerator:
    def __init__(self, province, min_zoom=8, max_zoom=18):
        self.province = province
        self.province_bbox = PROVINCE_BBOXES.get(province)
        if not self.province_bbox:
            raise ValueError(f"不支持的省份: {province}")
            
        self.min_zoom = min_zoom
        self.max_zoom = max_zoom
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取省份中心点
        self.center_lon = (self.province_bbox[0] + self.province_bbox[2]) / 2
        self.center_lat = (self.province_bbox[1] + self.province_bbox[3]) / 2
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def should_process_tile(self, tile_bbox):
        """检查是否应该处理这个瓦片"""
        # 检查瓦片边界框是否与省份边界框相交
        tile_min_lon, tile_min_lat, tile_max_lon, tile_max_lat = tile_bbox
        prov_min_lon, prov_min_lat, prov_max_lon, prov_max_lat = self.province_bbox
        
        # 检查两个矩形是否相交
        return not (tile_max_lon < prov_min_lon or 
                   tile_min_lon > prov_max_lon or 
                   tile_max_lat < prov_min_lat or 
                   tile_min_lat > prov_max_lat)
    
    def create_placeholder_tile(self, z, x, y):
        """创建占位瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 创建256x256的空白瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')
        img.save(tile_file, 'PNG')
        return True
    
    def generate_province_tiles(self, workers=4):
        """生成指定省份的瓦片"""
        print(f"开始生成 {self.province} 省的离线地图瓦片...")
        print(f"经纬度范围: {self.province_bbox}")
        print(f"缩放级别: {self.min_zoom}-{self.max_zoom}")
        
        total_tiles = 0
        generated_tiles = 0
        
        # 按缩放级别生成瓦片
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            print(f"\n生成缩放级别 {zoom} 的瓦片...")
            
            # 计算该缩放级别下省份的瓦片范围
            min_x, max_y = self.deg2num(self.province_bbox[1], self.province_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.province_bbox[3], self.province_bbox[2], zoom)
            
            # 扩展范围以确保覆盖完整
            min_x -= 1
            max_x += 1
            min_y -= 1
            max_y += 1
            
            level_tiles = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    level_tiles.append((x, y, zoom))
            
            level_total = len(level_tiles)
            total_tiles += level_total
            
            print(f"  需要生成 {level_total} 个瓦片 (X: {min_x}-{max_x}, Y: {min_y}-{max_y})")
            
            # 使用线程池并行生成瓦片
            success_count = 0
            with ThreadPoolExecutor(max_workers=workers) as executor:
                # 提交任务
                future_to_tile = {
                    executor.submit(self.create_placeholder_tile, z, x, y): (x, y, z)
                    for x, y, z in level_tiles
                }
                
                # 收集结果
                with tqdm(total=level_total, desc=f"  缩放级别 {zoom}", unit="tile") as pbar:
                    for future in concurrent.futures.as_completed(future_to_tile):
                        x, y, z = future_to_tile[future]
                        try:
                            if future.result():
                                success_count += 1
                        except Exception as e:
                            print(f"    生成瓦片 {z}/{x}/{y} 失败: {e}")
                        pbar.update(1)
            
            generated_tiles += success_count
            print(f"  缩放级别 {zoom} 完成: {success_count}/{level_total} 个瓦片")
        
        print(f"\n=== 生成完成 ===")
        print(f"总计需要生成: {total_tiles} 个瓦片")
        print(f"成功生成: {generated_tiles} 个瓦片")
        if total_tiles > 0:
            success_rate = generated_tiles / total_tiles * 100
            print(f"成功率: {success_rate:.1f}%")
        
        return generated_tiles == total_tiles

def list_provinces():
    """列出所有支持的省份"""
    print("支持的省份和直辖市:")
    provinces = list(PROVINCE_BBOXES.keys())
    for i, province in enumerate(provinces, 1):
        print(f"  {i:2d}. {province}")

def main():
    parser = argparse.ArgumentParser(description="按省份生成离线地图瓦片")
    parser.add_argument("province", nargs="?", help="省份名称 (例如: 北京)")
    parser.add_argument("--min-zoom", type=int, default=8, help="最小缩放级别 (默认: 8)")
    parser.add_argument("--max-zoom", type=int, default=15, help="最大缩放级别 (默认: 15)")
    parser.add_argument("--list", action="store_true", help="列出所有支持的省份")
    parser.add_argument("--workers", type=int, default=4, help="并行工作线程数 (默认: 4)")
    
    args = parser.parse_args()
    
    if args.list:
        list_provinces()
        return
    
    if not args.province:
        print("请指定省份名称，或使用 --list 查看支持的省份")
        list_provinces()
        return
    
    if args.province not in PROVINCE_BBOXES:
        print(f"不支持的省份: {args.province}")
        list_provinces()
        return
    
    try:
        generator = ProvinceTileGenerator(args.province, args.min_zoom, args.max_zoom)
        success = generator.generate_province_tiles(args.workers)
        
        if success:
            print(f"\n🎉 {args.province} 省地图瓦片生成成功!")
            print(f"瓦片保存在: {generator.tiles_dir}")
        else:
            print(f"\n❌ {args.province} 省地图瓦片生成失败!")
            
    except Exception as e:
        print(f"生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()