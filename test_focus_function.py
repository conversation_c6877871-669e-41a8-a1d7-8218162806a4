#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试地图聚焦功能
"""

import requests
import webbrowser
import time

def test_focus_function():
    """测试地图聚焦功能"""
    print("🎯 测试地图聚焦功能")
    print("=" * 50)
    
    # 检查系统状态
    print("1. 检查系统状态...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/statistics", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 系统运行正常")
            print(f"   📊 区域数: {stats['total_regions']}, 著名地标: {stats['famous_places_count']}")
        else:
            print(f"   ❌ 系统响应异常: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 系统连接失败: {e}")
        return
    
    # 测试搜索功能
    print("\n2. 测试搜索功能...")
    test_queries = ["天安门", "故宫", "北京", "上海"]
    for query in test_queries:
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ '{query}': 找到 {len(results)} 个结果")
            else:
                print(f"   ❌ '{query}': 搜索失败")
        except Exception as e:
            print(f"   ❌ '{query}': 异常 - {e}")
    
    # 测试著名地标
    print("\n3. 测试著名地标...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/famous-places")
        if response.status_code == 200:
            places = response.json()
            print(f"   ✅ 著名地标: {len(places)} 个")
            for name in list(places.keys())[:3]:  # 显示前3个
                print(f"      - {name}")
        else:
            print(f"   ❌ 著名地标获取失败")
    except Exception as e:
        print(f"   ❌ 著名地标异常: {e}")
    
    # 提供使用说明
    print(f"\n🎯 聚焦功能改进:")
    print(f"   ✅ 点击搜索结果项 → 平滑动画聚焦到位置")
    print(f"   ✅ 点击区域按钮 → 快速聚焦到区域中心")
    print(f"   ✅ 点击著名地标 → 高亮显示并聚焦")
    print(f"   ✅ 添加脉冲动画标记")
    print(f"   ✅ 显示圆形高亮区域")
    print(f"   ✅ 弹出详细信息窗口")
    
    print(f"\n📋 使用方法:")
    print(f"   1. 在搜索框输入地名 (如: 天安门、故宫、北京)")
    print(f"   2. 点击搜索结果中的任意项目")
    print(f"   3. 地图会平滑动画聚焦到该位置")
    print(f"   4. 显示脉冲动画标记和圆形高亮区域")
    print(f"   5. 弹出详细信息窗口")
    
    print(f"\n🔧 技术特性:")
    print(f"   - 使用 map.flyTo() 实现平滑动画")
    print(f"   - 1.5秒聚焦动画，2秒区域聚焦动画")
    print(f"   - 16级缩放精度 (街道级别)")
    print(f"   - 脉冲动画标记效果")
    print(f"   - 圆形高亮区域 (半径200米)")
    print(f"   - 自动清除之前的标记")
    
    # 自动打开系统
    print(f"\n🌐 自动打开系统...")
    try:
        webbrowser.open('http://127.0.0.1:5000')
        print(f"   ✅ 已自动打开系统")
    except Exception as e:
        print(f"   ❌ 无法自动打开浏览器: {e}")
        print(f"   💡 请手动访问: http://127.0.0.1:5000")

if __name__ == "__main__":
    test_focus_function()
