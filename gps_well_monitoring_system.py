#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS机井监控地图系统
用于野外机井作业监控，通过GPS定位显示机井位置
"""

import json
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from pathlib import Path
import math

class GPSWellMonitoringSystem:
    def __init__(self):
        self.app = Flask(__name__)
        self.wells = {}  # 存储机井信息
        self.gps_history = {}  # 存储GPS历史轨迹
        self.offline_mode = True  # 离线模式
        self.tile_server_port = 8080  # 本地瓦片服务器端口
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('gps_well_monitoring.html')
        
        @self.app.route('/test_ultra_precision_map.html')
        def test_ultra_precision_map():
            """提供超高精度地图测试页面"""
            import os
            test_file = os.path.join(os.path.dirname(__file__), 'test_ultra_precision_map.html')
            if os.path.exists(test_file):
                with open(test_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "测试页面不存在", 404
        
        @self.app.route('/debug_ultra_precision.html')
        def debug_ultra_precision():
            """提供超高精度数据调试页面"""
            import os
            debug_file = os.path.join(os.path.dirname(__file__), 'debug_ultra_precision.html')
            if os.path.exists(debug_file):
                with open(debug_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "调试页面不存在", 404
        
        @self.app.route('/simple_render_test.html')
        def simple_render_test():
            """提供简化渲染测试页面"""
            import os
            test_file = os.path.join(os.path.dirname(__file__), 'simple_render_test.html')
            if os.path.exists(test_file):
                with open(test_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "测试页面不存在", 404
        
        @self.app.route('/api/wells')
        def get_wells():
            """获取所有机井信息"""
            return jsonify(self.wells)
        
        @self.app.route('/api/wells', methods=['POST'])
        def add_well():
            """添加机井"""
            data = request.json
            well_id = data.get('id')
            if well_id:
                self.wells[well_id] = {
                    'id': well_id,
                    'name': data.get('name', f'机井{well_id}'),
                    'lat': float(data.get('lat', 0)),
                    'lon': float(data.get('lon', 0)),
                    'status': data.get('status', 'active'),
                    'gas_level': data.get('gas_level', 0),
                    'fan_status': data.get('fan_status', 'off'),
                    'last_update': datetime.now().isoformat(),
                    'description': data.get('description', '')
                }
                return jsonify({'success': True, 'well': self.wells[well_id]})
            return jsonify({'success': False, 'error': '缺少机井ID'})
        
        @self.app.route('/api/wells/<well_id>', methods=['PUT'])
        def update_well(well_id):
            """更新机井信息"""
            if well_id in self.wells:
                data = request.json
                self.wells[well_id].update({
                    'lat': float(data.get('lat', self.wells[well_id]['lat'])),
                    'lon': float(data.get('lon', self.wells[well_id]['lon'])),
                    'status': data.get('status', self.wells[well_id]['status']),
                    'gas_level': data.get('gas_level', self.wells[well_id]['gas_level']),
                    'fan_status': data.get('fan_status', self.wells[well_id]['fan_status']),
                    'last_update': datetime.now().isoformat()
                })
                
                # 记录GPS历史
                self.record_gps_history(well_id, self.wells[well_id]['lat'], self.wells[well_id]['lon'])
                
                return jsonify({'success': True, 'well': self.wells[well_id]})
            return jsonify({'success': False, 'error': '机井不存在'})
        
        @self.app.route('/api/wells/<well_id>/gps', methods=['POST'])
        def update_gps(well_id):
            """更新GPS坐标"""
            data = request.json
            lat = float(data.get('lat', 0))
            lon = float(data.get('lon', 0))
            
            if well_id in self.wells:
                self.wells[well_id]['lat'] = lat
                self.wells[well_id]['lon'] = lon
                self.wells[well_id]['last_update'] = datetime.now().isoformat()
                
                # 记录GPS历史
                self.record_gps_history(well_id, lat, lon)
                
                return jsonify({'success': True, 'well': self.wells[well_id]})
            return jsonify({'success': False, 'error': '机井不存在'})
        
        @self.app.route('/api/wells/<well_id>/gps-history')
        def get_gps_history(well_id):
            """获取GPS历史轨迹"""
            if well_id in self.gps_history:
                return jsonify(self.gps_history[well_id])
            return jsonify([])
        
        @self.app.route('/api/locate', methods=['POST'])
        def locate_by_gps():
            """根据GPS坐标定位"""
            data = request.json
            lat = float(data.get('lat', 0))
            lon = float(data.get('lon', 0))
            radius = float(data.get('radius', 1000))  # 搜索半径，默认1公里
            
            # 查找附近的机井
            nearby_wells = []
            for well_id, well in self.wells.items():
                distance = self.calculate_distance(lat, lon, well['lat'], well['lon'])
                if distance <= radius:
                    nearby_wells.append({
                        'well': well,
                        'distance': distance
                    })
            
            # 按距离排序
            nearby_wells.sort(key=lambda x: x['distance'])
            
            return jsonify({
                'success': True,
                'location': {'lat': lat, 'lon': lon},
                'nearby_wells': nearby_wells,
                'count': len(nearby_wells)
            })
        
        @self.app.route('/api/search-location', methods=['POST'])
        def search_location():
            """搜索位置"""
            data = request.json
            query = data.get('query', '').strip()
            
            if not query:
                return jsonify({'success': False, 'error': '搜索内容不能为空'})
            
            results = []
            
            # 搜索机井
            for well_id, well in self.wells.items():
                if query.lower() in well['name'].lower() or query in well_id:
                    results.append({
                        'type': 'well',
                        'id': well_id,
                        'name': well['name'],
                        'lat': well['lat'],
                        'lon': well['lon'],
                        'status': well['status']
                    })
            
            return jsonify({
                'success': True,
                'query': query,
                'results': results
            })
        
        @self.app.route('/api/statistics')
        def get_statistics():
            """获取统计信息"""
            total_wells = len(self.wells)
            active_wells = sum(1 for well in self.wells.values() if well['status'] == 'active')
            warning_wells = sum(1 for well in self.wells.values() if well['gas_level'] > 50)
            
            return jsonify({
                'total_wells': total_wells,
                'active_wells': active_wells,
                'warning_wells': warning_wells,
                'last_update': datetime.now().isoformat()
            })
        
        @self.app.route('/api/map-config')
        def get_map_config():
            """获取地图配置"""
            return jsonify({
                'offline_mode': False,  # 使用在线地图作为底图
                'vector_data_available': True,  # 矢量数据可用
                'vector_data_path': '/static/vector_data',
                'min_zoom': 3,  # 最小缩放级别
                'max_zoom': 18,  # 在线地图最大可用精度
                'vector_max_zoom': 20,  # 矢量数据支持的最高精度
                'initial_zoom': 10,  # 初始缩放级别 - 中国国家地图
                'gps_zoom': 18,  # GPS定位时的缩放级别
                'center': [35.0, 105.0],  # 中国中心坐标 [纬度, 经度]
                'bounds': {
                    'north': 54.0,   # 中国边界
                    'south': 18.0,
                    'east': 135.0,
                    'west': 73.0
                }
            })
        
        @self.app.route('/api/vector-data/<region>')
        def get_vector_data(region):
            """获取指定区域的矢量数据"""
            try:
                import os
                vector_dir = os.path.join('static', 'vector_data', region)
                
                if not os.path.exists(vector_dir):
                    return jsonify({'error': f'区域 {region} 的矢量数据不存在'}), 404
                
                data = {}
                for data_type in ['buildings', 'roads', 'pois']:
                    file_path = os.path.join(vector_dir, f'{data_type}.osm')
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data[data_type] = f.read()
                
                return jsonify(data)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/high-precision-data/<region>')
        def get_high_precision_data(region):
            """获取高精度矢量数据"""
            try:
                import os
                import json
                precision_file = os.path.join('static', 'processed_vector_data', f'{region}_high_precision.json')
                if os.path.exists(precision_file):
                    with open(precision_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    return jsonify(data)
                else:
                    return jsonify({'error': '高精度数据不存在'}), 404
            except Exception as e:
                return jsonify({'error': f'获取高精度数据失败: {str(e)}'}), 500
        
        @self.app.route('/api/ultra-precision-data/<region>')
        def get_ultra_precision_data(region):
            """获取超高精度矢量数据"""
            try:
                import os
                import json
                ultra_file = os.path.join('static', 'ultra_precision_data', f'{region}_ultra_precision.json')
                if os.path.exists(ultra_file):
                    with open(ultra_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    return jsonify(data)
                else:
                    return jsonify({'error': '超高精度数据不存在'}), 404
            except Exception as e:
                return jsonify({'error': f'获取超高精度数据失败: {str(e)}'}), 500
        
        @self.app.route('/api/vector-regions')
        def get_vector_regions():
            """获取可用的矢量数据区域列表"""
            try:
                import os
                vector_dir = 'static/vector_data'
                regions = []
                
                if os.path.exists(vector_dir):
                    for item in os.listdir(vector_dir):
                        item_path = os.path.join(vector_dir, item)
                        if os.path.isdir(item_path) and not item.startswith('.'):
                            # 检查是否有矢量数据文件
                            has_data = any(
                                os.path.exists(os.path.join(item_path, f'{data_type}.osm'))
                                for data_type in ['buildings', 'roads', 'pois']
                            )
                            if has_data:
                                regions.append(item)
                
                return jsonify({'regions': sorted(regions)})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def record_gps_history(self, well_id, lat, lon):
        """记录GPS历史轨迹"""
        if well_id not in self.gps_history:
            self.gps_history[well_id] = []
        
        self.gps_history[well_id].append({
            'lat': lat,
            'lon': lon,
            'timestamp': datetime.now().isoformat()
        })
        
        # 只保留最近100条记录
        if len(self.gps_history[well_id]) > 100:
            self.gps_history[well_id] = self.gps_history[well_id][-100:]
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（米）"""
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def add_sample_wells(self):
        """添加示例机井数据"""
        sample_wells = [
            {
                'id': 'WELL001',
                'name': '机井001',
                'lat': 39.9042,
                'lon': 116.4074,
                'status': 'active',
                'gas_level': 25,
                'fan_status': 'on',
                'description': '北京朝阳区机井'
            },
            {
                'id': 'WELL002',
                'name': '机井002',
                'lat': 31.2304,
                'lon': 121.4737,
                'status': 'active',
                'gas_level': 45,
                'fan_status': 'on',
                'description': '上海浦东机井'
            },
            {
                'id': 'WELL003',
                'name': '机井003',
                'lat': 22.3193,
                'lon': 114.1694,
                'status': 'warning',
                'gas_level': 75,
                'fan_status': 'on',
                'description': '深圳南山机井'
            }
        ]
        
        for well in sample_wells:
            self.wells[well['id']] = well
            self.record_gps_history(well['id'], well['lat'], well['lon'])
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行系统"""
        self.add_sample_wells()
        print("🔧 GPS机井监控地图系统")
        print("=" * 50)
        print(f"🌐 系统将在 http://{host}:{port} 启动")
        print("📱 请在浏览器中访问该地址")
        print("🎯 功能特点:")
        print("  - GPS坐标定位")
        print("  - 机井实时监控")
        print("  - 气体检测数据")
        print("  - 送风机状态")
        print("  - 历史轨迹追踪")
        print("=" * 50)
        
        self.app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    system = GPSWellMonitoringSystem()
    system.run()
