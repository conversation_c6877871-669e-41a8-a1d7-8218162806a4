<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速20米精度离线地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            padding: 20px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .control-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }
        
        .control-panel h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            width: 100%;
            margin-top: 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn.fast {
            background: #FF9800;
        }
        
        .btn.fast:hover {
            background: #F57C00;
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .status.error {
            background: #ffeaea;
            border-color: #f44336;
        }
        
        .status.warning {
            background: #fff3e0;
            border-color: #ff9800;
        }
        
        .precision-info {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .precision-info h4 {
            margin: 0 0 10px 0;
            color: #2E7D32;
            font-size: 16px;
        }
        
        .precision-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .precision-info li {
            font-size: 13px;
            margin-bottom: 5px;
        }
        
        .coordinates {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f5f5f5;
            padding: 8px;
            border-radius: 3px;
            margin-top: 5px;
            border: 1px solid #ddd;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 1000;
            display: none;
            text-align: center;
        }
        
        .performance-info {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .performance-info h4 {
            margin: 0 0 5px 0;
            color: #F57C00;
        }
        
        .performance-info ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .performance-info li {
            font-size: 12px;
            margin-bottom: 3px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .quick-actions .btn {
            margin-top: 0;
            font-size: 12px;
            padding: 8px 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 快速20米精度离线地图系统</h1>
        <div class="subtitle">优化版本 - 基于本地OSM数据的快速高精度地图</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="precision-info">
                <h4>⚡ 快速精度规格</h4>
                <ul>
                    <li>坐标精度: 8位小数 (约1.1米)</li>
                    <li>定位精度: ±20米</li>
                    <li>生成速度: 优化版本</li>
                    <li>数据来源: 本地OSM文件</li>
                    <li>智能限制: 避免超时</li>
                </ul>
            </div>
            
            <div class="performance-info">
                <h4>🎯 性能优化</h4>
                <ul>
                    <li>限制节点数量: 10,000个</li>
                    <li>限制路径数量: 10,000条</li>
                    <li>快速区域提取</li>
                    <li>智能缓存机制</li>
                    <li>串行生成避免竞争</li>
                </ul>
            </div>
            
            <div class="control-panel">
                <h3>📍 精确定位</h3>
                <div class="input-group">
                    <label>纬度 (8位小数)</label>
                    <input type="number" id="lat-input" step="0.00000001" placeholder="39.90420000">
                </div>
                <div class="input-group">
                    <label>经度 (8位小数)</label>
                    <input type="number" id="lon-input" step="0.00000001" placeholder="116.40740000">
                </div>
                <div class="quick-actions">
                    <button class="btn" onclick="gotoLocation()">定位</button>
                    <button class="btn" onclick="getCurrentLocation()">GPS</button>
                </div>
            </div>
            
            <div class="control-panel">
                <h3>⚡ 快速生成</h3>
                <button class="btn fast" id="fast-generate-btn" onclick="generateFastArea()">
                    快速生成区域数据
                </button>
                <div class="status warning">
                    <strong>说明:</strong> 快速模式会限制数据量以确保在30秒内完成生成
                </div>
                <div id="fast-generation-status"></div>
            </div>
            
            <div class="control-panel">
                <h3>📊 当前状态</h3>
                <div id="current-coordinates" class="coordinates">
                    点击地图获取坐标
                </div>
                <div id="zoom-level" class="coordinates">
                    缩放级别: -
                </div>
                <div id="tile-info" class="coordinates">
                    瓦片: -
                </div>
                <div id="performance-info" class="coordinates">
                    性能: 等待操作
                </div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 快速操作</h3>
                <div class="quick-actions">
                    <button class="btn" onclick="gotoBeijing()">北京</button>
                    <button class="btn" onclick="gotoShanghai()">上海</button>
                    <button class="btn" onclick="gotoGuangzhou()">广州</button>
                    <button class="btn" onclick="gotoShenzhen()">深圳</button>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="loading" id="loading">
                <div>⚡ 正在快速生成高精度数据...</div>
                <div style="font-size: 12px; margin-top: 10px;">预计30秒内完成</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京天安门
            zoom: 16,
            maxZoom: 18,  // 限制最大缩放以提高性能
            minZoom: 10
        });

        // 添加快速精度瓦片图层
        const fastTileLayer = L.tileLayer('/tiles/{z}/{x}/{y}.png', {
            attribution: '© 快速20米精度系统 | 基于本地OSM数据',
            maxZoom: 18,
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // 当前位置标记
        let currentMarker = null;
        let lastGenerationTime = 0;

        // 地图事件
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(8);
            const lng = e.latlng.lng.toFixed(8);
            
            document.getElementById('current-coordinates').innerHTML = 
                `纬度: ${lat}<br>经度: ${lng}`;
            
            // 更新输入框
            document.getElementById('lat-input').value = lat;
            document.getElementById('lon-input').value = lng;
            
            // 添加标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`精确坐标:<br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
        });

        map.on('zoomend', function() {
            const zoom = map.getZoom();
            document.getElementById('zoom-level').innerHTML = `缩放级别: ${zoom}`;
            
            // 更新瓦片信息
            const center = map.getCenter();
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            document.getElementById('tile-info').innerHTML = 
                `瓦片: ${zoom}/${tileCoord.x}/${tileCoord.y}`;
        });

        // 工具函数
        function getTileCoordinates(lat, lng, zoom) {
            const latRad = lat * Math.PI / 180;
            const n = Math.pow(2, zoom);
            const x = Math.floor((lng + 180) / 360 * n);
            const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
            return { x, y };
        }

        function gotoLocation() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请输入有效的坐标');
                return;
            }
            
            map.setView([lat, lng], 17);
            
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`目标位置:<br>纬度: ${lat.toFixed(8)}<br>经度: ${lng.toFixed(8)}`)
                .openPopup();
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude.toFixed(8);
                    const lng = position.coords.longitude.toFixed(8);
                    
                    document.getElementById('lat-input').value = lat;
                    document.getElementById('lon-input').value = lng;
                    
                    map.setView([lat, lng], 17);
                    
                    if (currentMarker) {
                        map.removeLayer(currentMarker);
                    }
                    currentMarker = L.marker([lat, lng]).addTo(map)
                        .bindPopup(`当前位置:<br>纬度: ${lat}<br>经度: ${lng}<br>精度: ±${position.coords.accuracy}米`)
                        .openPopup();
                }, function(error) {
                    alert('无法获取当前位置: ' + error.message);
                });
            } else {
                alert('浏览器不支持地理定位');
            }
        }

        function generateFastArea() {
            const lat = parseFloat(document.getElementById('lat-input').value);
            const lng = parseFloat(document.getElementById('lon-input').value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('请先设置坐标');
                return;
            }
            
            // 防止频繁生成
            const now = Date.now();
            if (now - lastGenerationTime < 30000) {
                alert('请等待30秒后再次生成');
                return;
            }
            lastGenerationTime = now;
            
            const btn = document.getElementById('fast-generate-btn');
            const status = document.getElementById('fast-generation-status');
            const loading = document.getElementById('loading');
            const performanceInfo = document.getElementById('performance-info');
            
            btn.disabled = true;
            btn.textContent = '快速生成中...';
            loading.style.display = 'block';
            
            const startTime = Date.now();
            
            fetch(`/api/generate-fast-area?lat=${lat}&lon=${lng}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        status.innerHTML = `<div class="status">${data.message}</div>`;
                        
                        // 30秒后刷新瓦片
                        setTimeout(() => {
                            fastTileLayer.redraw();
                            btn.disabled = false;
                            btn.textContent = '快速生成区域数据';
                            loading.style.display = 'none';
                            
                            const elapsed = (Date.now() - startTime) / 1000;
                            status.innerHTML = '<div class="status">快速生成完成，地图已更新</div>';
                            performanceInfo.innerHTML = `生成用时: ${elapsed.toFixed(1)}秒`;
                        }, 30000);
                    } else {
                        throw new Error(data.error || '生成失败');
                    }
                })
                .catch(error => {
                    status.innerHTML = `<div class="status error">错误: ${error.message}</div>`;
                    btn.disabled = false;
                    btn.textContent = '快速生成区域数据';
                    loading.style.display = 'none';
                });
        }

        // 快速定位函数
        function gotoBeijing() {
            document.getElementById('lat-input').value = '39.90420000';
            document.getElementById('lon-input').value = '116.40740000';
            gotoLocation();
        }

        function gotoShanghai() {
            document.getElementById('lat-input').value = '31.23040000';
            document.getElementById('lon-input').value = '121.47370000';
            gotoLocation();
        }

        function gotoGuangzhou() {
            document.getElementById('lat-input').value = '23.12910000';
            document.getElementById('lon-input').value = '113.26440000';
            gotoLocation();
        }

        function gotoShenzhen() {
            document.getElementById('lat-input').value = '22.54280000';
            document.getElementById('lon-input').value = '114.05950000';
            gotoLocation();
        }

        // 初始化显示
        document.getElementById('zoom-level').innerHTML = `缩放级别: ${map.getZoom()}`;
        document.getElementById('performance-info').innerHTML = '性能: 系统就绪';
    </script>
</body>
</html>
