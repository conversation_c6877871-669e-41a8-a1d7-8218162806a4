#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载全国34个省市自治区数据
完整覆盖中国大陆所有省级行政区
"""

import os
import time
import requests
from pathlib import Path

class DownloadAllChina:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.timeout = 30
        self.max_retries = 5
        self.retry_delay = 15
        self.request_delay = 5
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 全国34个省市自治区配置
        self.all_provinces = {
            # 直辖市
            "北京": "116.0,39.4,117.0,40.2",
            "上海": "121.0,31.0,122.0,31.5",
            "天津": "116.8,38.5,118.0,40.2",
            "重庆": "105.0,28.0,110.0,32.0",
            
            # 省份
            "河北": "113.0,36.0,120.0,42.0",
            "山西": "110.0,34.0,114.0,40.0",
            "辽宁": "118.0,38.0,125.0,43.0",
            "吉林": "121.0,40.0,131.0,46.0",
            "黑龙江": "121.0,43.0,135.0,53.0",
            "江苏": "116.0,30.0,122.0,35.0",
            "浙江": "118.0,27.0,123.0,31.0",
            "安徽": "114.0,29.0,120.0,35.0",
            "福建": "115.0,23.0,121.0,28.0",
            "江西": "113.0,24.0,118.0,30.0",
            "山东": "114.0,34.0,123.0,38.0",
            "河南": "110.0,31.0,117.0,36.0",
            "湖北": "108.0,29.0,116.0,33.0",
            "湖南": "108.0,24.0,114.0,30.0",
            "广东": "109.0,20.0,117.0,25.0",
            "海南": "108.0,18.0,111.0,20.0",
            "四川": "97.0,26.0,109.0,34.0",
            "贵州": "103.0,24.0,109.0,29.0",
            "云南": "97.0,21.0,106.0,29.0",
            "陕西": "105.0,31.0,111.0,39.0",
            "甘肃": "92.0,32.0,109.0,43.0",
            "青海": "89.0,31.0,103.0,39.0",
            
            # 自治区
            "内蒙古": "97.0,37.0,126.0,53.0",
            "广西": "104.0,20.0,112.0,26.0",
            "西藏": "78.0,27.0,99.0,36.0",
            "宁夏": "104.0,35.0,107.0,39.0",
            "新疆": "73.0,34.0,96.0,49.0",
            
            # 特别行政区
            "香港": "113.8,22.1,114.4,22.6",
            "澳门": "113.5,22.1,113.6,22.2"
        }
        
        # 数据类型
        self.data_types = ["roads", "buildings", "pois"]
        
        print(f"🔧 下载全国34个省市自治区数据")
        print(f"📊 总省份数: {len(self.all_provinces)}")
        print(f"📊 数据类型: {', '.join(self.data_types)}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        print(f"⏳ 请求间隔: {self.request_delay}秒")
    
    def convert_bbox_format(self, bbox):
        """转换边界框格式为正确的Overpass格式 (南,西,北,东)"""
        if isinstance(bbox, str):
            bbox = bbox.strip().rstrip(',')
            parts = bbox.split(',')
            if len(parts) != 4:
                raise ValueError("边界框必须有4个坐标")
            # 假设输入格式是 (经度最小,纬度最小,经度最大,纬度最大)
            lon_min, lat_min, lon_max, lat_max = [float(x.strip()) for x in parts]
        else:
            # 假设输入格式是 [经度最小,纬度最小,经度最大,纬度最大]
            lon_min, lat_min, lon_max, lat_max = bbox
        
        # 转换为Overpass格式 (南,西,北,东)
        return f"{lat_min},{lon_min},{lat_max},{lat_max}"
    
    def get_working_query(self, bbox, data_type):
        """构建可工作的查询语句"""
        bbox_str = self.convert_bbox_format(bbox)
        
        if data_type == "roads":
            return f"[out:xml];(way[\"highway\"=\"primary\"]({bbox_str}););out geom;"
        elif data_type == "buildings":
            return f"[out:xml];(way[\"building\"]({bbox_str}););out geom;"
        elif data_type == "pois":
            return f"[out:xml];(node[\"amenity\"]({bbox_str}););out;"
        else:
            return f"[out:xml];(way[\"highway\"]({bbox_str}););out geom;"
    
    def download_with_retry(self, url, data, max_retries=None):
        """带重试机制的下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt + 1}次)...")
                
                response = requests.post(
                    url, 
                    data=data, 
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    if response.text.strip().startswith('<?xml'):
                        return response
                    else:
                        print(f"  ⚠️ 响应不是XML格式")
                        return None
                elif response.status_code == 400:
                    print(f"  HTTP 400错误: 请求格式错误")
                    return None
                elif response.status_code == 429:
                    print(f"  HTTP 429错误: 请求过于频繁")
                    wait_time = self.retry_delay * (attempt + 1) * 2
                    print(f"  等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                print(f"  等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        return None
    
    def check_region_status(self, region_name):
        """检查区域下载状态"""
        region_dir = self.data_dir / region_name
        if not region_dir.exists():
            return "未开始"
        
        files = list(region_dir.glob("*.osm"))
        if len(files) == 0:
            return "空文件夹"
        elif len(files) == 3:
            # 检查文件大小
            total_size = sum(f.stat().st_size for f in files)
            if total_size > 3000:  # 总大小大于3KB
                return "已完成"
            else:
                return "数据不完整"
        else:
            return "部分完成"
    
    def download_region_data(self, region_name, bbox):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in self.data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            try:
                query = self.get_working_query(bbox, data_type)
                print(f"  📋 查询语句:")
                print(f"  {query}")
                
                response = self.download_with_retry(self.base_url, {"data": query})
                
                if response and response.status_code == 200:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    file_size = file_path.stat().st_size
                    print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                    results[data_type] = "成功"
                    
                else:
                    print(f"  ❌ {data_type} 数据下载失败")
                    results[data_type] = "失败"
                    
            except Exception as e:
                print(f"  ❌ {data_type} 下载异常: {e}")
                results[data_type] = "异常"
            
            # 请求间隔
            if data_type != self.data_types[-1]:
                print(f"  ⏳ 等待{self.request_delay}秒，避免限流...")
                time.sleep(self.request_delay)
        
        return results
    
    def get_missing_provinces(self):
        """获取缺失的省份列表"""
        missing_provinces = {}
        completed_count = 0
        
        print("\n📊 检查所有省份状态...")
        for region_name, bbox in self.all_provinces.items():
            status = self.check_region_status(region_name)
            if status != "已完成":
                missing_provinces[region_name] = bbox
                print(f"  ❌ {region_name}: {status}")
            else:
                completed_count += 1
                print(f"  ✅ {region_name}: {status}")
        
        print(f"\n📊 状态汇总:")
        print(f"✅ 已完成: {completed_count}")
        print(f"❌ 缺失: {len(missing_provinces)}")
        
        return missing_provinces
    
    def download_missing_provinces(self, missing_provinces):
        """下载缺失的省份数据"""
        print(f"🚀 开始下载 {len(missing_provinces)} 个缺失省份的矢量数据")
        print(f"📊 数据类型: {', '.join(self.data_types)}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        print(f"⏳ 请求间隔: {self.request_delay}秒")
        
        results = {}
        
        for i, (region_name, bbox) in enumerate(missing_provinces.items(), 1):
            print(f"\n{'='*60}")
            print(f"🗺️ 处理省份 {i}/{len(missing_provinces)}: {region_name}")
            print(f"{'='*60}")
            
            result = self.download_region_data(region_name, bbox)
            results[region_name] = result
            
            # 省份间间隔
            if i < len(missing_provinces):
                print(f"\n⏳ 省份间等待{self.request_delay * 2}秒...")
                time.sleep(self.request_delay * 2)
        
        return results

def main():
    """主函数"""
    downloader = DownloadAllChina()
    
    print("🔧 下载全国34个省市自治区数据")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 检查所有省份状态")
        print("2. 下载缺失的省份数据")
        print("3. 下载所有省份数据 (重新下载)")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            missing_provinces = downloader.get_missing_provinces()
            
            if missing_provinces:
                print(f"\n❌ 发现 {len(missing_provinces)} 个省份数据缺失:")
                for region_name in missing_provinces.keys():
                    print(f"  - {region_name}")
            else:
                print("\n✅ 所有省份数据都已完成!")
        
        elif choice == "2":
            missing_provinces = downloader.get_missing_provinces()
            
            if not missing_provinces:
                print("\n✅ 所有省份数据都已完成，无需下载!")
                continue
            
            print(f"\n🚀 开始下载 {len(missing_provinces)} 个缺失省份的数据...")
            print("⚠️ 注意: 这将需要较长时间")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_missing_provinces(missing_provinces)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "3":
            print(f"\n🚀 开始下载所有 {len(downloader.all_provinces)} 个省份的数据...")
            print("⚠️ 注意: 这将需要很长时间，会重新下载所有数据")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_missing_provinces(downloader.all_provinces)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "4":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
