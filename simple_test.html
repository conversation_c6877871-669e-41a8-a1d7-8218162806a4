<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            height: 600px;
        }
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
        }
        .map-container {
            flex: 1;
            position: relative;
        }
        #map {
            width: 100%;
            height: 100%;
        }
        .search-section {
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .search-button {
            width: 100%;
            padding: 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        .search-button:hover {
            background: #5a6fd8;
        }
        .results {
            margin-top: 20px;
        }
        .result-item {
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            background: white;
        }
        .result-item:hover {
            background: #f0f0f0;
        }
        .result-name {
            font-weight: bold;
            color: #333;
        }
        .result-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 搜索功能测试</h1>
            <p>测试搜索和定位功能是否正常工作</p>
        </div>
        
        <div class="content">
            <div class="sidebar">
                <div class="search-section">
                    <h3>搜索测试</h3>
                    <input type="text" id="searchInput" class="search-input" placeholder="输入搜索关键词，如：四川、北京、天安门" value="四川">
                    <button onclick="performSearch()" class="search-button">🔍 搜索</button>
                </div>
                
                <div id="status" class="status success">
                    ✅ 系统就绪，请输入关键词进行搜索
                </div>
                
                <div class="results">
                    <h3>搜索结果</h3>
                    <div id="searchResults">
                        <div class="loading">等待搜索...</div>
                    </div>
                </div>
            </div>
            
            <div class="map-container">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([35.0, 105.0], 5);
        
        // 添加地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(map);
        
        let currentMarkers = [];
        
        // 搜索函数
        async function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showStatus('请输入搜索关键词', 'error');
                return;
            }
            
            showStatus('搜索中...', 'success');
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
            
            try {
                console.log('开始搜索:', query);
                const response = await fetch(`/api/search-poi?q=${encodeURIComponent(query)}`);
                console.log('响应状态:', response.status);
                
                if (response.ok) {
                    const results = await response.json();
                    console.log('搜索结果:', results);
                    displayResults(results);
                    showStatus(`找到 ${results.length} 个结果`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('搜索错误:', error);
                showStatus(`搜索失败: ${error.message}`, 'error');
                resultsDiv.innerHTML = '<div class="loading">搜索失败</div>';
            }
        }
        
        // 显示搜索结果
        function displayResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">未找到相关结果</div>';
                return;
            }
            
            resultsDiv.innerHTML = results.map(result => {
                let typeText = '';
                if (result.type === 'famous_place') {
                    typeText = '著名地标';
                } else if (result.type === 'region') {
                    typeText = '区域';
                } else if (result.type === 'poi') {
                    typeText = '兴趣点';
                } else if (result.type === 'city') {
                    typeText = '地级市';
                } else if (result.type === 'county') {
                    typeText = '县级市/区/县';
                } else if (result.type === 'town') {
                    typeText = '乡镇';
                }
                
                return `
                    <div class="result-item" onclick="locateResult('${result.name}', ${result.lat}, ${result.lon})">
                        <div class="result-name">${result.name}</div>
                        <div class="result-info">
                            ${result.region} - ${typeText}
                            ${result.level ? ` (${result.level})` : ''}
                            ${result.capital_name ? ` (省会: ${result.capital_name})` : ''}
                            ${result.county ? ` (所属: ${result.county})` : ''}
                            ${result.amenity ? ` (${result.amenity})` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 定位到结果
        function locateResult(name, lat, lon) {
            console.log('定位到:', name, lat, lon);
            
            // 清除之前的标记
            currentMarkers.forEach(m => map.removeLayer(m));
            currentMarkers = [];
            
            // 聚焦到位置
            map.flyTo([lat, lon], 16, {
                animate: true,
                duration: 1.5
            });
            
            // 添加标记
            const marker = L.marker([lat, lon]).addTo(map);
            marker.bindPopup(`<b>${name}</b><br>坐标: ${lat.toFixed(4)}, ${lon.toFixed(4)}`).openPopup();
            
            // 添加圆形高亮
            const circle = L.circle([lat, lon], {
                color: '#ff6b6b',
                fillColor: '#ff6b6b',
                fillOpacity: 0.2,
                radius: 200
            }).addTo(map);
            
            currentMarkers.push(marker, circle);
            showStatus(`已定位到: ${name}`, 'success');
        }
        
        // 显示状态
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // 页面加载完成后自动搜索
        window.onload = function() {
            console.log('页面加载完成，开始自动搜索测试');
            setTimeout(performSearch, 1000);
        };
    </script>
</body>
</html>
