#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试OSM数据处理
查看为什么没有提取到道路和建筑物数据
"""

import os
import sys
from pathlib import Path
from enhanced_osm_processor import EnhancedOSMProcessor

def debug_osm_processing():
    """调试OSM数据处理"""
    print("🔍 调试OSM数据处理")
    print("=" * 50)
    
    # 测试瓦片坐标
    test_tiles = [
        (10, 840, 385),  # 第10级测试瓦片
        (11, 1671, 1681),  # 第11级测试瓦片  
        (12, 3363, 1525),  # 第12级测试瓦片
    ]
    
    osm_file = Path("F:/monitor1/china-latest.osm.pbf")
    if not osm_file.exists():
        print(f"❌ OSM文件不存在: {osm_file}")
        return False
    
    print(f"✅ OSM文件存在，大小: {osm_file.stat().st_size / 1024 / 1024:.1f} MB")
    
    for zoom, x, y in test_tiles:
        print(f"\n🔬 调试瓦片 {zoom}/{x}/{y}:")
        
        # 计算瓦片边界框
        def deg2num(lat_deg, lon_deg, zoom):
            import math
            lat_rad = math.radians(lat_deg)
            n = 2.0 ** zoom
            x = int((lon_deg + 180.0) / 360.0 * n)
            y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
            return (x, y)
        
        def num2deg(x, y, zoom):
            import math
            n = 2.0 ** zoom
            lon_deg = x / n * 360.0 - 180.0
            lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
            lat_deg = math.degrees(lat_rad)
            return (lat_deg, lon_deg)
        
        # 计算瓦片边界
        lat1, lon1 = num2deg(x, y, zoom)
        lat2, lon2 = num2deg(x + 1, y + 1, zoom)
        bbox = [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
        
        print(f"  边界框: {bbox}")
        print(f"  经度范围: {bbox[0]:.6f} - {bbox[2]:.6f}")
        print(f"  纬度范围: {bbox[1]:.6f} - {bbox[3]:.6f}")
        
        # 创建OSM处理器
        try:
            processor = EnhancedOSMProcessor(bbox, zoom)
            print(f"  开始处理OSM数据...")
            
            # 处理OSM文件
            processor.apply_file(str(osm_file))
            
            print(f"  📊 处理结果:")
            print(f"    节点数量: {len(processor.nodes)}")
            print(f"    路径数量: {len(processor.ways)}")
            print(f"    建筑物数量: {len(processor.buildings)}")
            print(f"    道路数量: {len(processor.roads)}")
            print(f"    水体数量: {len(processor.water_features)}")
            
            # 显示一些示例数据
            if processor.nodes:
                print(f"  📍 节点示例:")
                for i, (node_id, node_data) in enumerate(list(processor.nodes.items())[:3]):
                    print(f"    {node_id}: ({node_data['lon']:.6f}, {node_data['lat']:.6f})")
            
            if processor.roads:
                print(f"  🛣️  道路示例:")
                for i, road in enumerate(processor.roads[:3]):
                    highway_type = road['tags'].get('highway', 'unknown')
                    print(f"    {road['id']}: {highway_type} ({len(road['nodes'])} 节点)")
            
            if processor.buildings:
                print(f"  🏢 建筑物示例:")
                for i, building in enumerate(processor.buildings[:3]):
                    building_type = building['tags'].get('building', 'unknown')
                    print(f"    {building['id']}: {building_type} ({len(building['nodes'])} 节点)")
            
            if processor.water_features:
                print(f"  💧 水体示例:")
                for i, water in enumerate(processor.water_features[:3]):
                    water_type = water['tags'].get('natural', water['tags'].get('waterway', 'unknown'))
                    print(f"    {water['id']}: {water_type} ({len(water['nodes'])} 节点)")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    return True

def main():
    """主函数"""
    try:
        debug_osm_processing()
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
