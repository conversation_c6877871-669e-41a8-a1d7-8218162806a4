#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
20级超高精度地图系统
专门使用20级瓦片的地图系统，提供最高精度的地图显示
"""

import os
import sys
import json
import time
from pathlib import Path
from flask import Flask, render_template, jsonify, request, send_file
import sqlite3

class Level20MapSystem:
    """20级超高精度地图系统"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.level20_dir = self.tiles_dir / "20"
        self.level18_dir = self.tiles_dir / "18"
        
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('level20_map.html')
        
        @self.app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
        def get_tile(z, x, y):
            """获取瓦片 - 优先使用20级，回退到18级"""
            
            # 优先尝试20级瓦片
            if z == 20:
                level20_tile = self.level20_dir / str(x) / f"{y}.png"
                if level20_tile.exists():
                    return send_file(str(level20_tile), mimetype='image/png')
            
            # 回退到18级瓦片
            if z >= 18:
                # 计算对应的18级瓦片坐标
                scale_factor = 2 ** (z - 18)
                x18 = x // scale_factor
                y18 = y // scale_factor
                
                level18_tile = self.level18_dir / str(x18) / f"{y18}.png"
                if level18_tile.exists():
                    return send_file(str(level18_tile), mimetype='image/png')
            
            # 如果都没有，返回空瓦片
            return self.create_empty_tile(z, x, y)
        
        @self.app.route('/api/tile-status')
        def get_tile_status():
            """获取瓦片状态"""
            status = {
                'level20': self.analyze_level_tiles(20),
                'level18': self.analyze_level_tiles(18),
                'coverage': self.get_coverage_info(),
                'timestamp': time.time()
            }
            return jsonify(status)
        
        @self.app.route('/api/precision-info/<int:z>/<int:x>/<int:y>')
        def get_precision_info(z, x, y):
            """获取指定瓦片的精度信息"""
            info = self.calculate_tile_precision(z, x, y)
            return jsonify(info)
    
    def analyze_level_tiles(self, level):
        """分析指定级别的瓦片"""
        level_dir = self.tiles_dir / str(level)
        
        if not level_dir.exists():
            return {
                'exists': False,
                'count': 0,
                'size_mb': 0,
                'coverage_area': "无数据"
            }
        
        tile_count = 0
        total_size = 0
        x_coords = []
        y_coords = []
        
        try:
            for x_dir in level_dir.iterdir():
                if x_dir.is_dir() and x_dir.name.isdigit():
                    x = int(x_dir.name)
                    
                    for tile_file in x_dir.glob("*.png"):
                        if tile_file.stat().st_size > 1000:  # 有效瓦片
                            tile_count += 1
                            total_size += tile_file.stat().st_size
                            x_coords.append(x)
                            y_coords.append(int(tile_file.stem))
        
        except Exception as e:
            print(f"分析{level}级瓦片失败: {e}")
        
        # 计算覆盖面积
        coverage_area = "未知"
        if x_coords and y_coords:
            x_range = max(x_coords) - min(x_coords) + 1
            y_range = max(y_coords) - min(y_coords) + 1
            
            # 每个瓦片的地面距离
            tile_size_km = 40075 / (2 ** level)
            area_km2 = (x_range * tile_size_km) * (y_range * tile_size_km)
            
            if area_km2 < 1:
                coverage_area = f"{area_km2 * 1000000:.0f}平方米"
            elif area_km2 < 100:
                coverage_area = f"{area_km2:.2f}平方公里"
            else:
                coverage_area = f"{area_km2:.0f}平方公里"
        
        return {
            'exists': True,
            'count': tile_count,
            'size_mb': round(total_size / 1024 / 1024, 2),
            'coverage_area': coverage_area
        }
    
    def get_coverage_info(self):
        """获取覆盖信息"""
        coverage = {
            'level20_areas': [],
            'level18_areas': [],
            'precision_comparison': {}
        }
        
        # 分析20级覆盖
        if self.level20_dir.exists():
            for x_dir in self.level20_dir.iterdir():
                if x_dir.is_dir() and x_dir.name.isdigit():
                    x = int(x_dir.name)
                    y_files = list(x_dir.glob("*.png"))
                    if y_files:
                        y_coords = [int(f.stem) for f in y_files]
                        coverage['level20_areas'].append({
                            'x': x,
                            'y_range': [min(y_coords), max(y_coords)],
                            'tile_count': len(y_files)
                        })
        
        # 精度对比
        coverage['precision_comparison'] = {
            'level18': {
                'ground_resolution_m': 40075000 / (2 ** 18) / 256,  # 每像素地面距离
                'tile_coverage_m': 40075000 / (2 ** 18),  # 每瓦片覆盖距离
                'precision_level': "高精度"
            },
            'level20': {
                'ground_resolution_m': 40075000 / (2 ** 20) / 256,
                'tile_coverage_m': 40075000 / (2 ** 20),
                'precision_level': "超高精度"
            }
        }
        
        return coverage
    
    def calculate_tile_precision(self, z, x, y):
        """计算瓦片精度信息"""
        # 计算瓦片的地理边界
        n = 2.0 ** z
        lon1 = x / n * 360.0 - 180.0
        lat1_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat1 = math.degrees(lat1_rad)
        
        lon2 = (x + 1) / n * 360.0 - 180.0
        lat2_rad = math.atan(math.sinh(math.pi * (1 - 2 * (y + 1) / n)))
        lat2 = math.degrees(lat2_rad)
        
        # 计算精度
        import math
        
        # 瓦片宽度和高度（米）
        lat_center = (lat1 + lat2) / 2
        lon_diff_m = abs(lon2 - lon1) * 111000 * math.cos(math.radians(lat_center))
        lat_diff_m = abs(lat2 - lat1) * 111000
        
        # 像素精度
        pixel_precision_lon = lon_diff_m / 256
        pixel_precision_lat = lat_diff_m / 256
        
        return {
            'zoom_level': z,
            'tile_coordinates': {'x': x, 'y': y},
            'geographic_bounds': {
                'north': max(lat1, lat2),
                'south': min(lat1, lat2),
                'east': max(lon1, lon2),
                'west': min(lon1, lon2)
            },
            'tile_size_meters': {
                'width': lon_diff_m,
                'height': lat_diff_m
            },
            'pixel_precision_meters': {
                'longitude': pixel_precision_lon,
                'latitude': pixel_precision_lat,
                'average': (pixel_precision_lon + pixel_precision_lat) / 2
            },
            'precision_level': self.get_precision_level(z)
        }
    
    def get_precision_level(self, zoom):
        """获取精度级别描述"""
        if zoom >= 20:
            return "超高精度 (亚米级)"
        elif zoom >= 18:
            return "高精度 (米级)"
        elif zoom >= 16:
            return "中等精度 (数米级)"
        else:
            return "低精度 (十米级以上)"
    
    def create_empty_tile(self, z, x, y):
        """创建空瓦片"""
        from PIL import Image, ImageDraw
        import io
        
        img = Image.new('RGB', (256, 256), color='#f0f0f0')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), f"Z{z}/{x}/{y}", fill='#999999')
        draw.text((10, 30), "No Data", fill='#999999')
        
        img_io = io.BytesIO()
        img.save(img_io, 'PNG')
        img_io.seek(0)
        return img_io
    
    def run(self, host='127.0.0.1', port=5007, debug=False):
        """运行20级地图系统"""
        print("🎯 20级超高精度地图系统")
        print("=" * 60)
        print(f"🌐 系统地址: http://{host}:{port}")
        print(f"📁 20级瓦片: {self.level20_dir}")
        print(f"📁 18级瓦片: {self.level18_dir}")
        print("🎯 特性:")
        print("  - 20级超高精度瓦片显示")
        print("  - 自动回退到18级瓦片")
        print("  - 实时精度信息显示")
        print("  - 瓦片状态监控")
        print("=" * 60)
        
        # 检查瓦片状态
        level20_status = self.analyze_level_tiles(20)
        level18_status = self.analyze_level_tiles(18)
        
        print(f"📊 20级瓦片: {level20_status['count']} 个 ({level20_status['size_mb']} MB)")
        print(f"📊 18级瓦片: {level18_status['count']} 个 ({level18_status['size_mb']} MB)")
        
        if level20_status['count'] == 0 and level18_status['count'] == 0:
            print("⚠️  警告: 没有找到有效的瓦片文件")
        
        self.app.run(host=host, port=port, debug=debug)

# 创建20级地图模板
def create_level20_map_template():
    """创建20级地图模板"""
    template_dir = Path(__file__).parent / "templates"
    template_dir.mkdir(exist_ok=True)
    
    template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>20级超高精度地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 0; font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #8E24AA 0%, #7B1FA2 100%); color: white; padding: 15px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { margin: 0; font-size: 24px; }
        .header .subtitle { margin: 5px 0 0 0; font-size: 14px; opacity: 0.9; }
        .container { display: flex; height: calc(100vh - 80px); }
        .sidebar { width: 350px; background: white; border-right: 1px solid #ddd; overflow-y: auto; padding: 20px; }
        .map-container { flex: 1; position: relative; }
        #map { width: 100%; height: 100%; }
        .control-panel { background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border-left: 4px solid #8E24AA; }
        .control-panel h3 { margin: 0 0 10px 0; color: #333; font-size: 16px; }
        .precision-info { background: #f3e5f5; border: 1px solid #8E24AA; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
        .precision-info h4 { margin: 0 0 10px 0; color: #7B1FA2; font-size: 16px; }
        .stat { display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 13px; }
        .stat-label { font-weight: bold; }
        .stat-value { color: #8E24AA; font-family: monospace; }
        .coordinates { font-family: 'Courier New', monospace; font-size: 12px; background: #f5f5f5; padding: 8px; border-radius: 3px; margin-top: 5px; border: 1px solid #ddd; }
        .btn { background: #8E24AA; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; font-size: 14px; width: 100%; margin-top: 10px; }
        .btn:hover { background: #7B1FA2; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 20级超高精度地图系统</h1>
        <div class="subtitle">亚米级精度 - 基于本地OSM数据的最高精度地图</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="precision-info">
                <h4>🎯 超高精度规格</h4>
                <div class="stat"><span class="stat-label">20级像素精度:</span><span class="stat-value">~0.6米/像素</span></div>
                <div class="stat"><span class="stat-label">18级像素精度:</span><span class="stat-value">~2.4米/像素</span></div>
                <div class="stat"><span class="stat-label">坐标精度:</span><span class="stat-value">10位小数</span></div>
                <div class="stat"><span class="stat-label">定位精度:</span><span class="stat-value">亚米级</span></div>
                <div class="stat"><span class="stat-label">数据来源:</span><span class="stat-value">本地OSM</span></div>
            </div>
            
            <div class="control-panel">
                <h3>📊 瓦片状态</h3>
                <div id="tile-status">检查中...</div>
                <button class="btn" onclick="refreshTileStatus()">刷新状态</button>
            </div>
            
            <div class="control-panel">
                <h3>📍 当前位置信息</h3>
                <div id="current-coordinates" class="coordinates">点击地图获取坐标</div>
                <div id="zoom-level" class="coordinates">缩放级别: -</div>
                <div id="tile-info" class="coordinates">瓦片: -</div>
                <div id="precision-info" class="coordinates">精度: -</div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 快速导航</h3>
                <button class="btn" onclick="gotoBeijing()">北京天安门</button>
                <button class="btn" onclick="gotoShanghai()">上海外滩</button>
                <button class="btn" onclick="gotoGuangzhou()">广州塔</button>
                <button class="btn" onclick="gotoShenzhen()">深圳中心</button>
            </div>
            
            <div class="control-panel">
                <h3>🔍 精度分析</h3>
                <div id="precision-analysis">移动地图查看精度信息</div>
                <button class="btn" onclick="analyzePrecision()">分析当前区域精度</button>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京天安门
            zoom: 18,
            maxZoom: 20,
            minZoom: 16
        });

        // 添加20级瓦片图层
        const level20TileLayer = L.tileLayer('/tiles/{z}/{x}/{y}.png', {
            attribution: '© 20级超高精度系统 | 基于本地OSM数据',
            maxZoom: 20,
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // 当前位置标记
        let currentMarker = null;

        // 地图事件
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(10);
            const lng = e.latlng.lng.toFixed(10);
            
            document.getElementById('current-coordinates').innerHTML = 
                `纬度: ${lat}<br>经度: ${lng}`;
            
            // 添加标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`超高精度坐标:<br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
            
            // 分析精度
            analyzePrecision();
        });

        map.on('zoomend moveend', function() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            
            document.getElementById('zoom-level').innerHTML = `缩放级别: ${zoom}`;
            
            // 更新瓦片信息
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            document.getElementById('tile-info').innerHTML = 
                `瓦片: ${zoom}/${tileCoord.x}/${tileCoord.y}`;
            
            // 更新精度信息
            updatePrecisionInfo(zoom);
        });

        // 工具函数
        function getTileCoordinates(lat, lng, zoom) {
            const latRad = lat * Math.PI / 180;
            const n = Math.pow(2, zoom);
            const x = Math.floor((lng + 180) / 360 * n);
            const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
            return { x, y };
        }

        function updatePrecisionInfo(zoom) {
            let precision, level;
            
            if (zoom >= 20) {
                precision = "~0.6米/像素";
                level = "超高精度 (亚米级)";
            } else if (zoom >= 18) {
                precision = "~2.4米/像素";
                level = "高精度 (米级)";
            } else {
                precision = "~9.6米/像素";
                level = "中等精度";
            }
            
            document.getElementById('precision-info').innerHTML = 
                `精度: ${precision}<br>级别: ${level}`;
        }

        function refreshTileStatus() {
            document.getElementById('tile-status').innerHTML = '检查中...';
            
            fetch('/api/tile-status')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    
                    // 20级瓦片状态
                    const level20 = data.level20;
                    const status20 = level20.exists ? (level20.count > 0 ? 'status-good' : 'status-warning') : 'status-error';
                    html += `<div style="margin-bottom: 10px;">`;
                    html += `<div class="status-indicator ${status20}"></div>`;
                    html += `<strong>20级瓦片:</strong> ${level20.count} 个 (${level20.size_mb} MB)<br>`;
                    html += `<small>覆盖: ${level20.coverage_area}</small>`;
                    html += `</div>`;
                    
                    // 18级瓦片状态
                    const level18 = data.level18;
                    const status18 = level18.exists ? (level18.count > 0 ? 'status-good' : 'status-warning') : 'status-error';
                    html += `<div style="margin-bottom: 10px;">`;
                    html += `<div class="status-indicator ${status18}"></div>`;
                    html += `<strong>18级瓦片:</strong> ${level18.count} 个 (${level18.size_mb} MB)<br>`;
                    html += `<small>覆盖: ${level18.coverage_area}</small>`;
                    html += `</div>`;
                    
                    document.getElementById('tile-status').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('tile-status').innerHTML = '检查失败: ' + error.message;
                });
        }

        function analyzePrecision() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            
            fetch(`/api/precision-info/${zoom}/${tileCoord.x}/${tileCoord.y}`)
                .then(response => response.json())
                .then(data => {
                    let html = `<div style="font-size: 12px;">`;
                    html += `<strong>当前瓦片精度分析:</strong><br>`;
                    html += `缩放级别: ${data.zoom_level}<br>`;
                    html += `瓦片尺寸: ${data.tile_size_meters.width.toFixed(1)}m × ${data.tile_size_meters.height.toFixed(1)}m<br>`;
                    html += `像素精度: ${data.pixel_precision_meters.average.toFixed(2)}m/像素<br>`;
                    html += `精度级别: ${data.precision_level}`;
                    html += `</div>`;
                    
                    document.getElementById('precision-analysis').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('precision-analysis').innerHTML = '分析失败: ' + error.message;
                });
        }

        // 快速导航函数
        function gotoBeijing() {
            map.setView([39.9042, 116.4074], 20);
        }

        function gotoShanghai() {
            map.setView([31.2304, 121.4737], 20);
        }

        function gotoGuangzhou() {
            map.setView([23.1291, 113.2644], 20);
        }

        function gotoShenzhen() {
            map.setView([22.5428, 114.0595], 20);
        }

        // 初始化
        updatePrecisionInfo(map.getZoom());
        refreshTileStatus();
        
        // 自动刷新状态
        setInterval(refreshTileStatus, 60000); // 每分钟刷新一次
    </script>
</body>
</html>'''
    
    template_file = template_dir / "level20_map.html"
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(template_content)

if __name__ == "__main__":
    # 创建模板
    create_level20_map_template()
    
    # 启动20级地图系统
    system = Level20MapSystem()
    system.run()
