#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复查询格式的下载器
解决HTTP 400错误问题
"""

import os
import time
import json
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class FixedQueryDownloader:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.timeout = 30
        self.max_retries = 3
        self.retry_delay = 5
        self.max_workers = 2
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建进度记录文件
        self.progress_file = "fixed_query_progress.json"
        self.load_progress()
        
        print(f"🔧 修复查询格式下载器初始化完成")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
    
    def load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                self.progress = json.load(f)
        else:
            self.progress = {}
    
    def save_progress(self):
        """保存下载进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def get_fixed_query(self, bbox, data_type):
        """构建修复的查询语句"""
        # 确保边界框格式正确
        if isinstance(bbox, str):
            bbox = bbox.strip().rstrip(',')
            parts = bbox.split(',')
            if len(parts) != 4:
                raise ValueError("边界框必须有4个坐标")
            bbox_str = ','.join([x.strip() for x in parts])
        else:
            bbox_str = f"{bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}"
        
        if data_type == "roads":
            # 修复的道路查询 - 使用单行格式
            return f"[out:xml][timeout:30];(way[\"highway\"=\"primary\"]({bbox_str});way[\"highway\"=\"secondary\"]({bbox_str});way[\"highway\"=\"tertiary\"]({bbox_str}););out geom;"
        
        elif data_type == "buildings":
            # 修复的建筑物查询 - 使用单行格式
            return f"[out:xml][timeout:30];(way[\"building\"]({bbox_str}););out geom;"
        
        elif data_type == "pois":
            # 修复的POI查询 - 使用单行格式
            return f"[out:xml][timeout:30];(node[\"amenity\"]({bbox_str}););out;"
        
        else:
            # 默认查询
            return f"[out:xml][timeout:30];(way[\"highway\"]({bbox_str}););out geom;"
    
    def download_with_retry(self, url, data, max_retries=None):
        """带重试机制的下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt + 1}次)...")
                
                # 使用不同的请求方式
                response = requests.post(
                    url, 
                    data=data, 
                    timeout=self.timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/xml, text/xml, */*',
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                    }
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 400:
                    print(f"  HTTP 400错误: 请求格式错误")
                    print(f"  查询内容: {data.get('data', '')[:200]}...")
                    print(f"  响应内容: {response.text[:200]}...")
                    return None
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    print(f"  响应内容: {response.text[:200]}...")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                print(f"  等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        return None
    
    def test_query_format(self, bbox, data_type):
        """测试查询格式"""
        print(f"🧪 测试 {data_type} 查询格式...")
        
        try:
            query = self.get_fixed_query(bbox, data_type)
            print(f"📋 查询语句: {query}")
            print("-" * 50)
            
            response = self.download_with_retry(self.base_url, {"data": query})
            
            if response and response.status_code == 200:
                print(f"✅ 测试成功: {len(response.content)} 字节")
                return True
            else:
                print(f"❌ 测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
    
    def download_region_data(self, region_name, bbox, data_types):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:  # 文件大小大于1KB
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            try:
                # 构建修复的查询
                query = self.get_fixed_query(bbox, data_type)
                print(f"  📋 查询语句: {query}")
                
                # 下载数据
                response = self.download_with_retry(self.base_url, {"data": query})
                
                if response and response.status_code == 200:
                    # 保存数据
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    file_size = file_path.stat().st_size
                    print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                    results[data_type] = "成功"
                    
                    # 更新进度
                    self.progress[f"{region_name}_{data_type}"] = "completed"
                    self.save_progress()
                    
                else:
                    print(f"  ❌ {data_type} 数据下载失败")
                    results[data_type] = "失败"
                    
                    # 更新进度
                    self.progress[f"{region_name}_{data_type}"] = "failed"
                    self.save_progress()
                    
            except Exception as e:
                print(f"  ❌ {data_type} 下载异常: {e}")
                results[data_type] = "异常"
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "error"
                self.save_progress()
        
        return results
    
    def download_provinces(self, provinces, data_types):
        """下载多个省份数据"""
        print(f"🚀 开始下载 {len(provinces)} 个省份的矢量数据")
        print(f"📊 数据类型: {', '.join(data_types)}")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_region = {}
            for region_name, bbox in provinces.items():
                future = executor.submit(self.download_region_data, region_name, bbox, data_types)
                future_to_region[future] = region_name
            
            # 处理结果
            for future in as_completed(future_to_region):
                region_name = future_to_region[future]
                try:
                    result = future.result()
                    results[region_name] = result
                except Exception as e:
                    print(f"❌ {region_name} 下载异常: {e}")
                    results[region_name] = {"error": str(e)}
        
        return results
    
    def get_download_status(self):
        """获取下载状态"""
        status = {
            "completed": 0,
            "failed": 0,
            "error": 0,
            "pending": 0,
            "details": {}
        }
        
        for key, value in self.progress.items():
            if value == "completed":
                status["completed"] += 1
            elif value == "failed":
                status["failed"] += 1
            elif value == "error":
                status["error"] += 1
            else:
                status["pending"] += 1
            
            status["details"][key] = value
        
        return status

def main():
    """主函数"""
    downloader = FixedQueryDownloader()
    
    # 测试用的省份配置（小范围）
    test_provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5"
    }
    
    # 完整省份配置
    provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5",
        "天津": "116.8,38.5,118.0,40.2",
        "重庆": "105.0,28.0,110.0,32.0",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0"
    }
    
    # 数据类型
    data_types = ["roads", "buildings", "pois"]
    
    print("🔧 修复查询格式下载器")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 测试查询格式 (推荐)")
        print("2. 下载测试省份数据")
        print("3. 下载完整省份数据")
        print("4. 查看下载状态")
        print("5. 清理进度文件")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            print("\n🧪 测试查询格式...")
            bbox = "116.0,39.4,117.0,40.2"
            
            for data_type in data_types:
                success = downloader.test_query_format(bbox, data_type)
                if success:
                    print(f"✅ {data_type} 查询格式测试成功")
                else:
                    print(f"❌ {data_type} 查询格式测试失败")
                print()
        
        elif choice == "2":
            print(f"\n🚀 开始下载测试省份数据...")
            results = downloader.download_provinces(test_provinces, data_types)
            
            print("\n📊 下载结果汇总:")
            for region, result in results.items():
                print(f"\n{region}:")
                for data_type, status in result.items():
                    if status == "成功":
                        print(f"  ✅ {data_type}: {status}")
                    elif status == "失败":
                        print(f"  ❌ {data_type}: {status}")
                    else:
                        print(f"  ⏭️ {data_type}: {status}")
        
        elif choice == "3":
            print(f"\n🚀 开始下载完整省份数据...")
            print("⚠️ 注意: 这将需要较长时间")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_provinces(provinces, data_types)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "4":
            status = downloader.get_download_status()
            print(f"\n📊 下载状态:")
            print(f"✅ 已完成: {status['completed']}")
            print(f"❌ 失败: {status['failed']}")
            print(f"⚠️ 异常: {status['error']}")
            print(f"⏳ 待处理: {status['pending']}")
            
            if status['details']:
                print(f"\n📋 详细信息:")
                for key, value in status['details'].items():
                    print(f"  {key}: {value}")
        
        elif choice == "5":
            if os.path.exists(downloader.progress_file):
                os.remove(downloader.progress_file)
                print("✅ 进度文件已清理")
            else:
                print("ℹ️ 进度文件不存在")
        
        elif choice == "6":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
