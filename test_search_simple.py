#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单搜索测试
"""

import requests
import json

def test_search():
    """测试搜索功能"""
    print("🔍 测试搜索功能")
    print("=" * 50)
    
    # 测试搜索API
    test_queries = ["四川", "九寨沟", "北京", "天安门"]
    
    for query in test_queries:
        print(f"\n🔍 搜索: {query}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ 找到 {len(results)} 个结果:")
                for i, result in enumerate(results[:3]):  # 只显示前3个
                    print(f"      {i+1}. {result['name']} ({result['type']}) - {result['region']}")
                if len(results) > 3:
                    print(f"      ... 还有 {len(results)-3} 个结果")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    # 测试快速搜索API
    print(f"\n🚀 测试快速搜索:")
    for query in test_queries:
        print(f"\n🔍 快速搜索: {query}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/quick-search?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ 找到 {len(results)} 个结果:")
                for i, result in enumerate(results[:3]):
                    print(f"      {i+1}. {result['name']} ({result['type']}) - {result['region']}")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    test_search()
