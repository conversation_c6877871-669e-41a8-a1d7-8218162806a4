<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>20级超高精度地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 0; font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #8E24AA 0%, #7B1FA2 100%); color: white; padding: 15px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { margin: 0; font-size: 24px; }
        .header .subtitle { margin: 5px 0 0 0; font-size: 14px; opacity: 0.9; }
        .container { display: flex; height: calc(100vh - 80px); }
        .sidebar { width: 350px; background: white; border-right: 1px solid #ddd; overflow-y: auto; padding: 20px; }
        .map-container { flex: 1; position: relative; }
        #map { width: 100%; height: 100%; }
        .control-panel { background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border-left: 4px solid #8E24AA; }
        .control-panel h3 { margin: 0 0 10px 0; color: #333; font-size: 16px; }
        .precision-info { background: #f3e5f5; border: 1px solid #8E24AA; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
        .precision-info h4 { margin: 0 0 10px 0; color: #7B1FA2; font-size: 16px; }
        .stat { display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 13px; }
        .stat-label { font-weight: bold; }
        .stat-value { color: #8E24AA; font-family: monospace; }
        .coordinates { font-family: 'Courier New', monospace; font-size: 12px; background: #f5f5f5; padding: 8px; border-radius: 3px; margin-top: 5px; border: 1px solid #ddd; }
        .btn { background: #8E24AA; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; font-size: 14px; width: 100%; margin-top: 10px; }
        .btn:hover { background: #7B1FA2; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 20级超高精度地图系统</h1>
        <div class="subtitle">亚米级精度 - 基于本地OSM数据的最高精度地图</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <div class="precision-info">
                <h4>🎯 超高精度规格</h4>
                <div class="stat"><span class="stat-label">20级像素精度:</span><span class="stat-value">~0.6米/像素</span></div>
                <div class="stat"><span class="stat-label">18级像素精度:</span><span class="stat-value">~2.4米/像素</span></div>
                <div class="stat"><span class="stat-label">坐标精度:</span><span class="stat-value">10位小数</span></div>
                <div class="stat"><span class="stat-label">定位精度:</span><span class="stat-value">亚米级</span></div>
                <div class="stat"><span class="stat-label">数据来源:</span><span class="stat-value">本地OSM</span></div>
            </div>
            
            <div class="control-panel">
                <h3>📊 瓦片状态</h3>
                <div id="tile-status">检查中...</div>
                <button class="btn" onclick="refreshTileStatus()">刷新状态</button>
            </div>
            
            <div class="control-panel">
                <h3>📍 当前位置信息</h3>
                <div id="current-coordinates" class="coordinates">点击地图获取坐标</div>
                <div id="zoom-level" class="coordinates">缩放级别: -</div>
                <div id="tile-info" class="coordinates">瓦片: -</div>
                <div id="precision-info" class="coordinates">精度: -</div>
            </div>
            
            <div class="control-panel">
                <h3>🎛️ 快速导航</h3>
                <button class="btn" onclick="gotoBeijing()">北京天安门</button>
                <button class="btn" onclick="gotoShanghai()">上海外滩</button>
                <button class="btn" onclick="gotoGuangzhou()">广州塔</button>
                <button class="btn" onclick="gotoShenzhen()">深圳中心</button>
            </div>
            
            <div class="control-panel">
                <h3>🔍 精度分析</h3>
                <div id="precision-analysis">移动地图查看精度信息</div>
                <button class="btn" onclick="analyzePrecision()">分析当前区域精度</button>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        const map = L.map('map', {
            center: [39.9042, 116.4074], // 北京天安门
            zoom: 18,
            maxZoom: 20,
            minZoom: 16
        });

        // 添加20级瓦片图层
        const level20TileLayer = L.tileLayer('/tiles/{z}/{x}/{y}.png', {
            attribution: '© 20级超高精度系统 | 基于本地OSM数据',
            maxZoom: 20,
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // 当前位置标记
        let currentMarker = null;

        // 地图事件
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(10);
            const lng = e.latlng.lng.toFixed(10);
            
            document.getElementById('current-coordinates').innerHTML = 
                `纬度: ${lat}<br>经度: ${lng}`;
            
            // 添加标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([lat, lng]).addTo(map)
                .bindPopup(`超高精度坐标:<br>纬度: ${lat}<br>经度: ${lng}`)
                .openPopup();
            
            // 分析精度
            analyzePrecision();
        });

        map.on('zoomend moveend', function() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            
            document.getElementById('zoom-level').innerHTML = `缩放级别: ${zoom}`;
            
            // 更新瓦片信息
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            document.getElementById('tile-info').innerHTML = 
                `瓦片: ${zoom}/${tileCoord.x}/${tileCoord.y}`;
            
            // 更新精度信息
            updatePrecisionInfo(zoom);
        });

        // 工具函数
        function getTileCoordinates(lat, lng, zoom) {
            const latRad = lat * Math.PI / 180;
            const n = Math.pow(2, zoom);
            const x = Math.floor((lng + 180) / 360 * n);
            const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
            return { x, y };
        }

        function updatePrecisionInfo(zoom) {
            let precision, level;
            
            if (zoom >= 20) {
                precision = "~0.6米/像素";
                level = "超高精度 (亚米级)";
            } else if (zoom >= 18) {
                precision = "~2.4米/像素";
                level = "高精度 (米级)";
            } else {
                precision = "~9.6米/像素";
                level = "中等精度";
            }
            
            document.getElementById('precision-info').innerHTML = 
                `精度: ${precision}<br>级别: ${level}`;
        }

        function refreshTileStatus() {
            document.getElementById('tile-status').innerHTML = '检查中...';
            
            fetch('/api/tile-status')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    
                    // 20级瓦片状态
                    const level20 = data.level20;
                    const status20 = level20.exists ? (level20.count > 0 ? 'status-good' : 'status-warning') : 'status-error';
                    html += `<div style="margin-bottom: 10px;">`;
                    html += `<div class="status-indicator ${status20}"></div>`;
                    html += `<strong>20级瓦片:</strong> ${level20.count} 个 (${level20.size_mb} MB)<br>`;
                    html += `<small>覆盖: ${level20.coverage_area}</small>`;
                    html += `</div>`;
                    
                    // 18级瓦片状态
                    const level18 = data.level18;
                    const status18 = level18.exists ? (level18.count > 0 ? 'status-good' : 'status-warning') : 'status-error';
                    html += `<div style="margin-bottom: 10px;">`;
                    html += `<div class="status-indicator ${status18}"></div>`;
                    html += `<strong>18级瓦片:</strong> ${level18.count} 个 (${level18.size_mb} MB)<br>`;
                    html += `<small>覆盖: ${level18.coverage_area}</small>`;
                    html += `</div>`;
                    
                    document.getElementById('tile-status').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('tile-status').innerHTML = '检查失败: ' + error.message;
                });
        }

        function analyzePrecision() {
            const zoom = map.getZoom();
            const center = map.getCenter();
            const tileCoord = getTileCoordinates(center.lat, center.lng, zoom);
            
            fetch(`/api/precision-info/${zoom}/${tileCoord.x}/${tileCoord.y}`)
                .then(response => response.json())
                .then(data => {
                    let html = `<div style="font-size: 12px;">`;
                    html += `<strong>当前瓦片精度分析:</strong><br>`;
                    html += `缩放级别: ${data.zoom_level}<br>`;
                    html += `瓦片尺寸: ${data.tile_size_meters.width.toFixed(1)}m × ${data.tile_size_meters.height.toFixed(1)}m<br>`;
                    html += `像素精度: ${data.pixel_precision_meters.average.toFixed(2)}m/像素<br>`;
                    html += `精度级别: ${data.precision_level}`;
                    html += `</div>`;
                    
                    document.getElementById('precision-analysis').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('precision-analysis').innerHTML = '分析失败: ' + error.message;
                });
        }

        // 快速导航函数
        function gotoBeijing() {
            map.setView([39.9042, 116.4074], 20);
        }

        function gotoShanghai() {
            map.setView([31.2304, 121.4737], 20);
        }

        function gotoGuangzhou() {
            map.setView([23.1291, 113.2644], 20);
        }

        function gotoShenzhen() {
            map.setView([22.5428, 114.0595], 20);
        }

        // 初始化
        updatePrecisionInfo(map.getZoom());
        refreshTileStatus();
        
        // 自动刷新状态
        setInterval(refreshTileStatus, 60000); // 每分钟刷新一次
    </script>
</body>
</html>