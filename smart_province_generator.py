#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能省份瓦片生成器
根据实际需要生成特定区域和精度的瓦片，提高成功率和效率
"""

import json
from pathlib import Path
from PIL import Image, ImageDraw
import math
from tqdm import tqdm
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import argparse

# 各省份/直辖市的边界框 [min_lon, min_lat, max_lon, max_lat]
PROVINCE_BBOXES = {
    "北京": [115.7, 39.4, 117.4, 41.6],
    "上海": [120.9, 30.7, 122.1, 31.9],
    "天津": [116.7, 38.5, 118.1, 40.2],
    "重庆": [105.3, 28.2, 110.2, 32.2],
    "河北": [113.3, 36.0, 119.9, 42.6],
    "山西": [110.2, 34.6, 114.6, 40.7],
    "辽宁": [118.8, 38.7, 125.8, 43.5],
    "吉林": [121.3, 40.9, 131.3, 46.2],
    "黑龙江": [121.1, 43.4, 135.1, 53.6],
    "江苏": [116.2, 30.7, 121.9, 35.1],
    "浙江": [118.0, 27.1, 123.0, 31.2],
    "安徽": [114.9, 29.4, 119.6, 34.6],
    "福建": [115.8, 23.5, 120.7, 28.3],
    "江西": [113.6, 24.5, 118.5, 30.0],
    "山东": [114.8, 34.4, 122.7, 38.3],
    "河南": [110.3, 31.4, 116.6, 36.4],
    "湖北": [108.4, 29.0, 116.1, 33.2],
    "湖南": [108.8, 24.6, 114.2, 30.1],
    "广东": [109.7, 20.1, 117.3, 25.5],
    "海南": [108.6, 18.2, 111.1, 20.2],
    "四川": [102.1, 26.0, 108.6, 34.2],
    "贵州": [103.6, 24.6, 109.4, 29.2],
    "云南": [97.5, 21.5, 106.0, 29.2],
    "陕西": [105.5, 31.7, 111.2, 39.6],
    "甘肃": [92.1, 32.7, 103.4, 42.8],
    "青海": [89.4, 31.6, 102.4, 39.8],
    "台湾": [120.0, 21.9, 122.0, 25.3],
    "内蒙古": [97.2, 37.4, 126.0, 53.3],
    "广西": [104.5, 20.9, 112.0, 26.4],
    "西藏": [78.4, 26.9, 99.1, 36.1],
    "宁夏": [104.2, 35.2, 107.4, 39.3],
    "新疆": [73.5, 34.3, 96.3, 49.2],
    "香港": [113.8, 22.1, 114.5, 22.6],
    "澳门": [113.5, 22.1, 113.6, 22.2]
}

class SmartProvinceGenerator:
    def __init__(self, province, min_zoom=8, max_zoom=18, center_lat=None, center_lon=None, radius_km=50):
        self.province = province
        self.province_bbox = PROVINCE_BBOXES.get(province)
        if not self.province_bbox:
            raise ValueError(f"不支持的省份: {province}")
            
        self.min_zoom = min_zoom
        self.max_zoom = max_zoom
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 如果指定了中心点和半径，则使用该区域而不是整个省份
        if center_lat is not None and center_lon is not None:
            self.center_lat = center_lat
            self.center_lon = center_lon
            self.radius_km = radius_km
            # 计算指定区域的边界框
            self.area_bbox = self._calculate_area_bbox(center_lat, center_lon, radius_km)
        else:
            # 使用整个省份
            self.center_lon = (self.province_bbox[0] + self.province_bbox[2]) / 2
            self.center_lat = (self.province_bbox[1] + self.province_bbox[3]) / 2
            self.radius_km = self._calculate_province_radius()
            self.area_bbox = self.province_bbox
    
    def _calculate_area_bbox(self, center_lat, center_lon, radius_km):
        """计算指定中心点和半径的边界框"""
        # 近似计算
        lat_delta = radius_km / 111.0
        lon_delta = radius_km / (111.0 * math.cos(math.radians(center_lat)))
        
        south = center_lat - lat_delta
        north = center_lat + lat_delta
        west = center_lon - lon_delta
        east = center_lon + lon_delta
        
        return [west, south, east, north]
    
    def _calculate_province_radius(self):
        """计算省份的近似半径"""
        # 计算省份对角线长度的一半作为半径
        width_km = (self.province_bbox[2] - self.province_bbox[0]) * 111.0
        height_km = (self.province_bbox[3] - self.province_bbox[1]) * 111.0
        diagonal_km = math.sqrt(width_km**2 + height_km**2)
        return diagonal_km / 2
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片的地理边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def should_process_tile(self, tile_bbox):
        """检查是否应该处理这个瓦片"""
        # 检查瓦片边界框是否与区域边界框相交
        tile_min_lon, tile_min_lat, tile_max_lon, tile_max_lat = tile_bbox
        area_min_lon, area_min_lat, area_max_lon, area_max_lat = self.area_bbox
        
        # 检查两个矩形是否相交
        return not (tile_max_lon < area_min_lon or 
                   tile_min_lon > area_max_lon or 
                   tile_max_lat < area_min_lat or 
                   tile_min_lat > area_max_lat)
    
    def create_realistic_tile(self, z, x, y):
        """创建更真实的瓦片（包含一些基本特征）"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 如果文件已存在，直接返回
        if tile_file.exists():
            return True
        
        # 创建256x256的瓦片图像
        img = Image.new('RGB', (256, 256), color='#f8f8f8')
        draw = ImageDraw.Draw(img)
        
        # 检查是否应该处理这个瓦片
        tile_bbox = self.get_tile_bbox(x, y, z)
        if not self.should_process_tile(tile_bbox):
            # 创建浅色占位瓦片
            img = Image.new('RGB', (256, 256), color='#f0f8ff')
            img.save(tile_file, 'PNG')
            return True
        
        # 绘制网格（模拟道路）
        grid_color = '#e0e0e0'
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill=grid_color, width=1)
            draw.line([(0, i), (256, i)], fill=grid_color, width=1)
        
        # 根据缩放级别添加不同特征
        if z >= 12:
            # 模拟主要道路
            draw.line([(0, 128), (256, 128)], fill='#ffcc00', width=3)
            draw.line([(128, 0), (128, 256)], fill='#ffcc00', width=3)
        
        if z >= 14:
            # 模拟建筑物
            building_color = '#dddddd'
            building_outline = '#999999'
            draw.rectangle([50, 50, 90, 90], fill=building_color, outline=building_outline)
            draw.rectangle([150, 150, 190, 190], fill=building_color, outline=building_outline)
            draw.rectangle([70, 170, 110, 210], fill=building_color, outline=building_outline)
        
        if z >= 16:
            # 模拟更详细的特征
            draw.ellipse([100, 100, 120, 120], fill='#87ceeb', outline='#4682b4')  # 模拟水体
            draw.polygon([(200, 200), (220, 180), (240, 200)], fill='#90ee90', outline='#006400')  # 模拟绿地
        
        # 添加瓦片信息
        try:
            text = f"Z{z} X{x} Y{y}"
            # 简单的文本绘制（避免字体问题）
            draw.text((10, 10), text, fill='#666666')
        except:
            pass  # 忽略字体错误
        
        img.save(tile_file, 'PNG')
        return True
    
    def generate_tiles(self, workers=4):
        """生成瓦片"""
        print(f"开始生成 {self.province} 的离线地图瓦片...")
        if hasattr(self, 'center_lat'):
            print(f"区域中心: {self.center_lat:.4f}, {self.center_lon:.4f}")
            print(f"覆盖半径: {self.radius_km} 公里")
        else:
            print(f"覆盖整个省份")
        print(f"缩放级别: {self.min_zoom}-{self.max_zoom}")
        print(f"区域边界: {self.area_bbox}")
        
        total_tiles = 0
        generated_tiles = 0
        
        # 按缩放级别生成瓦片
        for zoom in range(self.min_zoom, self.max_zoom + 1):
            print(f"\n生成缩放级别 {zoom} 的瓦片...")
            
            # 计算该缩放级别下区域的瓦片范围
            min_x, max_y = self.deg2num(self.area_bbox[1], self.area_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.area_bbox[3], self.area_bbox[2], zoom)
            
            # 扩展范围以确保覆盖完整
            padding = 1 if zoom < 15 else 2
            min_x -= padding
            max_x += padding
            min_y -= padding
            max_y += padding
            
            level_tiles = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    level_tiles.append((x, y, zoom))
            
            level_total = len(level_tiles)
            total_tiles += level_total
            
            print(f"  需要生成 {level_total} 个瓦片 (X: {min_x}-{max_x}, Y: {min_y}-{max_y})")
            
            # 使用线程池并行生成瓦片
            success_count = 0
            with ThreadPoolExecutor(max_workers=workers) as executor:
                # 提交任务
                future_to_tile = {
                    executor.submit(self.create_realistic_tile, z, x, y): (x, y, z)
                    for x, y, z in level_tiles
                }
                
                # 收集结果
                with tqdm(total=level_total, desc=f"  缩放级别 {zoom}", unit="tile") as pbar:
                    for future in concurrent.futures.as_completed(future_to_tile):
                        x, y, z = future_to_tile[future]
                        try:
                            if future.result():
                                success_count += 1
                        except Exception as e:
                            print(f"    生成瓦片 {z}/{x}/{y} 失败: {e}")
                        pbar.update(1)
            
            generated_tiles += success_count
            print(f"  缩放级别 {zoom} 完成: {success_count}/{level_total} 个瓦片")
        
        print(f"\n=== 生成完成 ===")
        print(f"总计需要生成: {total_tiles} 个瓦片")
        print(f"成功生成: {generated_tiles} 个瓦片")
        if total_tiles > 0:
            success_rate = generated_tiles / total_tiles * 100
            print(f"成功率: {success_rate:.1f}%")
        
        return generated_tiles == total_tiles

def list_provinces():
    """列出所有支持的省份"""
    print("支持的省份和直辖市:")
    provinces = list(PROVINCE_BBOXES.keys())
    for i, province in enumerate(provinces, 1):
        print(f"  {i:2d}. {province}")

def main():
    parser = argparse.ArgumentParser(description="智能省份瓦片生成器")
    parser.add_argument("province", nargs="?", help="省份名称 (例如: 北京)")
    parser.add_argument("--min-zoom", type=int, default=12, help="最小缩放级别 (默认: 12)")
    parser.add_argument("--max-zoom", type=int, default=16, help="最大缩放级别 (默认: 16)")
    parser.add_argument("--center-lat", type=float, help="中心纬度")
    parser.add_argument("--center-lon", type=float, help="中心经度")
    parser.add_argument("--radius", type=float, default=30, help="覆盖半径(公里) (默认: 30)")
    parser.add_argument("--workers", type=int, default=4, help="并行工作线程数 (默认: 4)")
    parser.add_argument("--list", action="store_true", help="列出所有支持的省份")
    
    args = parser.parse_args()
    
    if args.list:
        list_provinces()
        return
    
    if not args.province:
        print("请指定省份名称，或使用 --list 查看支持的省份")
        list_provinces()
        return
    
    if args.province not in PROVINCE_BBOXES:
        print(f"不支持的省份: {args.province}")
        list_provinces()
        return
    
    try:
        generator = SmartProvinceGenerator(
            args.province, 
            args.min_zoom, 
            args.max_zoom,
            args.center_lat,
            args.center_lon,
            args.radius
        )
        success = generator.generate_tiles(args.workers)
        
        if success:
            print(f"\n🎉 {args.province} 地图瓦片生成成功!")
            print(f"瓦片保存在: {generator.tiles_dir}")
        else:
            print(f"\n❌ {args.province} 地图瓦片生成失败!")
            
    except Exception as e:
        print(f"生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()