#!/usr/bin/env python3
"""
手动创建一些20级测试瓦片
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance

def create_test_tiles():
    """创建测试瓦片"""
    base_dir = Path(__file__).parent
    level18_dir = base_dir / "static" / "tiles" / "18"
    level20_dir = base_dir / "static" / "tiles" / "20"
    
    # 确保20级目录存在
    level20_dir.mkdir(parents=True, exist_ok=True)
    
    # 选择一个18级瓦片进行测试
    source_path = level18_dir / "215828" / "99332.png"
    
    if not source_path.exists():
        print(f"❌ 源瓦片不存在: {source_path}")
        return False
    
    print(f"📖 读取源瓦片: {source_path}")
    source_img = Image.open(source_path)
    
    # 18级瓦片 215828/99332 对应的20级瓦片坐标
    x18, y18 = 215828, 99332
    scale_factor = 4  # 2^(20-18) = 4
    
    created_count = 0
    
    for dx in range(scale_factor):
        for dy in range(scale_factor):
            x20 = x18 * scale_factor + dx
            y20 = y18 * scale_factor + dy
            
            # 创建目录
            tile_dir = level20_dir / str(x20)
            tile_dir.mkdir(parents=True, exist_ok=True)
            tile_file = tile_dir / f"{y20}.png"
            
            # 从18级瓦片中提取对应区域
            crop_size = 256 // scale_factor  # = 64
            left = dx * crop_size
            top = dy * crop_size
            right = left + crop_size
            bottom = top + crop_size
            
            # 裁剪并放大
            cropped = source_img.crop((left, top, right, bottom))
            
            # 放大到512x512 (20级标准尺寸)
            enhanced = cropped.resize((512, 512), Image.LANCZOS)
            
            # 应用轻微的锐化滤镜
            enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=120, threshold=3))
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(enhanced)
            enhanced = enhancer.enhance(1.1)
            
            # 保存瓦片
            enhanced.save(tile_file, 'PNG', optimize=True)
            created_count += 1
            
            print(f"✅ 创建瓦片: 20/{x20}/{y20}.png")
    
    print(f"\n🎉 成功创建 {created_count} 个20级测试瓦片")
    return True

if __name__ == "__main__":
    print("🧪 创建20级测试瓦片")
    print("=" * 40)
    
    success = create_test_tiles()
    
    if success:
        print("\n✅ 测试瓦片创建完成！")
        print("🌐 现在可以在地图中测试20级瓦片了")
    else:
        print("\n❌ 测试瓦片创建失败")
