#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全国矢量数据下载工具
支持下载全国各省市的矢量地图数据
"""

import os
import requests
import json
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class NationalVectorDownloader:
    def __init__(self, output_dir="static/vector_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 全国省市级区域配置
        self.provinces = {
            '1': {'name': '北京市', 'bbox': (115.7, 39.4, 117.4, 41.6)},
            '2': {'name': '上海市', 'bbox': (120.9, 30.7, 122.1, 31.9)},
            '3': {'name': '天津市', 'bbox': (116.7, 38.6, 118.1, 40.2)},
            '4': {'name': '重庆市', 'bbox': (105.3, 28.2, 110.2, 32.2)},
            '5': {'name': '广东省', 'bbox': (109.7, 20.2, 117.2, 25.5)},
            '6': {'name': '江苏省', 'bbox': (116.2, 30.7, 121.9, 35.2)},
            '7': {'name': '浙江省', 'bbox': (118.0, 27.0, 123.0, 31.5)},
            '8': {'name': '山东省', 'bbox': (114.5, 34.4, 122.7, 38.4)},
            '9': {'name': '河南省', 'bbox': (110.4, 31.2, 116.7, 36.4)},
            '10': {'name': '四川省', 'bbox': (97.3, 26.0, 108.5, 34.3)},
            '11': {'name': '湖北省', 'bbox': (108.3, 29.0, 116.1, 33.3)},
            '12': {'name': '湖南省', 'bbox': (108.8, 24.6, 114.3, 30.1)},
            '13': {'name': '河北省', 'bbox': (113.5, 36.0, 119.8, 42.6)},
            '14': {'name': '福建省', 'bbox': (115.8, 23.5, 120.4, 28.3)},
            '15': {'name': '安徽省', 'bbox': (114.9, 29.4, 119.3, 34.7)},
            '16': {'name': '辽宁省', 'bbox': (118.3, 38.7, 125.3, 43.3)},
            '17': {'name': '江西省', 'bbox': (113.6, 24.5, 118.5, 30.0)},
            '18': {'name': '黑龙江省', 'bbox': (121.1, 43.4, 135.1, 53.6)},
            '19': {'name': '吉林省', 'bbox': (121.6, 40.9, 131.2, 46.3)},
            '20': {'name': '山西省', 'bbox': (110.2, 34.6, 114.6, 40.7)},
            '21': {'name': '陕西省', 'bbox': (105.5, 31.7, 111.3, 39.6)},
            '22': {'name': '甘肃省', 'bbox': (92.1, 32.1, 108.7, 42.8)},
            '23': {'name': '青海省', 'bbox': (89.4, 31.6, 103.1, 39.2)},
            '24': {'name': '云南省', 'bbox': (97.5, 21.1, 106.2, 29.2)},
            '25': {'name': '贵州省', 'bbox': (103.6, 24.6, 109.6, 29.2)},
            '26': {'name': '海南省', 'bbox': (108.6, 18.2, 111.1, 20.1)},
            '27': {'name': '台湾省', 'bbox': (119.3, 21.9, 122.0, 25.3)},
            '28': {'name': '内蒙古自治区', 'bbox': (97.2, 37.2, 126.0, 53.3)},
            '29': {'name': '新疆维吾尔自治区', 'bbox': (73.4, 34.3, 96.4, 48.9)},
            '30': {'name': '西藏自治区', 'bbox': (78.4, 27.4, 99.1, 36.5)},
            '31': {'name': '宁夏回族自治区', 'bbox': (104.2, 35.2, 107.6, 39.4)},
            '32': {'name': '广西壮族自治区', 'bbox': (104.3, 20.9, 112.0, 26.2)},
            '33': {'name': '香港特别行政区', 'bbox': (113.8, 22.2, 114.4, 22.6)},
            '34': {'name': '澳门特别行政区', 'bbox': (113.5, 22.1, 113.6, 22.2)}
        }
        
        # 主要城市配置
        self.major_cities = {
            '1': {'name': '北京市', 'bbox': (116.0, 39.4, 117.0, 40.2)},
            '2': {'name': '上海市', 'bbox': (121.2, 31.0, 121.8, 31.4)},
            '3': {'name': '广州市', 'bbox': (113.0, 22.7, 113.6, 23.4)},
            '4': {'name': '深圳市', 'bbox': (113.7, 22.4, 114.4, 22.8)},
            '5': {'name': '杭州市', 'bbox': (119.8, 30.0, 120.5, 30.5)},
            '6': {'name': '南京市', 'bbox': (118.4, 31.8, 119.2, 32.3)},
            '7': {'name': '成都市', 'bbox': (103.8, 30.4, 104.3, 30.8)},
            '8': {'name': '武汉市', 'bbox': (114.0, 30.4, 114.6, 30.8)},
            '9': {'name': '西安市', 'bbox': (108.7, 34.0, 109.2, 34.4)},
            '10': {'name': '重庆市', 'bbox': (106.2, 29.2, 107.0, 29.8)},
            '11': {'name': '天津市', 'bbox': (117.0, 38.8, 117.8, 39.4)},
            '12': {'name': '青岛市', 'bbox': (120.1, 35.9, 120.6, 36.3)},
            '13': {'name': '大连市', 'bbox': (121.3, 38.7, 121.9, 39.1)},
            '14': {'name': '厦门市', 'bbox': (117.9, 24.4, 118.3, 24.6)},
            '15': {'name': '苏州市', 'bbox': (120.4, 31.1, 121.0, 31.5)},
            '16': {'name': '无锡市', 'bbox': (120.1, 31.4, 120.6, 31.7)},
            '17': {'name': '宁波市', 'bbox': (121.3, 29.7, 121.9, 30.0)},
            '18': {'name': '长沙市', 'bbox': (112.7, 28.0, 113.2, 28.4)},
            '19': {'name': '郑州市', 'bbox': (113.4, 34.6, 114.0, 34.9)},
            '20': {'name': '沈阳市', 'bbox': (123.2, 41.6, 123.8, 42.0)}
        }
        
        # Overpass API配置
        self.overpass_url = 'https://overpass-api.de/api/interpreter'
        
        # 下载统计
        self.stats = {
            'total_regions': 0,
            'completed_regions': 0,
            'failed_regions': 0,
            'total_files': 0,
            'completed_files': 0,
            'failed_files': 0
        }
    
    def download_roads_data(self, bbox, output_file):
        """下载高精度道路数据"""
        min_lon, min_lat, max_lon, max_lat = bbox
        
        query = f"""
        [out:xml][timeout:120];
        (
          way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["highway"~"^(motorway_link|trunk_link|primary_link|secondary_link|tertiary_link)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "道路数据")
    
    def download_buildings_data(self, bbox, output_file):
        """下载高精度建筑物数据"""
        min_lon, min_lat, max_lon, max_lat = bbox
        
        query = f"""
        [out:xml][timeout:120];
        (
          way["building"]({min_lat},{min_lon},{max_lat},{max_lon});
          relation["building"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["building:part"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["amenity"~"^(school|hospital|university|college|library|museum|theatre|cinema|restaurant|hotel|bank|post_office|police|fire_station|townhall)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["shop"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["office"]({min_lat},{min_lon},{max_lat},{max_lon});
          way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool|golf_course)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "建筑物数据")
    
    def download_poi_data(self, bbox, output_file):
        """下载高精度POI数据"""
        min_lon, min_lat, max_lon, max_lat = bbox
        
        query = f"""
        [out:xml][timeout:120];
        (
          node["amenity"~"^(restaurant|fast_food|cafe|bar|pub|hospital|clinic|pharmacy|school|university|college|library|museum|theatre|cinema|bank|atm|post_office|police|fire_station|townhall|embassy|place_of_worship|fuel|parking|toilets|drinking_water|fountain|bench|waste_basket|recycling|vending_machine)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["shop"~"^(supermarket|convenience|bakery|butcher|clothes|shoes|electronics|furniture|hardware|books|gift|jewelry|beauty|hairdresser|car|bicycle|motorcycle|mobile_phone|computer|optician|florist|toy|sports|outdoor|pet|art|craft|stationery|newsagent|tobacco|alcohol|seafood|organic|frozen_food|confectionery|tea|coffee|wine|cheese|deli|farm)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["tourism"~"^(hotel|motel|hostel|guest_house|apartment|camp_site|caravan_site|attraction|museum|gallery|zoo|theme_park|aquarium|information|artwork|viewpoint|picnic_site)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["leisure"~"^(park|garden|playground|sports_centre|swimming_pool|golf_course|stadium|pitch|track|fitness_centre|bowling_alley|dance|hackerspace|ice_rink|marina|miniature_golf|outdoor_seating|park|pitch|sports_centre|stadium|swimming_pool|water_park)$"]({min_lat},{min_lon},{max_lat},{max_lon});
          node["office"~"^(company|corporation|ngo|political_party|religion|research|tax_advisor|travel_agent|estate_agent|insurance|lawyer|accountant|government|diplomatic|educational|financial|it|telecommunication|consulting|advertising|architect|notary|quango)$"]({min_lat},{min_lon},{max_lat},{max_lon});
        );
        out geom;
        """
        
        return self._execute_query(query, output_file, "POI数据")
    
    def _execute_query(self, query, output_file, data_type):
        """执行Overpass查询"""
        try:
            response = requests.post(
                self.overpass_url,
                data=query,
                timeout=120,
                headers={'User-Agent': 'NationalVectorDownloader/1.0'},
                stream=True
            )
            response.raise_for_status()
            
            # 流式保存数据
            total_size = 0
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        total_size += len(chunk)
            
            return True, f"{data_type}下载成功 ({total_size:,} bytes)"
            
        except requests.exceptions.Timeout:
            return False, f"{data_type}下载超时"
        except Exception as e:
            return False, f"{data_type}下载失败: {e}"
    
    def download_region_data(self, region_name, bbox, data_types=None):
        """下载区域数据"""
        if data_types is None:
            data_types = ['roads', 'buildings', 'poi']
        
        print(f"🗺️  下载{region_name}矢量数据")
        
        # 创建区域目录
        region_dir = self.output_dir / region_name
        region_dir.mkdir(parents=True, exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            if data_type == 'roads':
                output_file = region_dir / f"{region_name}_roads.xml"
                success, message = self.download_roads_data(bbox, output_file)
            elif data_type == 'buildings':
                output_file = region_dir / f"{region_name}_buildings.xml"
                success, message = self.download_buildings_data(bbox, output_file)
            elif data_type == 'poi':
                output_file = region_dir / f"{region_name}_poi.xml"
                success, message = self.download_poi_data(bbox, output_file)
            else:
                continue
            
            results[data_type] = (success, message)
            self.stats['total_files'] += 1
            
            if success:
                print(f"   ✅ {message}")
                self.stats['completed_files'] += 1
            else:
                print(f"   ❌ {message}")
                self.stats['failed_files'] += 1
            
            # 避免请求过快
            time.sleep(3)
        
        return results
    
    def download_all_provinces(self, data_types=None, max_workers=8):
        """下载全国所有省份数据"""
        if data_types is None:
            data_types = ['roads', 'buildings', 'poi']
        
        print(f"🗺️  开始下载全国矢量数据")
        print(f"   数据类型: {', '.join(data_types)}")
        print(f"   并发数: {max_workers}")
        print(f"   总省份数: {len(self.provinces)}")
        
        self.stats['total_regions'] = len(self.provinces)
        
        # 使用线程池并发下载
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for key, province in self.provinces.items():
                future = executor.submit(
                    self.download_region_data,
                    province['name'],
                    province['bbox'],
                    data_types
                )
                futures.append((key, future))
            
            # 等待所有任务完成
            for key, future in futures:
                try:
                    results = future.result()
                    self.stats['completed_regions'] += 1
                    print(f"✅ {self.provinces[key]['name']} 下载完成")
                except Exception as e:
                    self.stats['failed_regions'] += 1
                    print(f"❌ {self.provinces[key]['name']} 下载失败: {e}")
        
        # 打印最终统计
        self._print_final_stats()
    
    def download_major_cities(self, data_types=None, max_workers=8):
        """下载主要城市数据"""
        if data_types is None:
            data_types = ['roads', 'buildings', 'poi']
        
        print(f"🗺️  开始下载主要城市矢量数据")
        print(f"   数据类型: {', '.join(data_types)}")
        print(f"   并发数: {max_workers}")
        print(f"   总城市数: {len(self.major_cities)}")
        
        self.stats['total_regions'] = len(self.major_cities)
        
        # 使用线程池并发下载
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for key, city in self.major_cities.items():
                future = executor.submit(
                    self.download_region_data,
                    city['name'],
                    city['bbox'],
                    data_types
                )
                futures.append((key, future))
            
            # 等待所有任务完成
            for key, future in futures:
                try:
                    results = future.result()
                    self.stats['completed_regions'] += 1
                    print(f"✅ {self.major_cities[key]['name']} 下载完成")
                except Exception as e:
                    self.stats['failed_regions'] += 1
                    print(f"❌ {self.major_cities[key]['name']} 下载失败: {e}")
        
        # 打印最终统计
        self._print_final_stats()
    
    def _print_final_stats(self):
        """打印最终统计信息"""
        print(f"\n📊 下载统计:")
        print(f"   总区域数: {self.stats['total_regions']}")
        print(f"   完成区域: {self.stats['completed_regions']}")
        print(f"   失败区域: {self.stats['failed_regions']}")
        print(f"   总文件数: {self.stats['total_files']}")
        print(f"   完成文件: {self.stats['completed_files']}")
        print(f"   失败文件: {self.stats['failed_files']}")
        
        if self.stats['total_regions'] > 0:
            success_rate = self.stats['completed_regions'] / self.stats['total_regions'] * 100
            print(f"   成功率: {success_rate:.1f}%")
    
    def show_provinces(self):
        """显示省份列表"""
        print("\n📋 全国省份/直辖市:")
        print("-" * 50)
        
        for key, province in self.provinces.items():
            print(f"{key:2}. {province['name']}")
        
        print("0. 返回主菜单")
    
    def show_cities(self):
        """显示城市列表"""
        print("\n📋 主要城市:")
        print("-" * 50)
        
        for key, city in self.major_cities.items():
            print(f"{key:2}. {city['name']}")
        
        print("0. 返回主菜单")

def main():
    """主函数"""
    print("🗺️  全国矢量数据下载工具")
    print("=" * 60)
    
    downloader = NationalVectorDownloader()
    
    while True:
        print("\n" + "="*60)
        print("🗺️  全国矢量数据下载工具")
        print("="*60)
        print("1. 下载全国所有省份数据")
        print("2. 下载主要城市数据")
        print("3. 下载指定省份数据")
        print("4. 下载指定城市数据")
        print("5. 查看数据统计")
        print("0. 退出")
        print("="*60)
        
        choice = input("请选择功能 (0-5): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            # 下载全国所有省份数据
            print("\n⚠️  警告: 下载全国数据需要很长时间和大量存储空间")
            confirm = input("是否继续? (y/N): ").strip().lower()
            if confirm == 'y':
                data_types = ['roads', 'buildings', 'poi']
                downloader.download_all_provinces(data_types, max_workers=8)
            else:
                print("已取消下载")
        elif choice == '2':
            # 下载主要城市数据
            data_types = ['roads', 'buildings', 'poi']
            downloader.download_major_cities(data_types, max_workers=8)
        elif choice == '3':
            # 下载指定省份数据
            downloader.show_provinces()
            province_choice = input("\n请选择省份 (输入编号): ").strip()
            
            if province_choice == '0':
                continue
            
            if province_choice not in downloader.provinces:
                print("❌ 无效选择")
                continue
            
            province = downloader.provinces[province_choice]
            data_types = ['roads', 'buildings', 'poi']
            downloader.download_region_data(province['name'], province['bbox'], data_types)
        elif choice == '4':
            # 下载指定城市数据
            downloader.show_cities()
            city_choice = input("\n请选择城市 (输入编号): ").strip()
            
            if city_choice == '0':
                continue
            
            if city_choice not in downloader.major_cities:
                print("❌ 无效选择")
                continue
            
            city = downloader.major_cities[city_choice]
            data_types = ['roads', 'buildings', 'poi']
            downloader.download_region_data(city['name'], city['bbox'], data_types)
        elif choice == '5':
            # 查看数据统计
            vector_dir = downloader.output_dir
            if vector_dir.exists():
                regions = [d for d in vector_dir.iterdir() if d.is_dir()]
                print(f"\n📊 数据统计:")
                print(f"   已下载区域: {len(regions)}")
                for region in regions:
                    files = list(region.glob("*.xml"))
                    total_size = sum(f.stat().st_size for f in files)
                    print(f"   - {region.name}: {len(files)} 个文件, {total_size:,} bytes")
            else:
                print("❌ 数据目录不存在")
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")
    
    print(f"\n✅ 完成! 数据保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
