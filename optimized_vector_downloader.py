#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版矢量数据下载器 - 解决复杂查询和格式问题
基于诊断结果优化查询策略
"""

import os
import time
import json
import math
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import xml.etree.ElementTree as ET
import threading

class OptimizedVectorDownloader:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.timeout = 30  # 减少超时时间，避免复杂查询
        self.max_retries = 3
        self.retry_delay = 5
        self.max_workers = 3  # 减少并发数，避免服务器压力
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建进度记录文件
        self.progress_file = "optimized_download_progress.json"
        self.load_progress()
        
        print(f"🚀 优化版下载器初始化完成")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"📋 查询策略: 简化查询，分批下载")
    
    def load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                self.progress = json.load(f)
        else:
            self.progress = {}
    
    def save_progress(self):
        """保存下载进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def validate_and_fix_bbox(self, bbox):
        """验证并修复边界框格式"""
        try:
            if isinstance(bbox, str):
                # 清理字符串格式
                bbox = bbox.strip()
                # 移除多余的逗号
                while bbox.endswith(','):
                    bbox = bbox[:-1]
                
                parts = bbox.split(',')
                if len(parts) != 4:
                    raise ValueError(f"边界框必须有4个坐标，当前有{len(parts)}个")
                
                coords = [float(x.strip()) for x in parts]
            else:
                coords = bbox
            
            # 检查坐标范围
            if not (-180 <= coords[0] <= 180 and -180 <= coords[2] <= 180):
                raise ValueError("经度必须在-180到180之间")
            if not (-90 <= coords[1] <= 90 and -90 <= coords[3] <= 90):
                raise ValueError("纬度必须在-90到90之间")
            
            # 检查边界框大小，限制在合理范围内
            width = abs(coords[2] - coords[0])
            height = abs(coords[3] - coords[1])
            
            if width > 2.0 or height > 2.0:
                print(f"⚠️ 边界框较大: {width:.2f} x {height:.2f}度，将自动分割")
                return self.split_large_bbox(coords)
            
            # 返回标准格式
            return f"{coords[0]:.6f},{coords[1]:.6f},{coords[2]:.6f},{coords[3]:.6f}"
            
        except Exception as e:
            print(f"❌ 边界框验证失败: {e}")
            return None
    
    def split_large_bbox(self, coords):
        """分割大边界框"""
        min_lon, min_lat, max_lon, max_lat = coords
        
        # 计算分割数量
        width = max_lon - min_lon
        height = max_lat - min_lat
        
        lon_splits = max(1, int(math.ceil(width / 1.0)))
        lat_splits = max(1, int(math.ceil(height / 1.0)))
        
        bboxes = []
        for i in range(lon_splits):
            for j in range(lat_splits):
                sub_min_lon = min_lon + (width / lon_splits) * i
                sub_max_lon = min_lon + (width / lon_splits) * (i + 1)
                sub_min_lat = min_lat + (height / lat_splits) * j
                sub_max_lat = min_lat + (height / lat_splits) * (j + 1)
                
                bbox_str = f"{sub_min_lon:.6f},{sub_min_lat:.6f},{sub_max_lon:.6f},{sub_max_lat:.6f}"
                bboxes.append(bbox_str)
        
        print(f"📊 边界框分割为 {len(bboxes)} 个子区域")
        return bboxes
    
    def get_simple_overpass_query(self, bbox, data_type):
        """构建简化的Overpass查询语句"""
        if data_type == "roads":
            return f"""
[out:xml][timeout:30];
(
  way["highway"~"^(primary|secondary|tertiary|residential|service)"]({bbox});
);
out geom;
"""
        elif data_type == "buildings":
            return f"""
[out:xml][timeout:30];
(
  way["building"]({bbox});
  way["amenity"~"^(school|hospital|restaurant|hotel|bank)"]({bbox});
);
out geom;
"""
        elif data_type == "pois":
            return f"""
[out:xml][timeout:30];
(
  node["amenity"]({bbox});
  node["shop"]({bbox});
  node["tourism"]({bbox});
);
out;
"""
        else:
            return f"""
[out:xml][timeout:30];
(
  way["highway"]({bbox});
  way["building"]({bbox});
);
out geom;
"""
    
    def download_with_retry(self, url, data, max_retries=None):
        """带重试机制的下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt + 1}次)...")
                
                response = requests.post(
                    url, 
                    data=data, 
                    timeout=self.timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/xml, text/xml, */*',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 400:
                    print(f"  HTTP 400错误: 请求格式错误")
                    # 对于400错误，不重试
                    return None
                elif response.status_code == 429:
                    print(f"  HTTP 429错误: 请求过于频繁")
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"  等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                print(f"  等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        return None
    
    def download_region_data(self, region_name, bbox, data_types):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:  # 文件大小大于1KB
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            try:
                # 验证并修复边界框
                fixed_bbox = self.validate_and_fix_bbox(bbox)
                if not fixed_bbox:
                    print(f"  ❌ {data_type} 边界框格式错误")
                    results[data_type] = "边界框错误"
                    continue
                
                # 如果边界框被分割，需要合并多个查询结果
                if isinstance(fixed_bbox, list):
                    print(f"  📊 边界框已分割为 {len(fixed_bbox)} 个子区域")
                    success_count = 0
                    all_content = []
                    
                    for i, sub_bbox in enumerate(fixed_bbox):
                        print(f"  📥 下载子区域 {i+1}/{len(fixed_bbox)}...")
                        
                        query = self.get_simple_overpass_query(sub_bbox, data_type)
                        response = self.download_with_retry(self.base_url, {"data": query})
                        
                        if response and response.status_code == 200:
                            all_content.append(response.text)
                            success_count += 1
                        else:
                            print(f"  ⚠️ 子区域 {i+1} 下载失败")
                    
                    if success_count > 0:
                        # 合并所有内容
                        merged_content = self.merge_osm_content(all_content)
                        
                        # 保存合并后的数据
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(merged_content)
                        
                        file_size = file_path.stat().st_size
                        print(f"  ✅ {data_type} 数据下载成功 ({success_count}/{len(fixed_bbox)} 子区域, {file_size} 字节)")
                        results[data_type] = "成功"
                        
                        # 更新进度
                        self.progress[f"{region_name}_{data_type}"] = "completed"
                        self.save_progress()
                    else:
                        print(f"  ❌ {data_type} 所有子区域下载失败")
                        results[data_type] = "失败"
                        
                        # 更新进度
                        self.progress[f"{region_name}_{data_type}"] = "failed"
                        self.save_progress()
                
                else:
                    # 单个边界框
                    query = self.get_simple_overpass_query(fixed_bbox, data_type)
                    response = self.download_with_retry(self.base_url, {"data": query})
                    
                    if response and response.status_code == 200:
                        # 保存数据
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        
                        file_size = file_path.stat().st_size
                        print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                        results[data_type] = "成功"
                        
                        # 更新进度
                        self.progress[f"{region_name}_{data_type}"] = "completed"
                        self.save_progress()
                    else:
                        print(f"  ❌ {data_type} 数据下载失败")
                        results[data_type] = "失败"
                        
                        # 更新进度
                        self.progress[f"{region_name}_{data_type}"] = "failed"
                        self.save_progress()
                        
            except Exception as e:
                print(f"  ❌ {data_type} 下载异常: {e}")
                results[data_type] = "异常"
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "error"
                self.save_progress()
        
        return results
    
    def merge_osm_content(self, content_list):
        """合并多个OSM内容"""
        if not content_list:
            return ""
        
        if len(content_list) == 1:
            return content_list[0]
        
        # 解析第一个文件作为基础
        try:
            root = ET.fromstring(content_list[0])
        except:
            return content_list[0]
        
        # 合并其他文件的内容
        for content in content_list[1:]:
            try:
                other_root = ET.fromstring(content)
                for child in other_root:
                    if child.tag in ['node', 'way', 'relation']:
                        root.append(child)
            except:
                continue
        
        # 返回合并后的XML
        return ET.tostring(root, encoding='unicode')
    
    def download_provinces(self, provinces, data_types):
        """下载多个省份数据"""
        print(f"🚀 开始下载 {len(provinces)} 个省份的矢量数据")
        print(f"📊 数据类型: {', '.join(data_types)}")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_region = {}
            for region_name, bbox in provinces.items():
                future = executor.submit(self.download_region_data, region_name, bbox, data_types)
                future_to_region[future] = region_name
            
            # 处理结果
            for future in as_completed(future_to_region):
                region_name = future_to_region[future]
                try:
                    result = future.result()
                    results[region_name] = result
                except Exception as e:
                    print(f"❌ {region_name} 下载异常: {e}")
                    results[region_name] = {"error": str(e)}
        
        return results
    
    def get_download_status(self):
        """获取下载状态"""
        status = {
            "completed": 0,
            "failed": 0,
            "error": 0,
            "pending": 0,
            "details": {}
        }
        
        for key, value in self.progress.items():
            if value == "completed":
                status["completed"] += 1
            elif value == "failed":
                status["failed"] += 1
            elif value == "error":
                status["error"] += 1
            else:
                status["pending"] += 1
            
            status["details"][key] = value
        
        return status

def main():
    """主函数"""
    downloader = OptimizedVectorDownloader()
    
    # 测试用的省份配置（小范围）
    test_provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5"
    }
    
    # 完整省份配置
    provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5",
        "天津": "116.8,38.5,118.0,40.2",
        "重庆": "105.0,28.0,110.0,32.0",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0"
    }
    
    # 数据类型
    data_types = ["roads", "buildings", "pois"]
    
    print("🚀 优化版矢量数据下载器")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 下载测试省份数据 (推荐)")
        print("2. 下载完整省份数据")
        print("3. 查看下载状态")
        print("4. 清理进度文件")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            print(f"\n🚀 开始下载测试省份数据...")
            results = downloader.download_provinces(test_provinces, data_types)
            
            print("\n📊 下载结果汇总:")
            for region, result in results.items():
                print(f"\n{region}:")
                for data_type, status in result.items():
                    if status == "成功":
                        print(f"  ✅ {data_type}: {status}")
                    elif status == "失败":
                        print(f"  ❌ {data_type}: {status}")
                    else:
                        print(f"  ⏭️ {data_type}: {status}")
        
        elif choice == "2":
            print(f"\n🚀 开始下载完整省份数据...")
            print("⚠️ 注意: 这将需要较长时间")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_provinces(provinces, data_types)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "3":
            status = downloader.get_download_status()
            print(f"\n📊 下载状态:")
            print(f"✅ 已完成: {status['completed']}")
            print(f"❌ 失败: {status['failed']}")
            print(f"⚠️ 异常: {status['error']}")
            print(f"⏳ 待处理: {status['pending']}")
            
            if status['details']:
                print(f"\n📋 详细信息:")
                for key, value in status['details'].items():
                    print(f"  {key}: {value}")
        
        elif choice == "4":
            if os.path.exists(downloader.progress_file):
                os.remove(downloader.progress_file)
                print("✅ 进度文件已清理")
            else:
                print("ℹ️ 进度文件不存在")
        
        elif choice == "5":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
