#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试20米精度要求
验证野外作业监控系统的精度是否满足要求
"""

import requests
import json
import math

def calculate_distance(lat1, lon1, lat2, lon2):
    """计算两点间距离（米）"""
    R = 6371000  # 地球半径（米）
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * 
         math.sin(delta_lon/2) * math.sin(delta_lon/2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    return R * c

def test_precision_requirements():
    """测试精度要求"""
    print("🎯 野外作业监控系统 - 20米精度测试")
    print("=" * 60)
    
    # 测试GPS监控系统
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ GPS监控系统正常")
            print(f"📍 中心点: {config['center']}")
            print(f"🔍 在线地图精度: {config['min_zoom']}-{config['max_zoom']}")
            print(f"🎯 矢量数据精度: {config['vector_max_zoom']}级")
        else:
            print(f"❌ GPS监控系统异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GPS监控系统连接失败: {e}")
        return False
    
    # 测试高精度数据
    test_regions = ['北京', '重庆', '上海']
    
    for region in test_regions:
        print(f"\n🔧 测试区域: {region}")
        try:
            response = requests.get(f'http://127.0.0.1:5000/api/high-precision-data/{region}', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {region} 高精度数据正常")
                print(f"   - 总要素数: {data['metadata']['total_features']}")
                print(f"   - 坐标精度: {data['metadata']['coordinate_precision']}位小数")
                
                # 分析坐标精度
                if data['roads'] and data['roads']['features']:
                    road = data['roads']['features'][0]
                    coords = road['geometry']['coordinates'][0]
                    lat, lon = coords[1], coords[0]
                    
                    # 计算坐标精度
                    precision = 1 / (10 ** data['metadata']['coordinate_precision'])
                    distance_precision = precision * 111000  # 转换为米
                    
                    print(f"   - 理论精度: ±{distance_precision:.2f}米")
                    
                    if distance_precision <= 20:
                        print(f"   ✅ 满足20米精度要求")
                    else:
                        print(f"   ❌ 不满足20米精度要求")
                
            else:
                print(f"❌ {region} 高精度数据异常: {response.status_code}")
        except Exception as e:
            print(f"❌ {region} 高精度数据测试失败: {e}")
    
    return True

def analyze_zoom_levels():
    """分析缩放级别对应的精度"""
    print("\n🔍 缩放级别精度分析")
    print("=" * 60)
    
    # 地球周长（米）
    earth_circumference = 40075000
    
    zoom_levels = [
        (15, "城市级别"),
        (16, "区域级别"), 
        (17, "街道级别"),
        (18, "高精度"),
        (19, "超高精度"),
        (20, "最高精度")
    ]
    
    for zoom, description in zoom_levels:
        # 计算该级别下的瓦片大小
        tiles_per_side = 2 ** zoom
        tile_size_meters = earth_circumference / tiles_per_side
        
        # 计算精度（瓦片大小的一半）
        precision_meters = tile_size_meters / 2
        
        print(f"{zoom}级 - {description}:")
        print(f"   瓦片大小: {tile_size_meters:.2f}米")
        print(f"   理论精度: ±{precision_meters:.2f}米")
        
        if precision_meters <= 20:
            print(f"   ✅ 满足20米精度要求")
        else:
            print(f"   ❌ 不满足20米精度要求")
        print()

def test_coordinate_precision():
    """测试坐标精度"""
    print("🎯 坐标精度测试")
    print("=" * 60)
    
    # 测试不同精度级别的坐标
    test_coords = [
        (39.9042, 116.4074, "北京天安门"),
        (30.295, 109.486, "恩施市中心"),
        (31.2397, 121.4999, "上海外滩")
    ]
    
    for lat, lon, name in test_coords:
        print(f"📍 {name}: {lat}, {lon}")
        
        # 测试不同精度级别
        for precision in [4, 5, 6, 7]:
            # 计算精度范围
            lat_precision = 1 / (10 ** precision)
            lon_precision = 1 / (10 ** precision)
            
            # 转换为米
            lat_meters = lat_precision * 111000
            lon_meters = lon_precision * 111000 * math.cos(math.radians(lat))
            
            max_error = max(lat_meters, lon_meters)
            
            print(f"   {precision}位小数: ±{max_error:.2f}米", end="")
            if max_error <= 20:
                print(" ✅")
            else:
                print(" ❌")
        print()

def show_technical_solution():
    """显示技术解决方案"""
    print("🔧 技术解决方案")
    print("=" * 60)
    
    print("问题分析:")
    print("- 在线地图服务在19-20级没有瓦片数据")
    print("- 需要20米以内的精度要求")
    print("- 野外作业监控需要高精度定位")
    
    print("\n解决方案:")
    print("1. 高精度矢量数据渲染")
    print("   - 坐标精度: 6位小数 (±0.1米)")
    print("   - 数据格式: GeoJSON")
    print("   - 渲染引擎: Leaflet.js")
    
    print("\n2. 智能精度处理")
    print("   - 18级: 在线地图 + 基础矢量数据")
    print("   - 19-20级: 18级在线地图 + 高精度矢量数据")
    print("   - 自动降级机制")
    
    print("\n3. 数据优化")
    print("   - 道路数据: 详细道路网络")
    print("   - 建筑数据: 精确建筑轮廓")
    print("   - POI数据: 关键兴趣点")
    
    print("\n4. 用户体验")
    print("   - 精度级别选择器")
    print("   - 一键最高精度定位")
    print("   - 实时精度显示")

def main():
    """主函数"""
    success = test_precision_requirements()
    analyze_zoom_levels()
    test_coordinate_precision()
    show_technical_solution()
    
    if success:
        print("\n✅ 20米精度测试完成！")
        print("🎯 系统已优化为满足野外作业监控的20米精度要求")
        print("💡 建议使用19-20级精度进行GPS定位")
    else:
        print("\n❌ 测试失败，请检查系统状态！")

if __name__ == "__main__":
    main()