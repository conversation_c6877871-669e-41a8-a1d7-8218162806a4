#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试省市级瓦片下载功能
"""

from download_china_18 import ChinaTileDownloader
import math

def test_baidu_download():
    """测试百度地图下载"""
    print("🧪 测试百度地图下载")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    downloader.set_source('baidu')
    
    # 测试北京天安门瓦片
    x, y = downloader.deg2num(39.9042, 116.4074, 18)
    print(f"测试瓦片: 18/{x}/{y}")
    
    success, message = downloader.download_tile(x, y, 18)
    if success:
        print(f"✅ {message}")
        
        # 检查文件
        tile_path = downloader.output_dir / "18" / str(x) / f"{y}.png"
        if tile_path.exists():
            file_size = tile_path.stat().st_size
            print(f"   文件大小: {file_size} bytes")
            if file_size > 2000:
                print("   ✅ 瓦片质量正常")
            else:
                print("   ⚠️  瓦片可能有问题")
        else:
            print("   ❌ 文件不存在")
    else:
        print(f"❌ {message}")

def test_amap_download():
    """测试高德地图下载"""
    print("\n🧪 测试高德地图下载")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    downloader.set_source('amap')
    
    # 测试北京天安门瓦片
    x, y = downloader.deg2num(39.9042, 116.4074, 18)
    print(f"测试瓦片: 18/{x}/{y}")
    
    success, message = downloader.download_tile(x, y, 18)
    if success:
        print(f"✅ {message}")
        
        # 检查文件
        tile_path = downloader.output_dir / "18" / str(x) / f"{y}.png"
        if tile_path.exists():
            file_size = tile_path.stat().st_size
            print(f"   文件大小: {file_size} bytes")
            if file_size > 2000:
                print("   ✅ 瓦片质量正常")
            else:
                print("   ⚠️  瓦片可能有问题")
        else:
            print("   ❌ 文件不存在")
    else:
        print(f"❌ {message}")

def test_beijing_city():
    """测试北京市瓦片下载"""
    print("\n🧪 测试北京市瓦片下载")
    print("=" * 40)
    
    downloader = ChinaTileDownloader()
    downloader.set_source('baidu')
    
    # 北京市中心坐标和半径
    lat, lon = 39.9042, 116.4074
    radius = 30  # 30km半径
    
    # 计算瓦片范围
    lat_range = radius / 111.0
    lon_range = radius / (111.0 * math.cos(math.radians(lat)))
    
    min_lat = lat - lat_range
    max_lat = lat + lat_range
    min_lon = lon - lon_range
    max_lon = lon + lon_range
    
    min_x, max_y = downloader.deg2num(max_lat, min_lon, 18)
    max_x, min_y = downloader.deg2num(min_lat, max_lon, 18)
    
    if min_x > max_x:
        min_x, max_x = max_x, min_x
    if min_y > max_y:
        min_y, max_y = max_y, min_y
    
    total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
    
    print(f"北京市瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
    print(f"总瓦片数: {total_tiles:,}")
    
    # 下载中心瓦片周围的几个瓦片
    center_x, center_y = downloader.deg2num(lat, lon, 18)
    test_coords = [
        (center_x, center_y),      # 中心
        (center_x-1, center_y),    # 左边
        (center_x+1, center_y),    # 右边
        (center_x, center_y-1),    # 上边
        (center_x, center_y+1),    # 下边
    ]
    
    success_count = 0
    for test_x, test_y in test_coords:
        success, message = downloader.download_tile(test_x, test_y, 18)
        if success:
            success_count += 1
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
    
    print(f"\n测试结果: {success_count}/{len(test_coords)} 成功")

def main():
    """主函数"""
    import math
    
    print("🧪 省市级瓦片下载功能测试")
    print("=" * 50)
    
    # 测试百度地图
    test_baidu_download()
    
    # 测试高德地图
    test_amap_download()
    
    # 测试北京市下载
    test_beijing_city()
    
    print(f"\n✅ 测试完成!")

if __name__ == "__main__":
    main()
