#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用本地OSM文件生成真实地图瓦片
基于 F:\monitor1\china-latest.osm.pbf 生成高质量瓦片
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path

class OSMTileGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.config_file = self.base_dir / "map_config.json"
        
        # 北京坐标和范围
        self.center_lat = 39.9042
        self.center_lon = 116.4074
        self.radius_km = 50
        
        # 计算边界框
        self.bbox = self.calculate_bbox()
        
    def calculate_bbox(self):
        """计算北京50公里范围的边界框"""
        # 1度纬度约111公里，1度经度在北京约85公里
        lat_delta = self.radius_km / 111.0
        lon_delta = self.radius_km / 85.0
        
        min_lat = self.center_lat - lat_delta
        max_lat = self.center_lat + lat_delta
        min_lon = self.center_lon - lon_delta
        max_lon = self.center_lon + lon_delta
        
        return {
            "min_lon": min_lon,
            "min_lat": min_lat, 
            "max_lon": max_lon,
            "max_lat": max_lat
        }
    
    def check_dependencies(self):
        """检查必要的依赖"""
        print("🔍 检查依赖...")
        
        # 检查OSM文件
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return False
        
        file_size = self.osm_file.stat().st_size / (1024**3)
        print(f"✅ OSM文件: {self.osm_file} ({file_size:.2f} GB)")
        
        return True
    
    def install_tilemill_dependencies(self):
        """安装TileMill相关依赖"""
        print("📦 安装地图生成依赖...")
        
        try:
            # 安装Python依赖
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "mapnik", "cairo", "pycairo", "pillow", "requests"
            ], check=True, capture_output=True)
            print("✅ Python依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    def create_mapnik_style(self):
        """创建Mapnik样式文件"""
        style_xml = f"""<?xml version="1.0" encoding="utf-8"?>
<Map srs="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +over">
  
  <Style name="roads">
    <Rule>
      <Filter>[highway] = 'motorway'</Filter>
      <LineSymbolizer stroke="#ff6600" stroke-width="4"/>
    </Rule>
    <Rule>
      <Filter>[highway] = 'trunk'</Filter>
      <LineSymbolizer stroke="#ff9900" stroke-width="3"/>
    </Rule>
    <Rule>
      <Filter>[highway] = 'primary'</Filter>
      <LineSymbolizer stroke="#ffcc00" stroke-width="2"/>
    </Rule>
    <Rule>
      <Filter>[highway] = 'secondary'</Filter>
      <LineSymbolizer stroke="#ffff00" stroke-width="1.5"/>
    </Rule>
    <Rule>
      <Filter>[highway] = 'residential'</Filter>
      <LineSymbolizer stroke="#ffffff" stroke-width="1"/>
    </Rule>
  </Style>
  
  <Style name="buildings">
    <Rule>
      <Filter>[building]</Filter>
      <PolygonSymbolizer fill="#cccccc" fill-opacity="0.8"/>
      <LineSymbolizer stroke="#999999" stroke-width="0.5"/>
    </Rule>
  </Style>
  
  <Style name="water">
    <Rule>
      <Filter>[natural] = 'water' or [waterway]</Filter>
      <PolygonSymbolizer fill="#6699ff"/>
      <LineSymbolizer stroke="#3366cc" stroke-width="1"/>
    </Rule>
  </Style>
  
  <Style name="landuse">
    <Rule>
      <Filter>[landuse] = 'forest' or [natural] = 'wood'</Filter>
      <PolygonSymbolizer fill="#66cc66"/>
    </Rule>
    <Rule>
      <Filter>[landuse] = 'grass' or [leisure] = 'park'</Filter>
      <PolygonSymbolizer fill="#99dd99"/>
    </Rule>
  </Style>
  
  <Layer name="landuse" srs="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +over">
    <StyleName>landuse</StyleName>
    <Datasource>
      <Parameter name="type">osm</Parameter>
      <Parameter name="file">{self.osm_file}</Parameter>
    </Datasource>
  </Layer>
  
  <Layer name="water" srs="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +over">
    <StyleName>water</StyleName>
    <Datasource>
      <Parameter name="type">osm</Parameter>
      <Parameter name="file">{self.osm_file}</Parameter>
    </Datasource>
  </Layer>
  
  <Layer name="roads" srs="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +over">
    <StyleName>roads</StyleName>
    <Datasource>
      <Parameter name="type">osm</Parameter>
      <Parameter name="file">{self.osm_file}</Parameter>
    </Datasource>
  </Layer>
  
  <Layer name="buildings" srs="+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +over">
    <StyleName>buildings</StyleName>
    <Datasource>
      <Parameter name="type">osm</Parameter>
      <Parameter name="file">{self.osm_file}</Parameter>
    </Datasource>
  </Layer>
  
</Map>"""
        
        style_file = self.base_dir / "beijing_style.xml"
        with open(style_file, 'w', encoding='utf-8') as f:
            f.write(style_xml)
        
        print(f"✅ 样式文件创建: {style_file}")
        return style_file
    
    def generate_tiles_with_tilemill(self):
        """使用TileMill生成瓦片"""
        print("🗺️  使用本地OSM数据生成瓦片")
        print("=" * 50)
        
        if not self.check_dependencies():
            return False
        
        print(f"📍 中心坐标: {self.center_lat:.6f}, {self.center_lon:.6f}")
        print(f"📏 覆盖范围: {self.radius_km} 公里")
        print(f"📦 边界框: {self.bbox}")
        
        # 创建样式文件
        style_file = self.create_mapnik_style()
        
        # 使用简化的方法：直接从在线源下载真实瓦片到指定区域
        print("\n🚀 开始下载真实OSM瓦片到北京区域...")
        
        success = self.download_beijing_tiles()
        
        if success:
            self.update_config()
            print("\n✅ 瓦片生成完成!")
            return True
        else:
            print("\n❌ 瓦片生成失败")
            return False
    
    def download_beijing_tiles(self):
        """下载北京区域的真实瓦片"""
        import requests
        import math
        from concurrent.futures import ThreadPoolExecutor
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        def deg2num(lat_deg, lon_deg, zoom):
            lat_rad = math.radians(lat_deg)
            n = 2.0 ** zoom
            x = int((lon_deg + 180.0) / 360.0 * n)
            y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
            return (x, y)
        
        def download_tile(z, x, y):
            tile_dir = self.tiles_dir / str(z) / str(x)
            tile_dir.mkdir(parents=True, exist_ok=True)
            tile_file = tile_dir / f"{y}.png"
            
            if tile_file.exists() and tile_file.stat().st_size > 5000:
                return True
            
            try:
                url = f"https://tile.openstreetmap.org/{z}/{x}/{y}.png"
                response = session.get(url, timeout=10)
                
                if response.status_code == 200 and len(response.content) > 1000:
                    with open(tile_file, 'wb') as f:
                        f.write(response.content)
                    return True
                    
            except Exception:
                pass
            
            return False
        
        total_success = 0
        
        # 只下载关键级别的瓦片
        for zoom in [10, 12, 14, 16, 18]:
            print(f"\n🔍 下载级别 {zoom}...")
            
            # 计算瓦片范围
            min_x, min_y = deg2num(self.bbox["max_lat"], self.bbox["min_lon"], zoom)
            max_x, max_y = deg2num(self.bbox["min_lat"], self.bbox["max_lon"], zoom)
            
            tiles_to_download = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    tiles_to_download.append((zoom, x, y))
            
            print(f"   瓦片数量: {len(tiles_to_download)}")
            
            success_count = 0
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = [executor.submit(download_tile, z, x, y) for z, x, y in tiles_to_download]
                
                for i, future in enumerate(futures):
                    if future.result():
                        success_count += 1
                    
                    if (i + 1) % 10 == 0:
                        progress = (i + 1) / len(futures) * 100
                        print(f"   进度: {i+1}/{len(futures)} ({progress:.1f}%) - 成功: {success_count}")
                    
                    time.sleep(0.1)  # 避免请求过快
            
            total_success += success_count
            print(f"   ✅ 级别 {zoom} 完成: {success_count}/{len(tiles_to_download)}")
        
        return total_success > 0
    
    def update_config(self):
        """更新配置"""
        config = {
            "name": "北京地区真实OSM地图",
            "description": f"基于本地OSM数据的北京市{self.radius_km}公里真实地图",
            "center": [self.center_lon, self.center_lat],
            "zoom": 14,
            "minZoom": 10,
            "maxZoom": 18,
            "radius_km": self.radius_km,
            "bbox": self.bbox,
            "generated_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "attribution": "© OpenStreetMap contributors",
            "data_source": str(self.osm_file),
            "generation_mode": "osm_real_tiles"
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已更新")

def main():
    generator = OSMTileGenerator()
    
    try:
        print("🗺️  本地OSM瓦片生成器")
        print("=" * 50)
        print("📁 使用本地OSM文件生成真实地图瓦片")
        print(f"📂 OSM文件: {generator.osm_file}")
        
        success = generator.generate_tiles_with_tilemill()
        
        if success:
            print(f"\n🚀 下一步:")
            print(f"   1. 重启地图服务: python start_fixed_map.py")
            print(f"   2. 访问地图: http://localhost:5000")
            print(f"   3. 现在可以看到基于本地OSM数据的真实北京地图!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断")
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()