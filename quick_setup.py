#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速设置脚本
为野外深机井监控系统快速配置离线地图
"""

import os
import sys
import requests
import math
import time
from pathlib import Path

def download_essential_tiles():
    """下载必要的地图瓦片"""
    print("🗺️  快速下载必要地图瓦片")
    print("=" * 40)
    
    base_dir = Path(__file__).parent
    tiles_dir = base_dir / "static" / "tiles"
    tiles_dir.mkdir(parents=True, exist_ok=True)
    
    # 重点区域 (北京周边，适合深机井监控)
    locations = [
        {"lat": 39.9042, "lng": 116.4074, "name": "北京中心"},
        {"lat": 40.0000, "lng": 116.3000, "name": "北京西北"},
        {"lat": 39.8000, "lng": 116.5000, "name": "北京东南"},
        {"lat": 40.1000, "lng": 116.6000, "name": "北京东北"},
    ]
    
    zoom_levels = [10, 12, 15]  # 适中的缩放级别
    base_url = "https://tile.openstreetmap.org"
    
    total_downloaded = 0
    total_attempted = 0
    
    for location in locations:
        print(f"\n📍 下载 {location['name']} 区域瓦片...")
        lat, lng = location["lat"], location["lng"]
        
        for zoom in zoom_levels:
            # 计算瓦片坐标
            x = int((lng + 180.0) / 360.0 * (1 << zoom))
            y = int((1.0 - math.asinh(math.tan(math.radians(lat))) / math.pi) / 2.0 * (1 << zoom))
            
            # 下载3x3区域的瓦片
            for dx in range(-1, 2):
                for dy in range(-1, 2):
                    tile_x, tile_y = x + dx, y + dy
                    
                    # 检查坐标有效性
                    max_coord = 2 ** zoom
                    if 0 <= tile_x < max_coord and 0 <= tile_y < max_coord:
                        total_attempted += 1
                        if download_single_tile(base_url, zoom, tile_x, tile_y, tiles_dir):
                            total_downloaded += 1
                        
                        # 添加延迟避免过于频繁请求
                        time.sleep(0.2)
    
    print(f"\n✅ 下载完成: {total_downloaded}/{total_attempted} 个瓦片")
    return total_downloaded > 0

def download_single_tile(base_url, z, x, y, tiles_dir):
    """下载单个瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    # 如果文件已存在，跳过
    if tile_file.exists() and tile_file.stat().st_size > 100:
        return True
    
    try:
        url = f"{base_url}/{z}/{x}/{y}.png"
        headers = {
            'User-Agent': 'WellMonitoringSystem/1.0'
        }
        
        response = requests.get(url, timeout=10, headers=headers)
        
        if response.status_code == 200:
            with open(tile_file, 'wb') as f:
                f.write(response.content)
            print(f"  ✅ {z}/{x}/{y}.png")
            return True
        else:
            print(f"  ❌ {z}/{x}/{y}.png (HTTP {response.status_code})")
            return False
            
    except Exception as e:
        print(f"  ❌ {z}/{x}/{y}.png ({e})")
        return False

def create_basic_config():
    """创建基础配置文件"""
    print("\n🔧 创建基础配置...")
    
    base_dir = Path(__file__).parent
    
    # 创建地图配置
    map_config = {
        "name": "野外深机井监控地图",
        "center": [116.4074, 39.9042],
        "zoom": 12,
        "minZoom": 8,
        "maxZoom": 18
    }
    
    config_file = base_dir / "map_config.json"
    import json
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(map_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")

def test_system():
    """测试系统功能"""
    print("\n🧪 测试系统功能...")
    
    try:
        # 测试数据接收
        from test_system import MockDataReceiver
        receiver = MockDataReceiver()
        receiver.start()
        
        gps_data = receiver.get_gps_data()
        plc_data = receiver.get_plc_data()
        
        if gps_data and plc_data:
            print("✅ 数据接收模块正常")
        else:
            print("⚠️  数据接收模块异常")
        
        receiver.stop()
        
    except Exception as e:
        print(f"⚠️  测试过程中出现问题: {e}")

def main():
    print("🚀 野外深机井监控系统 - 快速设置")
    print("=" * 50)
    
    try:
        # 1. 下载必要瓦片
        if download_essential_tiles():
            print("✅ 地图瓦片准备完成")
        else:
            print("⚠️  地图瓦片下载失败，但系统仍可运行")
        
        # 2. 创建配置
        create_basic_config()
        
        # 3. 测试系统
        test_system()
        
        print("\n" + "=" * 50)
        print("🎉 快速设置完成!")
        print("\n🚀 启动系统:")
        print("   python start_system.py test")
        print("\n🌐 访问地址:")
        print("   http://localhost:5000")
        
        # 询问是否立即启动
        choice = input("\n是否立即启动测试模式? (Y/n): ").lower()
        if choice != 'n':
            print("\n🚀 启动测试模式...")
            os.system("python start_system.py test")
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户中断设置")
        return False
    except Exception as e:
        print(f"\n❌ 设置失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)