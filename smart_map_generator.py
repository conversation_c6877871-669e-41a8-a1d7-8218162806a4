#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能地图生成器
根据GPS坐标动态生成指定范围和精度的离线地图
"""

import os
import sys
import math
import json
# 依赖自动修复
def install_missing_module(module_name):
    import subprocess
    import sys
    try:
        print(f"检测到缺少 '{module_name}' 模块，正在自动安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", module_name])
        return True
    except Exception as e:
        print(f"自动安装失败，请手动运行: pip install {module_name}")
        return False

# 必需模块列表
required_modules = ['requests', 'osmium', 'Pillow']
for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        if not install_missing_module(module):
            raise SystemExit("依赖缺失，脚本终止")

# 原代码继续执行
import threading
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import time

class SmartMapGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.config_file = self.base_dir / "map_config.json"
        self.download_stats = {"success": 0, "failed": 0, "total": 0}
        
        # 验证OSM文件是否存在且可读
        osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        if not osm_file.exists():
            raise FileNotFoundError(f"OSM文件不存在: {osm_file}")
        if not os.access(osm_file, os.R_OK):
            raise PermissionError(f"无法读取OSM文件: {osm_file}")
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """将经纬度转换为瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """将瓦片坐标转换为经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def calculate_tile_range(self, center_lat, center_lon, radius_km, zoom):
        """计算指定半径内的瓦片范围 - 优化为小范围高精度"""
        # 支持50公里半径范围
        
        # 近似算法优化
        lat_delta = radius_km / 111.32
        lon_delta = radius_km / (111.32 * math.cos(math.radians(center_lat)))
        
        min_lat = center_lat - lat_delta
        max_lat = center_lat + lat_delta
        min_lon = center_lon - lon_delta
        max_lon = center_lon + lon_delta
        
        # 计算经度范围 (考虑纬度影响)
        # 地球平均半径（单位：公里）
        earth_radius = 6371.0
        lon_delta = radius_km / (earth_radius * math.cos(math.radians(center_lat))) * 180 / math.pi
        min_lon = center_lon - lon_delta
        max_lon = center_lon + lon_delta
        
        # 转换为瓦片坐标
        min_x, max_y = self.deg2num(min_lat, min_lon, zoom)
        max_x, min_y = self.deg2num(max_lat, max_lon, zoom)
        
        return {
            "min_x": min_x, "max_x": max_x,
            "min_y": min_y, "max_y": max_y,
            "center_x": (min_x + max_x) // 2,
            "center_y": (min_y + max_y) // 2,
            "total_tiles": (max_x - min_x + 1) * (max_y - min_y + 1)
        }
    
    def estimate_data_size(self, center_lat, center_lon, radius_km, max_zoom):
        """估算数据大小"""
        total_tiles = 0
        zoom_info = {}
        
        for zoom in range(8, max_zoom + 1):
            tile_range = self.calculate_tile_range(center_lat, center_lon, radius_km, zoom)
            tiles_count = tile_range["total_tiles"]
            total_tiles += tiles_count
            
            zoom_info[zoom] = {
                "tiles": tiles_count,
                "size_mb": round(tiles_count * 0.015, 2)  # 平均每个瓦片15KB
            }
        
        total_size_mb = round(total_tiles * 0.015, 2)
        
        return {
            "total_tiles": total_tiles,
            "total_size_mb": total_size_mb,
            "zoom_levels": zoom_info
        }
    
    def create_offline_tile(self, z, x, y):
        """创建离线瓦片 (当无法下载时使用)"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        if tile_file.exists():
            return True
        
        try:
            # 创建256x256的瓦片图像
            img = Image.new('RGB', (256, 256), color='#f0f8ff')
            draw = ImageDraw.Draw(img)
            
            # 绘制网格
            for i in range(0, 256, 32):
                draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
                draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
            
            # 添加瓦片信息
            try:
                font = ImageFont.truetype("arial.ttf", 10)
            except:
                font = ImageFont.load_default()
            
            text = f"Z{z} X{x} Y{y}"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            text_x = (256 - text_width) // 2
            text_y = (256 - text_height) // 2
            
            draw.rectangle([text_x-3, text_y-3, text_x+text_width+3, text_y+text_height+3], 
                          fill='white', outline='black')
            draw.text((text_x, text_y), text, fill='black', font=font)
            
            # 根据缩放级别添加不同特征
            if z >= 12:
                # 模拟道路
                draw.line([(50, 50), (200, 200)], fill='#ffff00', width=2)
                draw.line([(200, 50), (50, 200)], fill='#ffff00', width=1)
            
            if z >= 15:
                # 模拟建筑
                draw.rectangle([80, 80, 120, 120], fill='#d3d3d3', outline='black')
                draw.rectangle([140, 140, 180, 180], fill='#d3d3d3', outline='black')
            
            if z >= 18:
                # 高精度特征
                draw.ellipse([100, 100, 130, 130], fill='#90ee90', outline='green')
                draw.rectangle([160, 100, 190, 130], fill='#87ceeb', outline='blue')
                draw.polygon([(70, 200), (90, 180), (110, 200)], fill='#8b4513', outline='black')
            
            img.save(tile_file, 'PNG')
            return True
            
        except Exception as e:
            print(f"  ❌ 创建离线瓦片失败: {z}/{x}/{y} - {e}")
            return False
    
    def download_tile(self, z, x, y, base_url="https://tile.openstreetmap.org"):
        """下载单个瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        if tile_file.exists():
            return True
        
        try:
            url = f"{base_url}/{z}/{x}/{y}.png"
            response = requests.get(url, timeout=5, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            if response.status_code == 200:
                with open(tile_file, 'wb') as f:
                    f.write(response.content)
                self.download_stats["success"] += 1
                return True
            else:
                # 下载失败，创建离线瓦片
                return self.create_offline_tile(z, x, y)
                
        except Exception:
            # 网络错误，创建离线瓦片
            return self.create_offline_tile(z, x, y)
    
    def generate_area_map(self, center_lat, center_lon, radius_km=100, max_zoom=18, 
                         min_zoom=8, use_online=True):
        """生成指定区域的地图"""
        print(f"🗺️  智能地图生成器")
        print(f"📍 中心坐标: {center_lat:.6f}, {center_lon:.6f}")
        print(f"📏 覆盖半径: {radius_km} 公里")
        print(f"🔍 精度范围: {min_zoom}-{max_zoom} 级")
        print("=" * 50)
        
        # 估算数据大小
        size_info = self.estimate_data_size(center_lat, center_lon, radius_km, max_zoom)
        print(f"📊 预估数据量:")
        print(f"   总瓦片数: {size_info['total_tiles']:,}")
        print(f"   预估大小: {size_info['total_size_mb']} MB")
        
        # 询问用户确认
        if size_info['total_size_mb'] > 100:
            print(f"⚠️  数据量较大 ({size_info['total_size_mb']} MB)")
            choice = input("是否继续生成? (y/N): ").lower()
            if choice != 'y':
                print("已取消生成")
                return False
        
        print(f"\n🚀 开始生成地图...")
        start_time = time.time()
        
        # 重置统计
        self.download_stats = {"success": 0, "failed": 0, "total": 0}
        
        # 按缩放级别生成
        for zoom in range(min_zoom, max_zoom + 1):
            print(f"\n📍 生成缩放级别 {zoom}...")
            
            tile_range = self.calculate_tile_range(center_lat, center_lon, radius_km, zoom)
            level_total = tile_range["total_tiles"]
            level_processed = 0
            
            print(f"   瓦片范围: X({tile_range['min_x']}-{tile_range['max_x']}) "
                  f"Y({tile_range['min_y']}-{tile_range['max_y']})")
            print(f"   瓦片数量: {level_total}")
            
            # 生成瓦片
            for x in range(tile_range["min_x"], tile_range["max_x"] + 1):
                for y in range(tile_range["min_y"], tile_range["max_y"] + 1):
                    if use_online:
                        success = self.download_tile(zoom, x, y)
                    else:
                        success = self.create_offline_tile(zoom, x, y)
                    
                    if success:
                        self.download_stats["success"] += 1
                    else:
                        self.download_stats["failed"] += 1
                    
                    self.download_stats["total"] += 1
                    level_processed += 1
                    
                    # 显示进度
                    if level_processed % 50 == 0 or level_processed == level_total:
                        progress = level_processed / level_total * 100
                        print(f"   进度: {level_processed}/{level_total} ({progress:.1f}%)")
        
        # 生成完成统计
        elapsed_time = time.time() - start_time
        print(f"\n" + "=" * 50)
        print(f"🎉 地图生成完成!")
        print(f"📊 统计信息:")
        print(f"   成功: {self.download_stats['success']:,} 个瓦片")
        print(f"   失败: {self.download_stats['failed']:,} 个瓦片")
        print(f"   总计: {self.download_stats['total']:,} 个瓦片")
        print(f"   用时: {elapsed_time:.1f} 秒")
        
        # 创建地图配置
        self.create_map_config(center_lat, center_lon, radius_km, min_zoom, max_zoom)
        
        return True
    
    def create_map_config(self, center_lat, center_lon, radius_km, min_zoom, max_zoom):
        """创建地图配置文件"""
        config = {
            "name": "智能生成离线地图",
            "description": f"以({center_lat:.6f}, {center_lon:.6f})为中心，半径{radius_km}公里的高精度地图",
            "center": [center_lon, center_lat],
            "zoom": 15,
            "minZoom": min_zoom,
            "maxZoom": max_zoom,
            "radius_km": radius_km,
            "generated_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "attribution": "© OpenStreetMap contributors"
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 地图配置已保存: {self.config_file}")
    
    def get_current_tiles_info(self):
        """获取当前瓦片信息"""
        if not self.tiles_dir.exists():
            return {"tiles": 0, "size_mb": 0, "zoom_levels": []}
        
        total_tiles = 0
        total_size = 0
        zoom_levels = set()
        
        for tile_file in self.tiles_dir.rglob("*.png"):
            total_tiles += 1
            total_size += tile_file.stat().st_size
            
            # 提取缩放级别
            parts = tile_file.parts
            if len(parts) >= 3:
                try:
                    zoom = int(parts[-3])
                    zoom_levels.add(zoom)
                except ValueError:
                    pass
        
        return {
            "tiles": total_tiles,
            "size_mb": round(total_size / 1024 / 1024, 2),
            "zoom_levels": sorted(list(zoom_levels))
        }

def main():
    generator = SmartMapGenerator()
    
    print("🗺️  智能地图生成器")
    print("=" * 50)
    
    # 显示当前状态
    current_info = generator.get_current_tiles_info()
    print(f"📊 当前瓦片: {current_info['tiles']} 个")
    print(f"📊 占用空间: {current_info['size_mb']} MB")
    print(f"📊 缩放级别: {current_info['zoom_levels']}")
    
    print(f"\n📋 生成选项:")
    print(f"1. 北京地区 (39.9042, 116.4074)")
    print(f"2. 上海地区 (31.2304, 121.4737)")
    print(f"3. 广州地区 (23.1291, 113.2644)")
    print(f"4. 自定义坐标")
    print(f"5. 从GPS数据生成")
    
    try:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            center_lat, center_lon = 39.9042, 116.4074
            area_name = "北京"
        elif choice == "2":
            center_lat, center_lon = 31.2304, 121.4737
            area_name = "上海"
        elif choice == "3":
            center_lat, center_lon = 23.1291, 113.2644
            area_name = "广州"
        elif choice == "4":
            center_lat = float(input("请输入纬度: "))
            center_lon = float(input("请输入经度: "))
            area_name = "自定义区域"
        elif choice == "5":
            # 从GPS数据文件读取
            print("从GPS数据生成功能待实现...")
            return
        else:
            print("无效选择")
            return
        
        # 获取参数
        radius_km = int(input(f"覆盖半径 (公里, 默认100): ") or "100")
        max_zoom = int(input(f"最大精度级别 (8-18, 默认18): ") or "18")
        min_zoom = int(input(f"最小精度级别 (1-17, 默认8): ") or "8")
        
        use_online = input("是否尝试在线下载? (Y/n, 离线模式直接使用 F:\\monitor1\\china-latest.osm.pbf): ").lower() != 'n'
        if not use_online:
            offline_file = "F:\\monitor1\\china-latest.osm.pbf"
            if not os.path.exists(offline_file):
                print(f"❌ 离线地图文件未找到: {offline_file}")
                raise SystemExit("请确保文件存在或选择在线下载")
            print(f"✅ 使用离线地图文件: {offline_file}")
        
        print(f"\n🎯 生成参数:")
        print(f"   区域: {area_name}")
        print(f"   中心: {center_lat:.6f}, {center_lon:.6f}")
        print(f"   半径: {radius_km} 公里")
        print(f"   精度: {min_zoom}-{max_zoom} 级")
        print(f"   模式: {'在线+离线' if use_online else '纯离线'}")
        
        confirm = input(f"\n确认生成? (y/N): ").lower()
        if confirm == 'y':
            success = generator.generate_area_map(
                center_lat, center_lon, radius_km, max_zoom, min_zoom, use_online
            )
            
            if success:
                print(f"\n🚀 下一步:")
                print(f"   1. 启动系统: python start_system.py test")
                print(f"   2. 访问界面: http://localhost:5000")
                print(f"   3. 查看地图: 地图将自动定位到生成区域")
        else:
            print("已取消生成")
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️  用户中断生成")
    except Exception as e:
        print(f"❌ 生成过程出错: {e}")

if __name__ == "__main__":
    main()