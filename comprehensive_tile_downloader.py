#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的瓦片下载工具
基于开源项目MapTileGenerator的思路，支持多种地图源
"""

import os
import requests
import math
import time
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlencode

class ComprehensiveTileDownloader:
    def __init__(self, output_dir="static/tiles"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 地图源配置
        self.tile_sources = {
            'osm': {
                'name': 'OpenStreetMap',
                'url_template': 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            'cartodb_light': {
                'name': 'CartoDB Light',
                'url_template': 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
                'subdomains': ['a', 'b', 'c', 'd'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            'cartodb_dark': {
                'name': 'CartoDB Dark',
                'url_template': 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png',
                'subdomains': ['a', 'b', 'c', 'd'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            'esri': {
                'name': 'ESRI World Street Map',
                'url_template': 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            }
        }
        
        self.current_source = 'osm'
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """将经纬度转换为瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return x, y
    
    def get_tile_url(self, x, y, z, source=None):
        """获取瓦片URL"""
        if source is None:
            source = self.current_source
            
        config = self.tile_sources[source]
        url_template = config['url_template']
        
        # 处理子域名轮换
        if 'subdomains' in config:
            subdomain = config['subdomains'][(x + y) % len(config['subdomains'])]
            url_template = url_template.replace('{s}', subdomain)
        
        return url_template.format(x=x, y=y, z=z)
    
    def download_tile(self, x, y, z, source=None, retries=3):
        """下载单个瓦片"""
        if source is None:
            source = self.current_source
            
        config = self.tile_sources[source]
        url = self.get_tile_url(x, y, z, source)
        
        # 创建目录
        tile_dir = self.output_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        
        tile_path = tile_dir / f"{y}.png"
        
        # 如果文件已存在且大小合理，跳过下载
        if tile_path.exists() and tile_path.stat().st_size > 2000:
            return True, f"跳过 {z}/{x}/{y}"
        
        for attempt in range(retries):
            try:
                response = requests.get(url, headers=config['headers'], timeout=15)
                response.raise_for_status()
                
                # 检查响应内容
                if len(response.content) < 1000:
                    if attempt < retries - 1:
                        time.sleep(1)
                        continue
                    else:
                        return False, f"瓦片 {z}/{x}/{y} 内容过小"
                
                # 保存文件
                with open(tile_path, 'wb') as f:
                    f.write(response.content)
                
                return True, f"下载 {z}/{x}/{y} ({len(response.content)} bytes)"
                
            except Exception as e:
                if attempt < retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return False, f"下载 {z}/{x}/{y} 失败: {e}"
        
        return False, f"下载 {z}/{x}/{y} 失败，已重试{retries}次"
    
    def download_region_tiles(self, center_lat, center_lon, zoom_range, radius_km=20, max_workers=5):
        """下载指定区域的瓦片"""
        print(f"🗺️  开始下载区域瓦片")
        print(f"   中心点: ({center_lat}, {center_lon})")
        print(f"   缩放级别: {zoom_range}")
        print(f"   半径: {radius_km} km")
        print(f"   地图源: {self.tile_sources[self.current_source]['name']}")
        
        # 计算边界框
        lat_range = radius_km / 111.0
        lon_range = radius_km / (111.0 * math.cos(math.radians(center_lat)))
        
        min_lat = center_lat - lat_range
        max_lat = center_lat + lat_range
        min_lon = center_lon - lon_range
        max_lon = center_lon + lon_range
        
        print(f"   边界框: ({min_lat:.4f}, {min_lon:.4f}) 到 ({max_lat:.4f}, {max_lon:.4f})")
        
        total_success = 0
        total_tiles = 0
        
        for z in zoom_range:
            print(f"\n📊 下载缩放级别 {z}")
            
            # 计算瓦片范围
            min_x, max_y = self.deg2num(max_lat, min_lon, z)
            max_x, min_y = self.deg2num(min_lat, max_lon, z)
            
            # 确保范围正确
            if min_x > max_x:
                min_x, max_x = max_x, min_x
            if min_y > max_y:
                min_y, max_y = max_y, min_y
            
            zoom_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
            total_tiles += zoom_tiles
            
            print(f"   瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
            print(f"   需要下载: {zoom_tiles} 个瓦片")
            
            if zoom_tiles <= 0:
                print(f"   ⚠️  跳过无效范围")
                continue
            
            # 创建任务列表
            tasks = []
            for x in range(min_x, max_x + 1):
                for y in range(min_y, max_y + 1):
                    tasks.append((x, y, z))
            
            # 并行下载
            zoom_success = 0
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_tile = {
                    executor.submit(self.download_tile, x, y, z): (x, y, z) 
                    for x, y, z in tasks
                }
                
                for future in as_completed(future_to_tile):
                    x, y, z = future_to_tile[future]
                    try:
                        success, message = future.result()
                        if success:
                            zoom_success += 1
                            total_success += 1
                        else:
                            print(f"   ❌ {message}")
                    except Exception as e:
                        print(f"   ❌ 瓦片 {z}/{x}/{y} 处理异常: {e}")
            
            print(f"   ✅ 完成: {zoom_success}/{zoom_tiles} 成功")
        
        print(f"\n🎉 下载完成!")
        print(f"   总瓦片数: {total_tiles}")
        print(f"   成功下载: {total_success}")
        if total_tiles > 0:
            print(f"   成功率: {total_success/total_tiles*100:.1f}%")
        
        return total_success, total_tiles
    
    def download_beijing_comprehensive(self, zoom_range=(8, 15)):
        """全面下载北京地区瓦片"""
        # 北京天安门坐标
        beijing_lat = 39.9042
        beijing_lon = 116.4074
        
        return self.download_region_tiles(
            center_lat=beijing_lat,
            center_lon=beijing_lon,
            zoom_range=zoom_range,
            radius_km=30,  # 30km半径
            max_workers=6
        )
    
    def set_source(self, source_name):
        """设置地图源"""
        if source_name in self.tile_sources:
            self.current_source = source_name
            print(f"✅ 已切换到地图源: {self.tile_sources[source_name]['name']}")
        else:
            print(f"❌ 未知的地图源: {source_name}")
            print(f"可用地图源: {list(self.tile_sources.keys())}")

def main():
    """主函数"""
    print("🗺️  全面瓦片下载工具")
    print("=" * 50)
    
    downloader = ComprehensiveTileDownloader()
    
    # 显示可用地图源
    print("可用地图源:")
    for key, config in downloader.tile_sources.items():
        print(f"  {key}: {config['name']}")
    
    # 选择地图源
    source = input("\n请选择地图源 (默认: osm): ").strip() or 'osm'
    downloader.set_source(source)
    
    # 选择下载模式
    print("\n下载模式:")
    print("1. 快速修复 (只下载14级)")
    print("2. 全面下载 (8-15级)")
    print("3. 自定义区域")
    
    choice = input("请选择 (默认: 1): ").strip() or '1'
    
    if choice == '1':
        # 快速修复14级
        print("\n🚀 快速修复14级瓦片")
        success, total = downloader.download_region_tiles(
            center_lat=39.9042,
            center_lon=116.4074,
            zoom_range=[14],
            radius_km=15,
            max_workers=8
        )
        
    elif choice == '2':
        # 全面下载
        print("\n🚀 全面下载北京地区瓦片")
        success, total = downloader.download_beijing_comprehensive(range(8, 16))
        
    else:
        # 自定义区域
        try:
            lat = float(input("请输入中心纬度: "))
            lon = float(input("请输入中心经度: "))
            radius = float(input("请输入半径(km, 默认20): ") or "20")
            
            zoom_input = input("请输入缩放级别范围 (默认: 8-15): ").strip()
            if zoom_input:
                min_z, max_z = map(int, zoom_input.split('-'))
                zoom_range = range(min_z, max_z + 1)
            else:
                zoom_range = range(8, 16)
            
            print(f"\n开始下载自定义区域瓦片")
            success, total = downloader.download_region_tiles(
                center_lat=lat,
                center_lon=lon,
                zoom_range=zoom_range,
                radius_km=radius
            )
            
        except ValueError:
            print("❌ 输入格式错误")
            return
    
    print(f"\n✅ 下载完成! 瓦片保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
