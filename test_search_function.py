1#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能
"""

import requests
import json

def test_search_api():
    """测试搜索API"""
    base_url = "http://127.0.0.1:5000"
    
    # 测试查询
    test_queries = [
        "北京",
        "上海", 
        "天安门",
        "故宫",
        "广东",
        "四川",
        "西湖",
        "长城"
    ]
    
    print("🔍 测试搜索功能")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n📝 测试查询: {query}")
        
        try:
            # 测试快速搜索
            response = requests.get(f"{base_url}/api/quick-search?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"  ✅ 快速搜索: 找到 {len(results)} 个结果")
                for result in results[:3]:  # 显示前3个结果
                    print(f"    - {result['name']} ({result['type']}) - {result['region']}")
            else:
                print(f"  ❌ 快速搜索失败: HTTP {response.status_code}")
            
            # 测试完整搜索
            response = requests.get(f"{base_url}/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"  ✅ 完整搜索: 找到 {len(results)} 个结果")
            else:
                print(f"  ❌ 完整搜索失败: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接失败: 请确保系统正在运行")
            break
        except Exception as e:
            print(f"  ❌ 搜索异常: {e}")

def test_system_status():
    """测试系统状态"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n📊 测试系统状态")
    print("=" * 50)
    
    try:
        # 测试统计API
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"  ✅ 系统统计:")
            print(f"    - 总区域数: {stats['total_regions']}")
            print(f"    - 完整区域: {stats['complete_regions']}")
            print(f"    - 完成率: {stats['completion_rate']}%")
            print(f"    - 著名地标: {stats['famous_places_count']}")
        else:
            print(f"  ❌ 统计API失败: HTTP {response.status_code}")
        
        # 测试区域API
        response = requests.get(f"{base_url}/api/regions")
        if response.status_code == 200:
            regions = response.json()
            print(f"  ✅ 区域数据: 加载了 {len(regions)} 个区域")
        else:
            print(f"  ❌ 区域API失败: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print(f"  ❌ 连接失败: 请确保系统正在运行")
    except Exception as e:
        print(f"  ❌ 状态检查异常: {e}")

def main():
    """主函数"""
    print("🧪 搜索功能测试工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择测试类型:")
        print("1. 测试搜索功能")
        print("2. 测试系统状态")
        print("3. 全部测试")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            test_search_api()
        
        elif choice == "2":
            test_system_status()
        
        elif choice == "3":
            test_system_status()
            test_search_api()
        
        elif choice == "4":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
