#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瓦片检查器和20级瓦片生成器
检查现有18级瓦片的有效性，并生成20级超高精度瓦片
"""

import os
import sys
import math
import time
import osmium
from pathlib import Path
from PIL import Image, ImageDraw
import json
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing

class Level20OSMProcessor(osmium.SimpleHandler):
    """20级超高精度OSM处理器"""
    
    def __init__(self, bbox, max_features=10000):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox
        self.max_features = max_features
        self.nodes = {}
        self.roads = []
        self.buildings = []
        self.water_features = []
        self.pois = []
        self.feature_count = 0
        
    def node(self, n):
        """处理节点 - 20级精度"""
        if self.feature_count > self.max_features:
            return
            
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            
            # 20级需要更高精度 - 10位小数
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 10),
                'lat': round(n.location.lat, 10),
                'tags': dict(n.tags)
            }
            
            # 检查POI
            if n.tags:
                poi_tags = ['amenity', 'shop', 'tourism', 'leisure', 'office', 'craft', 'emergency']
                if any(tag in n.tags for tag in poi_tags):
                    self.pois.append({
                        'id': n.id,
                        'lon': round(n.location.lon, 10),
                        'lat': round(n.location.lat, 10),
                        'tags': dict(n.tags)
                    })
                    self.feature_count += 1
    
    def way(self, w):
        """处理路径 - 20级精度"""
        if self.feature_count > self.max_features:
            return
            
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if len(relevant_nodes) >= 2:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            if 'building' in w.tags:
                self.buildings.append(way_data)
                self.feature_count += 1
            elif 'highway' in w.tags:
                self.roads.append(way_data)
                self.feature_count += 1
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)
                self.feature_count += 1

class TileCheckerAndLevel20Generator:
    """瓦片检查器和20级生成器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.level20_dir = self.tiles_dir / "20"
        
        # 创建20级目录
        self.level20_dir.mkdir(parents=True, exist_ok=True)
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        # num2deg 返回 (lat, lon)，需要正确赋值
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)

        # 返回格式: [west, south, east, north] = [min_lon, min_lat, max_lon, max_lat]
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def check_level18_tiles(self):
        """检查18级瓦片的有效性"""
        print("🔍 检查18级瓦片有效性")
        print("=" * 50)
        
        level18_dir = self.tiles_dir / "18"
        if not level18_dir.exists():
            print("❌ 18级瓦片目录不存在")
            return False, []
        
        valid_tiles = []
        invalid_tiles = []
        total_size = 0
        
        for x_dir in level18_dir.iterdir():
            if x_dir.is_dir() and x_dir.name.isdigit():
                x = int(x_dir.name)
                
                for tile_file in x_dir.glob("*.png"):
                    y = int(tile_file.stem)
                    
                    try:
                        # 检查文件大小
                        file_size = tile_file.stat().st_size
                        total_size += file_size
                        
                        # 检查图像有效性
                        with Image.open(tile_file) as img:
                            if img.size == (256, 256) and file_size > 1000:
                                valid_tiles.append((18, x, y))
                            else:
                                invalid_tiles.append((18, x, y, f"尺寸或大小异常: {img.size}, {file_size}字节"))
                                
                    except Exception as e:
                        invalid_tiles.append((18, x, y, f"文件损坏: {e}"))
        
        print(f"✅ 有效瓦片: {len(valid_tiles)} 个")
        print(f"❌ 无效瓦片: {len(invalid_tiles)} 个")
        print(f"📊 总大小: {total_size / 1024 / 1024:.2f} MB")
        
        if invalid_tiles:
            print("\n⚠️  无效瓦片列表:")
            for z, x, y, reason in invalid_tiles[:10]:  # 只显示前10个
                print(f"   {z}/{x}/{y}: {reason}")
            if len(invalid_tiles) > 10:
                print(f"   ... 还有 {len(invalid_tiles) - 10} 个")
        
        # 分析覆盖范围
        if valid_tiles:
            x_coords = [tile[1] for tile in valid_tiles]
            y_coords = [tile[2] for tile in valid_tiles]
            
            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)
            
            # 转换为地理坐标
            lat1, lon1 = self.num2deg(min_x, min_y, 18)
            lat2, lon2 = self.num2deg(max_x + 1, max_y + 1, 18)
            
            print(f"\n📍 覆盖范围:")
            print(f"   经度: {min(lon1, lon2):.6f} - {max(lon1, lon2):.6f}")
            print(f"   纬度: {min(lat1, lat2):.6f} - {max(lat1, lat2):.6f}")
            print(f"   瓦片范围: X({min_x}-{max_x}) Y({min_y}-{max_y})")
        
        return len(valid_tiles) > 0, valid_tiles
    
    def generate_level20_tile(self, tile_info):
        """生成单个20级瓦片"""
        z, x, y = tile_info
        
        try:
            start_time = time.time()
            bbox = self.get_tile_bbox(x, y, z)
            
            # 检查瓦片是否已存在
            tile_path = self.level20_dir / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            if tile_file.exists() and tile_file.stat().st_size > 1000:
                return True, f"已存在: {z}/{x}/{y}"
            
            # 处理OSM数据
            processor = Level20OSMProcessor(bbox, max_features=15000)
            processor.apply_file(str(self.osm_file))
            
            # 创建高分辨率图像 (512x512 for level 20)
            img = Image.new('RGB', (512, 512), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换 - 20级使用更高分辨率
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 512)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 512)
                return (max(0, min(511, px)), max(0, min(511, py)))
            
            # 绘制水体
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4', width=1)
            
            # 绘制建筑物 - 20级显示更多细节
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    building_type = building['tags'].get('building', 'yes')
                    if building_type in ['house', 'residential']:
                        fill_color = '#e6e6e6'
                    elif building_type in ['commercial', 'retail']:
                        fill_color = '#ffcccc'
                    elif building_type in ['industrial']:
                        fill_color = '#ccccff'
                    else:
                        fill_color = '#dddddd'
                    
                    draw.polygon(points, fill=fill_color, outline='#999999', width=1)
            
            # 绘制道路 - 20级显示所有道路类型
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 20级道路样式 - 更详细
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = 8
                elif highway_type in ['primary']:
                    color = '#ffcc00'
                    width = 6
                elif highway_type in ['secondary']:
                    color = '#ffff00'
                    width = 4
                elif highway_type in ['tertiary']:
                    color = '#ffffff'
                    width = 3
                elif highway_type in ['residential', 'unclassified']:
                    color = '#ffffff'
                    width = 2
                elif highway_type in ['service', 'track']:
                    color = '#cccccc'
                    width = 1
                elif highway_type in ['footway', 'path', 'cycleway']:
                    color = '#ff9999'
                    width = 1
                else:
                    color = '#eeeeee'
                    width = 1
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 绘制POI - 20级显示详细POI
            for poi in processor.pois:
                px, py = geo_to_pixel(poi['lon'], poi['lat'])
                
                amenity = poi['tags'].get('amenity', poi['tags'].get('shop', 'unknown'))
                if amenity in ['restaurant', 'cafe', 'fast_food']:
                    color = '#ff0000'
                elif amenity in ['hospital', 'clinic', 'pharmacy']:
                    color = '#00ff00'
                elif amenity in ['school', 'university']:
                    color = '#0000ff'
                elif amenity in ['bank', 'atm']:
                    color = '#ffff00'
                else:
                    color = '#ff00ff'
                
                draw.ellipse([px-3, py-3, px+3, py+3], fill=color, outline='#ffffff', width=1)
            
            # 添加详细信息
            elapsed = time.time() - start_time
            info_text = f"L20 {len(processor.roads)}R {len(processor.buildings)}B {len(processor.pois)}P"
            draw.text((5, 5), info_text, fill='#333333')
            
            # 缩放到标准尺寸
            img = img.resize((256, 256), Image.LANCZOS)
            
            # 保存瓦片
            img.save(tile_file, 'PNG', optimize=True, quality=95)
            
            elapsed = time.time() - start_time
            return True, f"生成成功: {z}/{x}/{y} ({elapsed:.1f}s) - {len(processor.roads)}R {len(processor.buildings)}B {len(processor.pois)}P"
            
        except Exception as e:
            return False, f"生成失败: {z}/{x}/{y} - {e}"
    
    def generate_level20_from_level18(self, max_workers=2):
        """基于18级瓦片生成20级瓦片"""
        print("\n🚀 生成20级超高精度瓦片")
        print("=" * 50)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return False
        
        # 检查18级瓦片
        has_level18, level18_tiles = self.check_level18_tiles()
        if not has_level18:
            print("❌ 没有有效的18级瓦片作为基础")
            return False
        
        print(f"\n📊 基于 {len(level18_tiles)} 个18级瓦片生成20级瓦片")
        
        # 为每个18级瓦片生成对应的20级瓦片
        level20_tasks = []
        
        for z18, x18, y18 in level18_tiles:
            # 每个18级瓦片对应16个20级瓦片 (2^(20-18) = 2^2 = 4, 所以是4x4=16个)
            scale_factor = 2 ** (20 - 18)  # = 4
            for dx in range(scale_factor):
                for dy in range(scale_factor):
                    x20 = x18 * scale_factor + dx
                    y20 = y18 * scale_factor + dy
                    level20_tasks.append((20, x20, y20))
        
        print(f"📊 需要生成 {len(level20_tasks)} 个20级瓦片")
        
        # 分批处理
        batch_size = 100
        total_success = 0
        total_failed = 0
        
        for i in range(0, len(level20_tasks), batch_size):
            batch = level20_tasks[i:i+batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(level20_tasks) + batch_size - 1) // batch_size
            
            print(f"\n🔄 批次 {batch_num}/{total_batches} ({len(batch)} 个瓦片)")
            
            # 使用线程池处理 (IO密集型)
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                results = list(executor.map(self.generate_level20_tile, batch))
                
                batch_success = sum(1 for success, _ in results if success)
                batch_failed = len(results) - batch_success
                
                total_success += batch_success
                total_failed += batch_failed
                
                print(f"   ✅ 批次完成: {batch_success}/{len(batch)} 成功")
                
                # 显示一些结果示例
                for success, message in results[:3]:
                    status = "✅" if success else "❌"
                    print(f"   {status} {message}")
                
                if len(results) > 3:
                    print(f"   ... 还有 {len(results) - 3} 个结果")
            
            # 批次间休息
            time.sleep(1)
        
        success_rate = (total_success / len(level20_tasks)) * 100 if level20_tasks else 0
        
        print(f"\n🎉 20级瓦片生成完成!")
        print(f"📊 总瓦片: {len(level20_tasks)}")
        print(f"📊 成功: {total_success}")
        print(f"📊 失败: {total_failed}")
        print(f"📊 成功率: {success_rate:.1f}%")
        print(f"\n📁 20级瓦片目录: {self.level20_dir}")
        
        return total_success > 0

def main():
    """主函数"""
    print("🎯 瓦片检查器和20级生成器")
    print("=" * 60)
    
    generator = TileCheckerAndLevel20Generator()
    
    try:
        # 检查18级瓦片
        has_valid_tiles, _ = generator.check_level18_tiles()
        
        if has_valid_tiles:
            print("\n🚀 开始生成20级瓦片...")
            
            # 使用适当的并行度
            cpu_count = multiprocessing.cpu_count()
            max_workers = min(3, cpu_count)  # 限制并行数
            
            success = generator.generate_level20_from_level18(max_workers=max_workers)
            
            if success:
                print("\n✅ 20级瓦片生成完成！")
                print("🌐 现在可以在地图系统中使用20级瓦片了")
            else:
                print("\n❌ 20级瓦片生成失败")
        else:
            print("\n❌ 无法生成20级瓦片，因为没有有效的18级瓦片")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断生成")
    except Exception as e:
        print(f"\n❌ 生成过程出错: {e}")

if __name__ == "__main__":
    main()
