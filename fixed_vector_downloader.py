#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版矢量数据下载器 - 解决HTTP 400错误
优化查询语句格式，减少超时问题
"""

import os
import time
import json
import math
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import xml.etree.ElementTree as ET
import threading

class FixedVectorDownloader:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.timeout = 60  # 减少超时时间
        self.max_retries = 3
        self.retry_delay = 5
        self.max_workers = 4  # 减少并发数，避免服务器压力
        self.data_dir = Path("static/vector_data")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建进度记录文件
        self.progress_file = "fixed_download_progress.json"
        self.load_progress()
        
        print(f"🔧 修复版下载器初始化完成")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
    
    def load_progress(self):
        """加载下载进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                self.progress = json.load(f)
        else:
            self.progress = {}
    
    def save_progress(self):
        """保存下载进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def validate_bbox(self, bbox):
        """验证边界框格式"""
        try:
            if isinstance(bbox, str):
                parts = bbox.split(',')
                if len(parts) != 4:
                    return False
                coords = [float(x.strip()) for x in parts]
            else:
                coords = bbox
            
            # 检查坐标范围
            if not (-180 <= coords[0] <= 180 and -180 <= coords[2] <= 180):
                return False
            if not (-90 <= coords[1] <= 90 and -90 <= coords[3] <= 90):
                return False
            
            # 检查边界框大小
            width = abs(coords[2] - coords[0])
            height = abs(coords[3] - coords[1])
            
            # 限制边界框大小，避免查询过大
            if width > 5.0 or height > 5.0:
                print(f"⚠️ 边界框过大: {width:.2f} x {height:.2f}度")
                return False
            
            return True
        except Exception as e:
            print(f"❌ 边界框验证失败: {e}")
            return False
    
    def get_safe_overpass_query(self, bbox, data_type):
        """构建安全的Overpass查询语句"""
        # 验证边界框
        if not self.validate_bbox(bbox):
            raise ValueError("无效的边界框")
        
        # 格式化边界框
        if isinstance(bbox, str):
            bbox_str = bbox
        else:
            bbox_str = f"{bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}"
        
        if data_type == "roads":
            return f"""
[out:xml][timeout:60];
(
  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)"]({bbox_str});
  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)_link"]({bbox_str});
);
out geom;
"""
        elif data_type == "buildings":
            return f"""
[out:xml][timeout:60];
(
  way["building"]({bbox_str});
  way["building:part"]({bbox_str});
  relation["building"]({bbox_str});
  way["amenity"~"^(school|hospital|university|restaurant|hotel|bank|police|park|garden)"]({bbox_str});
  way["shop"]({bbox_str});
  way["office"]({bbox_str});
  way["leisure"~"^(park|garden|playground|sports_centre|swimming_pool)"]({bbox_str});
);
out geom;
"""
        elif data_type == "pois":
            return f"""
[out:xml][timeout:60];
(
  node["amenity"]({bbox_str});
  node["shop"]({bbox_str});
  node["tourism"]({bbox_str});
  node["leisure"]({bbox_str});
  node["office"]({bbox_str});
  node["historic"]({bbox_str});
  node["natural"]({bbox_str});
);
out;
"""
        else:
            return f"""
[out:xml][timeout:60];
(
  way["highway"]({bbox_str});
  way["building"]({bbox_str});
  node["amenity"]({bbox_str});
);
out geom;
"""
    
    def download_with_retry(self, url, data, max_retries=None):
        """带重试机制的下载"""
        if max_retries is None:
            max_retries = self.max_retries
        
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt + 1}次)...")
                
                # 使用更保守的请求设置
                response = requests.post(
                    url, 
                    data=data, 
                    timeout=self.timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/xml, text/xml, */*',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 400:
                    print(f"  HTTP 400错误: 请求格式错误")
                    # 对于400错误，不重试，直接返回
                    return None
                elif response.status_code == 429:
                    print(f"  HTTP 429错误: 请求过于频繁")
                    # 对于429错误，等待更长时间
                    if attempt < max_retries - 1:
                        wait_time = self.retry_delay * (2 ** attempt)
                        print(f"  等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                else:
                    print(f"  HTTP错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"  超时错误 (第{attempt + 1}次)")
            except requests.exceptions.ConnectionError:
                print(f"  连接错误 (第{attempt + 1}次)")
            except Exception as e:
                print(f"  其他错误: {e} (第{attempt + 1}次)")
            
            if attempt < max_retries - 1:
                print(f"  等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)
        
        return None
    
    def download_region_data(self, region_name, bbox, data_types):
        """下载区域数据"""
        print(f"\n🗺️ 开始下载 {region_name} 矢量数据")
        
        region_dir = self.data_dir / region_name
        region_dir.mkdir(exist_ok=True)
        
        results = {}
        
        for data_type in data_types:
            print(f"\n📥 下载 {data_type} 数据...")
            
            # 检查是否已下载
            file_path = region_dir / f"{data_type}.osm"
            if file_path.exists():
                file_size = file_path.stat().st_size
                if file_size > 1000:  # 文件大小大于1KB
                    print(f"  ✅ {data_type} 数据已存在，跳过下载")
                    results[data_type] = "已存在"
                    continue
            
            try:
                # 构建查询
                query = self.get_safe_overpass_query(bbox, data_type)
                
                # 下载数据
                response = self.download_with_retry(self.base_url, {"data": query})
                
                if response and response.status_code == 200:
                    # 保存数据
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    
                    file_size = file_path.stat().st_size
                    print(f"  ✅ {data_type} 数据下载成功 ({file_size} 字节)")
                    results[data_type] = "成功"
                    
                    # 更新进度
                    self.progress[f"{region_name}_{data_type}"] = "completed"
                    self.save_progress()
                    
                else:
                    print(f"  ❌ {data_type} 数据下载失败")
                    results[data_type] = "失败"
                    
                    # 更新进度
                    self.progress[f"{region_name}_{data_type}"] = "failed"
                    self.save_progress()
                    
            except ValueError as e:
                print(f"  ❌ {data_type} 查询构建失败: {e}")
                results[data_type] = "查询错误"
                
                # 更新进度
                self.progress[f"{region_name}_{data_type}"] = "query_error"
                self.save_progress()
        
        return results
    
    def download_provinces(self, provinces, data_types):
        """下载多个省份数据"""
        print(f"🚀 开始下载 {len(provinces)} 个省份的矢量数据")
        print(f"📊 数据类型: {', '.join(data_types)}")
        print(f"⚡ 并发数: {self.max_workers}")
        print(f"⏱️ 超时时间: {self.timeout}秒")
        print(f"🔄 最大重试: {self.max_retries}次")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_region = {}
            for region_name, bbox in provinces.items():
                future = executor.submit(self.download_region_data, region_name, bbox, data_types)
                future_to_region[future] = region_name
            
            # 处理结果
            for future in as_completed(future_to_region):
                region_name = future_to_region[future]
                try:
                    result = future.result()
                    results[region_name] = result
                except Exception as e:
                    print(f"❌ {region_name} 下载异常: {e}")
                    results[region_name] = {"error": str(e)}
        
        return results
    
    def get_download_status(self):
        """获取下载状态"""
        status = {
            "completed": 0,
            "failed": 0,
            "query_error": 0,
            "pending": 0,
            "details": {}
        }
        
        for key, value in self.progress.items():
            if value == "completed":
                status["completed"] += 1
            elif value == "failed":
                status["failed"] += 1
            elif value == "query_error":
                status["query_error"] += 1
            else:
                status["pending"] += 1
            
            status["details"][key] = value
        
        return status
    
    def test_single_download(self, region_name, bbox, data_type):
        """测试单个下载"""
        print(f"🧪 测试下载 {region_name} 的 {data_type} 数据...")
        
        try:
            # 构建查询
            query = self.get_safe_overpass_query(bbox, data_type)
            print(f"📋 查询语句:")
            print(query)
            
            # 下载数据
            response = self.download_with_retry(self.base_url, {"data": query})
            
            if response and response.status_code == 200:
                print(f"✅ 测试下载成功")
                return True
            else:
                print(f"❌ 测试下载失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试下载异常: {e}")
            return False

def main():
    """主函数"""
    downloader = FixedVectorDownloader()
    
    # 测试用的省份配置（小范围）
    test_provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5"
    }
    
    # 完整省份配置
    provinces = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5",
        "天津": "116.8,38.5,118.0,40.2",
        "重庆": "105.0,28.0,110.0,32.0",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0"
    }
    
    # 数据类型
    data_types = ["roads", "buildings", "pois"]
    
    print("🔧 修复版矢量数据下载器")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 测试下载 (推荐先测试)")
        print("2. 下载测试省份数据")
        print("3. 下载完整省份数据")
        print("4. 查看下载状态")
        print("5. 清理进度文件")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            print("\n🧪 测试下载功能...")
            region_name = "北京"
            bbox = "116.0,39.4,117.0,40.2"
            data_type = "roads"
            
            success = downloader.test_single_download(region_name, bbox, data_type)
            if success:
                print("✅ 测试成功，可以继续下载")
            else:
                print("❌ 测试失败，请检查网络连接")
        
        elif choice == "2":
            print(f"\n🚀 开始下载测试省份数据...")
            results = downloader.download_provinces(test_provinces, data_types)
            
            print("\n📊 下载结果汇总:")
            for region, result in results.items():
                print(f"\n{region}:")
                for data_type, status in result.items():
                    if status == "成功":
                        print(f"  ✅ {data_type}: {status}")
                    elif status == "失败":
                        print(f"  ❌ {data_type}: {status}")
                    else:
                        print(f"  ⏭️ {data_type}: {status}")
        
        elif choice == "3":
            print(f"\n🚀 开始下载完整省份数据...")
            print("⚠️ 注意: 这将需要较长时间")
            confirm = input("确认继续? (y/N): ").strip().lower()
            
            if confirm == 'y':
                results = downloader.download_provinces(provinces, data_types)
                
                print("\n📊 下载结果汇总:")
                for region, result in results.items():
                    print(f"\n{region}:")
                    for data_type, status in result.items():
                        if status == "成功":
                            print(f"  ✅ {data_type}: {status}")
                        elif status == "失败":
                            print(f"  ❌ {data_type}: {status}")
                        else:
                            print(f"  ⏭️ {data_type}: {status}")
            else:
                print("❌ 已取消下载")
        
        elif choice == "4":
            status = downloader.get_download_status()
            print(f"\n📊 下载状态:")
            print(f"✅ 已完成: {status['completed']}")
            print(f"❌ 失败: {status['failed']}")
            print(f"🔧 查询错误: {status['query_error']}")
            print(f"⏳ 待处理: {status['pending']}")
            
            if status['details']:
                print(f"\n📋 详细信息:")
                for key, value in status['details'].items():
                    print(f"  {key}: {value}")
        
        elif choice == "5":
            if os.path.exists(downloader.progress_file):
                os.remove(downloader.progress_file)
                print("✅ 进度文件已清理")
            else:
                print("ℹ️ 进度文件不存在")
        
        elif choice == "6":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
