#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国矢量数据下载工具
支持下载省市级矢量地图数据，包括GeoJSON、Shapefile等格式
"""

import os
import requests
import json
import time
from pathlib import Path
import zipfile
import shutil

class ChinaVectorDownloader:
    def __init__(self, output_dir="static/vector_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 矢量数据源配置
        self.vector_sources = {
            'osm': {
                'name': 'OpenStreetMap',
                'overpass_url': 'https://overpass-api.de/api/interpreter',
                'format': 'osm'
            },
            'natural_earth': {
                'name': 'Natural Earth',
                'base_url': 'https://www.naturalearthdata.com/http//www.naturalearthdata.com/download',
                'format': 'shp'
            },
            'gadm': {
                'name': 'GADM',
                'base_url': 'https://gadm.org/data.html',
                'format': 'gpkg'
            }
        }
        
        # 省市级区域配置
        self.provinces = {
            '1': {'name': '北京市', 'bbox': (115.7, 39.4, 117.4, 41.6)},
            '2': {'name': '上海市', 'bbox': (120.9, 30.7, 122.1, 31.9)},
            '3': {'name': '天津市', 'bbox': (116.7, 38.6, 118.1, 40.2)},
            '4': {'name': '重庆市', 'bbox': (105.3, 28.2, 110.2, 32.2)},
            '5': {'name': '广东省', 'bbox': (109.7, 20.2, 117.2, 25.5)},
            '6': {'name': '江苏省', 'bbox': (116.2, 30.7, 121.9, 35.2)},
            '7': {'name': '浙江省', 'bbox': (118.0, 27.0, 123.0, 31.5)},
            '8': {'name': '山东省', 'bbox': (114.5, 34.4, 122.7, 38.4)},
            '9': {'name': '河南省', 'bbox': (110.4, 31.2, 116.7, 36.4)},
            '10': {'name': '四川省', 'bbox': (97.3, 26.0, 108.5, 34.3)},
            '11': {'name': '湖北省', 'bbox': (108.3, 29.0, 116.1, 33.3)},
            '12': {'name': '湖南省', 'bbox': (108.8, 24.6, 114.3, 30.1)},
            '13': {'name': '河北省', 'bbox': (113.5, 36.0, 119.8, 42.6)},
            '14': {'name': '福建省', 'bbox': (115.8, 23.5, 120.4, 28.3)},
            '15': {'name': '安徽省', 'bbox': (114.9, 29.4, 119.3, 34.7)},
            '16': {'name': '辽宁省', 'bbox': (118.3, 38.7, 125.3, 43.3)},
            '17': {'name': '江西省', 'bbox': (113.6, 24.5, 118.5, 30.0)},
            '18': {'name': '黑龙江省', 'bbox': (121.1, 43.4, 135.1, 53.6)},
            '19': {'name': '吉林省', 'bbox': (121.6, 40.9, 131.2, 46.3)},
            '20': {'name': '山西省', 'bbox': (110.2, 34.6, 114.6, 40.7)},
            '21': {'name': '陕西省', 'bbox': (105.5, 31.7, 111.3, 39.6)},
            '22': {'name': '甘肃省', 'bbox': (92.1, 32.1, 108.7, 42.8)},
            '23': {'name': '青海省', 'bbox': (89.4, 31.6, 103.1, 39.2)},
            '24': {'name': '云南省', 'bbox': (97.5, 21.1, 106.2, 29.2)},
            '25': {'name': '贵州省', 'bbox': (103.6, 24.6, 109.6, 29.2)},
            '26': {'name': '海南省', 'bbox': (108.6, 18.2, 111.1, 20.1)},
            '27': {'name': '台湾省', 'bbox': (119.3, 21.9, 122.0, 25.3)},
            '28': {'name': '内蒙古自治区', 'bbox': (97.2, 37.2, 126.0, 53.3)},
            '29': {'name': '新疆维吾尔自治区', 'bbox': (73.4, 34.3, 96.4, 48.9)},
            '30': {'name': '西藏自治区', 'bbox': (78.4, 27.4, 99.1, 36.5)},
            '31': {'name': '宁夏回族自治区', 'bbox': (104.2, 35.2, 107.6, 39.4)},
            '32': {'name': '广西壮族自治区', 'bbox': (104.3, 20.9, 112.0, 26.2)},
            '33': {'name': '香港特别行政区', 'bbox': (113.8, 22.2, 114.4, 22.6)},
            '34': {'name': '澳门特别行政区', 'bbox': (113.5, 22.1, 113.6, 22.2)}
        }
        
        self.cities = {
            '1': {'name': '北京市', 'bbox': (116.0, 39.4, 117.0, 40.2)},
            '2': {'name': '上海市', 'bbox': (121.2, 31.0, 121.8, 31.4)},
            '3': {'name': '广州市', 'bbox': (113.0, 22.7, 113.6, 23.4)},
            '4': {'name': '深圳市', 'bbox': (113.7, 22.4, 114.4, 22.8)},
            '5': {'name': '杭州市', 'bbox': (119.8, 30.0, 120.5, 30.5)},
            '6': {'name': '南京市', 'bbox': (118.4, 31.8, 119.2, 32.3)},
            '7': {'name': '成都市', 'bbox': (103.8, 30.4, 104.3, 30.8)},
            '8': {'name': '武汉市', 'bbox': (114.0, 30.4, 114.6, 30.8)},
            '9': {'name': '西安市', 'bbox': (108.7, 34.0, 109.2, 34.4)},
            '10': {'name': '重庆市', 'bbox': (106.2, 29.2, 107.0, 29.8)},
            '11': {'name': '天津市', 'bbox': (117.0, 38.8, 117.8, 39.4)},
            '12': {'name': '青岛市', 'bbox': (120.1, 35.9, 120.6, 36.3)},
            '13': {'name': '大连市', 'bbox': (121.3, 38.7, 121.9, 39.1)},
            '14': {'name': '厦门市', 'bbox': (117.9, 24.4, 118.3, 24.6)},
            '15': {'name': '苏州市', 'bbox': (120.4, 31.1, 121.0, 31.5)},
            '16': {'name': '无锡市', 'bbox': (120.1, 31.4, 120.6, 31.7)},
            '17': {'name': '宁波市', 'bbox': (121.3, 29.7, 121.9, 30.0)},
            '18': {'name': '长沙市', 'bbox': (112.7, 28.0, 113.2, 28.4)},
            '19': {'name': '郑州市', 'bbox': (113.4, 34.6, 114.0, 34.9)},
            '20': {'name': '沈阳市', 'bbox': (123.2, 41.6, 123.8, 42.0)}
        }
    
    def download_osm_vector_data(self, bbox, output_file, data_types=None):
        """下载OSM矢量数据"""
        if data_types is None:
            data_types = ['highway', 'building', 'waterway', 'natural', 'boundary']
        
        print(f"🗺️  下载OSM矢量数据")
        print(f"   边界框: {bbox}")
        print(f"   数据类型: {', '.join(data_types)}")
        print(f"   输出文件: {output_file}")
        
        min_lon, min_lat, max_lon, max_lat = bbox
        
        # 构建Overpass API查询
        query_parts = []
        for data_type in data_types:
            if data_type == 'highway':
                query_parts.append(f'way["highway"]({min_lat},{min_lon},{max_lat},{max_lon});')
            elif data_type == 'building':
                query_parts.append(f'way["building"]({min_lat},{min_lon},{max_lat},{max_lon});')
            elif data_type == 'waterway':
                query_parts.append(f'way["waterway"]({min_lat},{min_lon},{max_lat},{max_lon});')
            elif data_type == 'natural':
                query_parts.append(f'way["natural"]({min_lat},{min_lon},{max_lat},{max_lon});')
            elif data_type == 'boundary':
                query_parts.append(f'relation["boundary"="administrative"]({min_lat},{min_lon},{max_lat},{max_lon});')
        
        query = f"""
        [out:xml][timeout:300];
        (
          {''.join(query_parts)}
        );
        out geom;
        """
        
        try:
            response = requests.post(
                self.vector_sources['osm']['overpass_url'],
                data=query,
                timeout=300,
                headers={'User-Agent': 'ChinaVectorDownloader/1.0'}
            )
            response.raise_for_status()
            
            # 保存数据
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"   ✅ 下载完成: {file_size:,} bytes")
            return True, f"OSM矢量数据下载成功 ({file_size:,} bytes)"
            
        except Exception as e:
            return False, f"OSM矢量数据下载失败: {e}"
    
    def download_china_admin_boundaries(self, output_file):
        """下载中国行政区划边界数据"""
        print(f"🗺️  下载中国行政区划边界数据")
        print(f"   输出文件: {output_file}")
        
        # 使用Overpass API查询中国行政区划
        query = """
        [out:xml][timeout:300];
        (
          relation["ISO3166-1"="CN"]["admin_level"];
          relation["boundary"="administrative"]["admin_level"~"^[2-4]$"](area);
        );
        out geom;
        """
        
        try:
            response = requests.post(
                self.vector_sources['osm']['overpass_url'],
                data=query,
                timeout=300,
                headers={'User-Agent': 'ChinaVectorDownloader/1.0'}
            )
            response.raise_for_status()
            
            # 保存数据
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"   ✅ 下载完成: {file_size:,} bytes")
            return True, f"中国行政区划数据下载成功 ({file_size:,} bytes)"
            
        except Exception as e:
            return False, f"中国行政区划数据下载失败: {e}"
    
    def download_province_vector_data(self, province_name, bbox, data_types=None):
        """下载省级矢量数据"""
        print(f"🗺️  下载{province_name}矢量数据")
        
        # 创建省份目录
        province_dir = self.output_dir / province_name
        province_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载OSM矢量数据
        osm_file = province_dir / f"{province_name}_osm.xml"
        success, message = self.download_osm_vector_data(bbox, osm_file, data_types)
        
        if success:
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
        
        return success
    
    def download_city_vector_data(self, city_name, bbox, data_types=None):
        """下载市级矢量数据"""
        print(f"🗺️  下载{city_name}矢量数据")
        
        # 创建城市目录
        city_dir = self.output_dir / city_name
        city_dir.mkdir(parents=True, exist_ok=True)
        
        # 下载OSM矢量数据
        osm_file = city_dir / f"{city_name}_osm.xml"
        success, message = self.download_osm_vector_data(bbox, osm_file, data_types)
        
        if success:
            print(f"   ✅ {message}")
        else:
            print(f"   ❌ {message}")
        
        return success
    
    def show_provinces(self):
        """显示省份列表"""
        print("\n📋 请选择省份/直辖市:")
        print("-" * 40)
        
        for key, province in self.provinces.items():
            print(f"{key:2}. {province['name']}")
        
        print("0. 返回主菜单")
    
    def show_cities(self):
        """显示城市列表"""
        print("\n📋 请选择城市:")
        print("-" * 40)
        
        for key, city in self.cities.items():
            print(f"{key:2}. {city['name']}")
        
        print("0. 返回主菜单")
    
    def show_data_types(self):
        """显示数据类型选项"""
        print("\n📋 请选择数据类型:")
        print("-" * 40)
        print("1. 道路数据 (highway)")
        print("2. 建筑物数据 (building)")
        print("3. 水体数据 (waterway)")
        print("4. 自然要素 (natural)")
        print("5. 行政区划 (boundary)")
        print("6. 全部数据")
        print("0. 返回主菜单")
    
    def get_data_types(self, choice):
        """根据选择获取数据类型"""
        if choice == '1':
            return ['highway']
        elif choice == '2':
            return ['building']
        elif choice == '3':
            return ['waterway']
        elif choice == '4':
            return ['natural']
        elif choice == '5':
            return ['boundary']
        elif choice == '6':
            return ['highway', 'building', 'waterway', 'natural', 'boundary']
        else:
            return ['highway', 'building', 'waterway', 'natural', 'boundary']

def main():
    """主函数"""
    print("🗺️  中国矢量数据下载工具")
    print("=" * 50)
    
    downloader = ChinaVectorDownloader()
    
    while True:
        print("\n" + "="*60)
        print("🗺️  中国矢量数据下载工具")
        print("="*60)
        print("1. 下载省级矢量数据")
        print("2. 下载市级矢量数据")
        print("3. 下载中国行政区划边界")
        print("4. 测试矢量数据下载")
        print("0. 退出")
        print("="*60)
        
        choice = input("请选择功能 (0-4): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            # 下载省级矢量数据
            downloader.show_provinces()
            province_choice = input("\n请选择省份 (输入编号): ").strip()
            
            if province_choice == '0':
                continue
            
            if province_choice not in downloader.provinces:
                print("❌ 无效选择")
                continue
            
            province = downloader.provinces[province_choice]
            print(f"\n已选择: {province['name']}")
            
            # 选择数据类型
            downloader.show_data_types()
            data_choice = input("\n请选择数据类型 (默认: 6): ").strip() or "6"
            data_types = downloader.get_data_types(data_choice)
            
            downloader.download_province_vector_data(province['name'], province['bbox'], data_types)
            
        elif choice == '2':
            # 下载市级矢量数据
            downloader.show_cities()
            city_choice = input("\n请选择城市 (输入编号): ").strip()
            
            if city_choice == '0':
                continue
            
            if city_choice not in downloader.cities:
                print("❌ 无效选择")
                continue
            
            city = downloader.cities[city_choice]
            print(f"\n已选择: {city['name']}")
            
            # 选择数据类型
            downloader.show_data_types()
            data_choice = input("\n请选择数据类型 (默认: 6): ").strip() or "6"
            data_types = downloader.get_data_types(data_choice)
            
            downloader.download_city_vector_data(city['name'], city['bbox'], data_types)
            
        elif choice == '3':
            # 下载中国行政区划边界
            output_file = downloader.output_dir / "china_admin_boundaries.xml"
            downloader.download_china_admin_boundaries(output_file)
            
        elif choice == '4':
            # 测试矢量数据下载
            print("🧪 测试矢量数据下载")
            test_bbox = (116.0, 39.4, 117.0, 40.2)  # 北京市
            test_file = downloader.output_dir / "test_beijing.xml"
            success, message = downloader.download_osm_vector_data(test_bbox, test_file)
            print(f"测试结果: {message}")
            
        else:
            print("❌ 无效选择")
        
        input("\n按回车键继续...")
    
    print(f"\n✅ 完成! 数据保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
