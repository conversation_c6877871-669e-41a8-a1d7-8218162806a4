#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边界框问题分析工具
分析为什么北京市数据正常而其他地区不行
"""

import requests
import time

def test_bbox_coordinates():
    """测试不同地区的边界框坐标"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同地区的边界框
    test_regions = {
        "北京": "116.0,39.4,117.0,40.2",
        "上海": "121.0,31.0,122.0,31.5", 
        "天津": "116.8,38.5,118.0,40.2",
        "重庆": "105.0,28.0,110.0,32.0",
        "广东": "109.0,20.0,117.0,25.0",
        "江苏": "116.0,30.0,122.0,35.0",
        "浙江": "118.0,27.0,123.0,31.0",
        "山东": "114.0,34.0,123.0,38.0",
        "河南": "110.0,31.0,117.0,36.0",
        "四川": "97.0,26.0,109.0,34.0"
    }
    
    print("🔍 测试不同地区的边界框坐标...")
    print("=" * 60)
    
    for region, bbox in test_regions.items():
        print(f"\n📍 测试 {region}: {bbox}")
        
        # 计算边界框大小
        coords = [float(x) for x in bbox.split(',')]
        width = abs(coords[2] - coords[0])
        height = abs(coords[3] - coords[1])
        area = width * height
        
        print(f"   边界框大小: {width:.2f} x {height:.2f}度 (面积: {area:.2f}度²)")
        
        # 测试查询
        query = f"[out:xml][timeout:30];(way[\"highway\"=\"primary\"]({bbox}););out geom;"
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 成功: {len(response.content)} 字节")
            else:
                print(f"   ❌ 失败: HTTP {response.status_code}")
                if response.status_code == 400:
                    print(f"   错误内容: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        time.sleep(2)

def test_bbox_size_limits():
    """测试边界框大小限制"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同大小的边界框
    test_sizes = [
        ("小范围", "116.0,39.4,116.5,39.9"),      # 0.5 x 0.5度
        ("中范围", "116.0,39.4,117.0,40.2"),      # 1.0 x 0.8度 (北京)
        ("大范围", "116.0,39.4,118.0,41.0"),      # 2.0 x 1.6度
        ("超大范围", "116.0,39.4,120.0,43.0"),    # 4.0 x 3.6度
        ("极大范围", "110.0,30.0,120.0,40.0"),    # 10.0 x 10.0度
    ]
    
    print("\n📏 测试边界框大小限制...")
    print("=" * 60)
    
    for size_name, bbox in test_sizes:
        print(f"\n📐 测试 {size_name}: {bbox}")
        
        # 计算边界框大小
        coords = [float(x) for x in bbox.split(',')]
        width = abs(coords[2] - coords[0])
        height = abs(coords[3] - coords[1])
        area = width * height
        
        print(f"   边界框大小: {width:.2f} x {height:.2f}度 (面积: {area:.2f}度²)")
        
        # 测试查询
        query = f"[out:xml][timeout:30];(way[\"highway\"=\"primary\"]({bbox}););out geom;"
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 成功: {len(response.content)} 字节")
            else:
                print(f"   ❌ 失败: HTTP {response.status_code}")
                if response.status_code == 400:
                    print(f"   错误内容: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        time.sleep(2)

def test_coordinate_ranges():
    """测试坐标范围"""
    base_url = "https://overpass-api.de/api/interpreter"
    
    # 测试不同坐标范围
    test_coords = [
        ("北京区域", "116.0,39.4,117.0,40.2"),      # 正常范围
        ("上海区域", "121.0,31.0,122.0,31.5"),      # 正常范围
        ("新疆区域", "73.0,34.0,96.0,49.0"),        # 大范围
        ("西藏区域", "78.0,27.0,99.0,36.0"),        # 大范围
        ("海南区域", "108.0,18.0,111.0,20.0"),      # 小范围
        ("黑龙江区域", "121.0,43.0,135.0,53.0"),    # 大范围
    ]
    
    print("\n🗺️ 测试不同坐标范围...")
    print("=" * 60)
    
    for region, bbox in test_coords:
        print(f"\n🌍 测试 {region}: {bbox}")
        
        # 计算边界框大小
        coords = [float(x) for x in bbox.split(',')]
        width = abs(coords[2] - coords[0])
        height = abs(coords[3] - coords[1])
        area = width * height
        
        print(f"   边界框大小: {width:.2f} x {height:.2f}度 (面积: {area:.2f}度²)")
        
        # 检查坐标是否在合理范围内
        if any(coord < -180 or coord > 180 for coord in [coords[0], coords[2]]):
            print(f"   ⚠️ 经度超出范围 (-180到180)")
        if any(coord < -90 or coord > 90 for coord in [coords[1], coords[3]]):
            print(f"   ⚠️ 纬度超出范围 (-90到90)")
        
        # 测试查询
        query = f"[out:xml][timeout:30];(way[\"highway\"=\"primary\"]({bbox}););out geom;"
        
        try:
            response = requests.post(
                base_url,
                data={"data": query},
                timeout=30
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 成功: {len(response.content)} 字节")
            else:
                print(f"   ❌ 失败: HTTP {response.status_code}")
                if response.status_code == 400:
                    print(f"   错误内容: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        time.sleep(2)

def analyze_beijing_success():
    """分析北京成功的原因"""
    print("\n🔍 分析北京成功的原因...")
    print("=" * 60)
    
    beijing_bbox = "116.0,39.4,117.0,40.2"
    coords = [float(x) for x in beijing_bbox.split(',')]
    width = abs(coords[2] - coords[0])
    height = abs(coords[3] - coords[1])
    area = width * height
    
    print(f"📍 北京边界框: {beijing_bbox}")
    print(f"📏 边界框大小: {width:.2f} x {height:.2f}度 (面积: {area:.2f}度²)")
    print(f"🌍 坐标范围: 经度 {coords[0]}-{coords[2]}, 纬度 {coords[1]}-{coords[3]}")
    
    # 分析可能的原因
    print(f"\n💡 北京成功的可能原因:")
    print(f"   1. 边界框大小适中 ({area:.2f}度²)")
    print(f"   2. 坐标范围合理 (经度: {coords[0]}-{coords[2]}, 纬度: {coords[1]}-{coords[3]})")
    print(f"   3. 数据密度适中")
    print(f"   4. 网络连接稳定")
    
    # 建议其他地区的边界框调整
    print(f"\n🔧 建议其他地区的边界框调整:")
    print(f"   1. 减少边界框大小到 1-2 度²")
    print(f"   2. 分割大区域为小区域")
    print(f"   3. 使用更精确的坐标")
    print(f"   4. 分批下载数据")

def main():
    """主函数"""
    print("🔍 边界框问题分析工具")
    print("=" * 60)
    
    while True:
        print("\n📋 请选择分析类型:")
        print("1. 测试不同地区的边界框坐标")
        print("2. 测试边界框大小限制")
        print("3. 测试坐标范围")
        print("4. 分析北京成功的原因")
        print("5. 全部分析")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            test_bbox_coordinates()
        
        elif choice == "2":
            test_bbox_size_limits()
        
        elif choice == "3":
            test_coordinate_ranges()
        
        elif choice == "4":
            analyze_beijing_success()
        
        elif choice == "5":
            test_bbox_coordinates()
            test_bbox_size_limits()
            test_coordinate_ranges()
            analyze_beijing_success()
        
        elif choice == "6":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
