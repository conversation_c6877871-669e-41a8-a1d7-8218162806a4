#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 模拟GPS和PLC数据
用于在没有实际硬件的情况下测试系统功能
"""

import time
import random
import math
from datetime import datetime

class MockDataReceiver:
    """模拟数据接收器"""
    
    def __init__(self):
        self.running = False
        # 模拟GPS起始位置 (北京)
        self.base_lat = 39.9042
        self.base_lng = 116.4074
        self.time_offset = 0
        
    def start(self):
        """启动模拟数据接收"""
        self.running = True
        print("模拟数据接收器已启动")
    
    def get_gps_data(self):
        """生成模拟GPS数据"""
        if not self.running:
            return None
            
        # 模拟GPS轨迹 (圆形运动)
        self.time_offset += 0.1
        radius = 0.001  # 约100米半径
        
        lat = self.base_lat + radius * math.sin(self.time_offset)
        lng = self.base_lng + radius * math.cos(self.time_offset)
        
        return {
            'lat': lat,
            'lng': lng,
            'altitude': random.uniform(50, 100),
            'speed': random.uniform(0, 5)
        }
    
    def get_plc_data(self):
        """生成模拟PLC数据"""
        if not self.running:
            return None
            
        # 模拟气体浓度变化
        co = random.uniform(0, 30)  # CO浓度
        h2s = random.uniform(0, 15)  # H2S浓度
        ch4 = random.uniform(0, 20)  # CH4浓度
        o2 = random.uniform(18, 22)  # O2浓度
        
        # 模拟风机状态 (随机开关)
        fan1_running = random.choice([True, False])
        fan2_running = random.choice([True, False])
        
        # 模拟环境数据
        temperature = random.uniform(20, 35)
        humidity = random.uniform(40, 80)
        
        return {
            'gas_levels': {
                'CO': co,
                'H2S': h2s,
                'CH4': ch4,
                'O2': o2
            },
            'fan_status': {
                'fan1': fan1_running,
                'fan2': fan2_running
            },
            'temperature': temperature,
            'humidity': humidity
        }
    
    def stop(self):
        """停止模拟数据接收"""
        self.running = False
        print("模拟数据接收器已停止")

def test_data_generation():
    """测试数据生成功能"""
    print("=== 测试数据生成功能 ===")
    
    mock_receiver = MockDataReceiver()
    mock_receiver.start()
    
    for i in range(5):
        print(f"\n--- 第 {i+1} 次数据采集 ---")
        
        # 获取GPS数据
        gps_data = mock_receiver.get_gps_data()
        if gps_data:
            print(f"GPS数据: 纬度={gps_data['lat']:.6f}, 经度={gps_data['lng']:.6f}")
            print(f"         海拔={gps_data['altitude']:.1f}m, 速度={gps_data['speed']:.1f}km/h")
        
        # 获取PLC数据
        plc_data = mock_receiver.get_plc_data()
        if plc_data:
            gas = plc_data['gas_levels']
            print(f"气体数据: CO={gas['CO']:.1f}ppm, H2S={gas['H2S']:.1f}ppm")
            print(f"         CH4={gas['CH4']:.1f}%LEL, O2={gas['O2']:.1f}%")
            
            fan = plc_data['fan_status']
            print(f"风机状态: 风机1={'运行' if fan['fan1'] else '停止'}, 风机2={'运行' if fan['fan2'] else '停止'}")
            
            print(f"环境数据: 温度={plc_data['temperature']:.1f}°C, 湿度={plc_data['humidity']:.1f}%")
        
        time.sleep(2)
    
    mock_receiver.stop()

def test_gas_thresholds():
    """测试气体阈值判断"""
    print("\n=== 测试气体阈值判断 ===")
    
    thresholds = {
        'CO': {'warning': 25, 'danger': 50},
        'H2S': {'warning': 10, 'danger': 20},
        'CH4': {'warning': 10, 'danger': 25},
        'O2': {'warning': 19, 'danger': 16}
    }
    
    test_values = {
        'CO': [5, 30, 60],
        'H2S': [2, 15, 25],
        'CH4': [3, 15, 30],
        'O2': [21, 18, 15]
    }
    
    for gas, values in test_values.items():
        print(f"\n{gas} 阈值测试:")
        threshold = thresholds[gas]
        
        for value in values:
            if gas == 'O2':
                # 氧气浓度判断 (低氧危险)
                if value <= threshold['danger']:
                    status = '危险'
                elif value <= threshold['warning']:
                    status = '警告'
                else:
                    status = '正常'
            else:
                # 其他气体浓度判断 (高浓度危险)
                if value >= threshold['danger']:
                    status = '危险'
                elif value >= threshold['warning']:
                    status = '警告'
                else:
                    status = '正常'
            
            unit = '%' if gas == 'O2' else ('%LEL' if gas == 'CH4' else 'ppm')
            print(f"  {value}{unit} -> {status}")

def test_modbus_simulation():
    """测试MODBUS数据模拟"""
    print("\n=== 测试MODBUS数据模拟 ===")
    
    # 模拟MODBUS寄存器数据
    def simulate_modbus_registers():
        # 气体浓度寄存器 (保持寄存器)
        gas_registers = [
            int(random.uniform(0, 30) * 100),  # CO * 100
            0,  # CO高位
            int(random.uniform(0, 15) * 100),  # H2S * 100
            0,  # H2S高位
            int(random.uniform(0, 20) * 100),  # CH4 * 100
            0,  # CH4高位
            int(random.uniform(18, 22) * 100), # O2 * 100
            0   # O2高位
        ]
        
        # 风机状态 (线圈)
        fan_coils = [
            random.choice([True, False]),  # 风机1
            random.choice([True, False])   # 风机2
        ]
        
        # 环境数据 (输入寄存器)
        env_registers = [
            int(random.uniform(20, 35) * 10),  # 温度 * 10
            int(random.uniform(40, 80) * 10)   # 湿度 * 10
        ]
        
        return gas_registers, fan_coils, env_registers
    
    for i in range(3):
        print(f"\n--- MODBUS数据模拟 {i+1} ---")
        gas_regs, fan_coils, env_regs = simulate_modbus_registers()
        
        print(f"气体寄存器: {gas_regs}")
        print(f"风机线圈: {fan_coils}")
        print(f"环境寄存器: {env_regs}")
        
        # 解析数据
        co = gas_regs[0] / 100.0
        h2s = gas_regs[2] / 100.0
        ch4 = gas_regs[4] / 100.0
        o2 = gas_regs[6] / 100.0
        
        temp = env_regs[0] / 10.0
        humidity = env_regs[1] / 10.0
        
        print(f"解析结果:")
        print(f"  气体: CO={co}ppm, H2S={h2s}ppm, CH4={ch4}%LEL, O2={o2}%")
        print(f"  风机: 1={'运行' if fan_coils[0] else '停止'}, 2={'运行' if fan_coils[1] else '停止'}")
        print(f"  环境: 温度={temp}°C, 湿度={humidity}%")

if __name__ == '__main__':
    print("野外深机井监控系统 - 测试脚本")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_data_generation()
        test_gas_thresholds()
        test_modbus_simulation()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")