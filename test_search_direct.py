#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试搜索功能
"""

import requests
import json

def test_search_direct():
    """直接测试搜索功能"""
    print("🔍 直接测试搜索功能")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试查询
    test_queries = ["四川", "北京", "天安门", "九寨沟"]
    
    for query in test_queries:
        print(f"\n📝 测试查询: '{query}'")
        
        try:
            # 测试搜索API
            response = requests.get(f"{base_url}/api/search-poi?q={query}")
            print(f"  HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                results = response.json()
                print(f"  结果数量: {len(results)}")
                
                if results:
                    for result in results[:3]:  # 显示前3个
                        print(f"    - {result.get('name', 'N/A')} ({result.get('type', 'N/A')}) - {result.get('region', 'N/A')}")
                else:
                    print(f"    ❌ 无结果")
                    
                    # 检查响应内容
                    print(f"  响应内容: {response.text[:200]}...")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                print(f"  响应内容: {response.text}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 测试其他API
    print(f"\n🔍 测试其他API:")
    
    try:
        # 测试著名地标API
        response = requests.get(f"{base_url}/api/famous-places")
        if response.status_code == 200:
            places = response.json()
            print(f"  ✅ 著名地标API: {len(places)} 个")
        else:
            print(f"  ❌ 著名地标API: HTTP {response.status_code}")
    except Exception as e:
        print(f"  ❌ 著名地标API异常: {e}")
    
    try:
        # 测试区域API
        response = requests.get(f"{base_url}/api/regions")
        if response.status_code == 200:
            regions = response.json()
            print(f"  ✅ 区域API: {len(regions)} 个")
            if "四川" in regions:
                print(f"    - 四川区域存在")
            else:
                print(f"    - 四川区域不存在")
        else:
            print(f"  ❌ 区域API: HTTP {response.status_code}")
    except Exception as e:
        print(f"  ❌ 区域API异常: {e}")

if __name__ == "__main__":
    test_search_direct()
