#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查超高精度数据结构
"""

import json
import os

def check_data_structure():
    """检查恩施超高精度数据结构"""
    data_file = os.path.join('static', 'ultra_precision_data', '恩施_ultra_precision.json')
    
    if not os.path.exists(data_file):
        print("❌ 数据文件不存在")
        return
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("🎯 恩施超高精度数据结构检查")
        print("=" * 50)
        
        # 检查元数据
        if 'metadata' in data:
            metadata = data['metadata']
            print(f"✅ 元数据:")
            print(f"   - 总要素数: {metadata.get('total_features', 'N/A')}")
            print(f"   - 坐标精度: {metadata.get('coordinate_precision', 'N/A')}位小数")
            print(f"   - 理论精度: ±{metadata.get('accuracy_meters', 'N/A')}米")
            print(f"   - 中心坐标: {metadata.get('center', 'N/A')}")
        else:
            print("❌ 缺少元数据")
        
        # 检查道路数据
        if 'roads' in data and 'features' in data['roads']:
            roads_count = len(data['roads']['features'])
            print(f"✅ 道路数据: {roads_count}个要素")
            if roads_count > 0:
                sample_road = data['roads']['features'][0]
                print(f"   - 示例道路: {sample_road.get('properties', {}).get('name', '未命名')}")
                print(f"   - 道路类型: {sample_road.get('properties', {}).get('highway', '未知')}")
        else:
            print("❌ 缺少道路数据")
        
        # 检查建筑数据
        if 'buildings' in data and 'features' in data['buildings']:
            buildings_count = len(data['buildings']['features'])
            print(f"✅ 建筑数据: {buildings_count}个要素")
            if buildings_count > 0:
                sample_building = data['buildings']['features'][0]
                print(f"   - 示例建筑: {sample_building.get('properties', {}).get('name', '未命名')}")
                print(f"   - 建筑类型: {sample_building.get('properties', {}).get('building', '未知')}")
        else:
            print("❌ 缺少建筑数据")
        
        # 检查POI数据
        if 'pois' in data and 'features' in data['pois']:
            pois_count = len(data['pois']['features'])
            print(f"✅ POI数据: {pois_count}个要素")
            if pois_count > 0:
                sample_poi = data['pois']['features'][0]
                print(f"   - 示例POI: {sample_poi.get('properties', {}).get('name', '未命名')}")
                print(f"   - POI类型: {sample_poi.get('properties', {}).get('type', '未知')}")
        else:
            print("❌ 缺少POI数据")
        
        # 检查机井数据
        if 'wells' in data and 'features' in data['wells']:
            wells_count = len(data['wells']['features'])
            print(f"✅ 机井数据: {wells_count}个要素")
            if wells_count > 0:
                sample_well = data['wells']['features'][0]
                print(f"   - 示例机井: {sample_well.get('properties', {}).get('name', '未命名')}")
                print(f"   - 机井状态: {sample_well.get('properties', {}).get('status', '未知')}")
        else:
            print("❌ 缺少机井数据")
        
        print("\n" + "=" * 50)
        print("🎯 数据结构检查完成")
        
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")

if __name__ == "__main__":
    check_data_structure()
