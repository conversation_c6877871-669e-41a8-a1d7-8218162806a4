#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全国矢量地图系统启动器
集成数据下载和地图查看功能
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_data_status():
    """检查数据状态"""
    vector_dir = Path("static/vector_data")
    
    if not vector_dir.exists():
        print("❌ 矢量数据目录不存在")
        return False, 0, 0
    
    regions = [d for d in vector_dir.iterdir() if d.is_dir()]
    total_files = 0
    total_size = 0
    
    for region in regions:
        files = list(region.glob("*.xml"))
        total_files += len(files)
        total_size += sum(f.stat().st_size for f in files)
    
    return True, len(regions), total_files, total_size

def show_data_status():
    """显示数据状态"""
    print("📊 数据状态检查")
    print("=" * 40)
    
    has_data, region_count, file_count, total_size = check_data_status()
    
    if has_data:
        print(f"✅ 已下载区域: {region_count} 个")
        print(f"✅ 数据文件: {file_count} 个")
        print(f"✅ 总大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
        
        # 显示具体区域
        vector_dir = Path("static/vector_data")
        regions = [d for d in vector_dir.iterdir() if d.is_dir()]
        
        print(f"\n📁 已下载区域:")
        for region in regions:
            files = list(region.glob("*.xml"))
            region_size = sum(f.stat().st_size for f in files)
            print(f"   - {region.name}: {len(files)} 个文件 ({region_size/1024/1024:.1f} MB)")
    else:
        print("❌ 没有找到矢量数据")
        print("   请先下载矢量数据")

def start_map_viewer():
    """启动地图查看器"""
    print("🚀 启动矢量地图查看器...")
    print("   访问地址: http://localhost:5000")
    print("   按 Ctrl+C 停止服务")
    
    try:
        from vector_map_viewer import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 地图查看器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_national_downloader():
    """启动全国数据下载器"""
    print("📥 启动全国矢量数据下载工具...")
    
    try:
        subprocess.run([sys.executable, "national_vector_downloader.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 下载已取消")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_simple_downloader():
    """启动简单数据下载器"""
    print("📥 启动矢量数据下载工具...")
    
    try:
        subprocess.run([sys.executable, "simple_vector_downloader.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 下载已取消")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🗺️  全国矢量地图系统")
    print("="*60)
    print("1. 启动地图查看器")
    print("2. 下载全国矢量数据")
    print("3. 下载省市级矢量数据")
    print("4. 查看数据状态")
    print("5. 测试天安门定位")
    print("0. 退出")
    print("="*60)

def test_tiananmen():
    """测试天安门定位"""
    print("🧪 测试天安门定位功能...")
    
    try:
        subprocess.run([sys.executable, "test_tiananmen.py"], check=True)
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🗺️  全国矢量地图系统")
    print("=" * 60)
    
    # 显示初始状态
    show_data_status()
    
    while True:
        show_menu()
        choice = input("请选择功能 (0-5): ").strip()
        
        if choice == '0':
            print("👋 再见!")
            break
        elif choice == '1':
            # 启动地图查看器
            has_data, _, _, _ = check_data_status()
            if has_data:
                start_map_viewer()
            else:
                print("❌ 没有矢量数据，请先下载数据")
                input("按回车键继续...")
        elif choice == '2':
            # 下载全国矢量数据
            start_national_downloader()
        elif choice == '3':
            # 下载省市级矢量数据
            start_simple_downloader()
        elif choice == '4':
            # 查看数据状态
            show_data_status()
            input("按回车键继续...")
        elif choice == '5':
            # 测试天安门定位
            test_tiananmen()
            input("按回车键继续...")
        else:
            print("❌ 无效选择")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
