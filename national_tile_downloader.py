#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全国瓦片下载工具
支持百度地图、高德地图、OSM等多种地图源
专门用于下载18级全国瓦片作为离线地图
"""

import os
import requests
import math
import time
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlencode
import threading
from queue import Queue

class NationalTileDownloader:
    def __init__(self, output_dir="static/tiles"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 地图源配置
        self.tile_sources = {
            'osm': {
                'name': 'OpenStreetMap',
                'url_template': 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                'max_zoom': 19
            },
            'baidu': {
                'name': '百度地图',
                'url_template': 'https://maponline0.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20200101',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://map.baidu.com/'
                },
                'max_zoom': 19
            },
            'amap': {
                'name': '高德地图',
                'url_template': 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
                'subdomains': ['1', '2', '3', '4'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://ditu.amap.com/'
                },
                'max_zoom': 18
            },
            'tencent': {
                'name': '腾讯地图',
                'url_template': 'https://rt{}.map.gtimg.com/tile?z={z}&x={x}&y={y}&type=vector&styleid=3',
                'subdomains': ['0', '1', '2', '3'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://map.qq.com/'
                },
                'max_zoom': 18
            },
            'cartodb_light': {
                'name': 'CartoDB Light',
                'url_template': 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
                'subdomains': ['a', 'b', 'c', 'd'],
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                'max_zoom': 18
            }
        }
        
        self.current_source = 'osm'
        self.download_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """将经纬度转换为瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return x, y
    
    def get_tile_url(self, x, y, z, source=None):
        """获取瓦片URL"""
        if source is None:
            source = self.current_source
            
        config = self.tile_sources[source]
        url_template = config['url_template']
        
        # 处理子域名轮换
        if 'subdomains' in config:
            subdomain = config['subdomains'][(x + y) % len(config['subdomains'])]
            url_template = url_template.replace('{s}', subdomain)
        
        return url_template.format(x=x, y=y, z=z)
    
    def download_tile(self, x, y, z, source=None, retries=3):
        """下载单个瓦片"""
        if source is None:
            source = self.current_source
            
        config = self.tile_sources[source]
        url = self.get_tile_url(x, y, z, source)
        
        # 创建目录
        tile_dir = self.output_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        
        tile_path = tile_dir / f"{y}.png"
        
        # 如果文件已存在且大小合理，跳过下载
        if tile_path.exists() and tile_path.stat().st_size > 2000:
            self.download_stats['skipped'] += 1
            return True, f"跳过 {z}/{x}/{y}"
        
        for attempt in range(retries):
            try:
                response = requests.get(url, headers=config['headers'], timeout=20)
                response.raise_for_status()
                
                # 检查响应内容
                if len(response.content) < 1000:
                    if attempt < retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        self.download_stats['failed'] += 1
                        return False, f"瓦片 {z}/{x}/{y} 内容过小"
                
                # 保存文件
                with open(tile_path, 'wb') as f:
                    f.write(response.content)
                
                self.download_stats['success'] += 1
                return True, f"下载 {z}/{x}/{y} ({len(response.content)} bytes)"
                
            except Exception as e:
                if attempt < retries - 1:
                    time.sleep(3)
                    continue
                else:
                    self.download_stats['failed'] += 1
                    return False, f"下载 {z}/{x}/{y} 失败: {e}"
        
        self.download_stats['failed'] += 1
        return False, f"下载 {z}/{x}/{y} 失败，已重试{retries}次"
    
    def get_china_bounds(self):
        """获取中国边界框"""
        return {
            'min_lat': 3.86,   # 最南端
            'max_lat': 53.55,  # 最北端
            'min_lon': 73.66,  # 最西端
            'max_lon': 135.05  # 最东端
        }
    
    def calculate_tile_range(self, zoom):
        """计算指定缩放级别的瓦片范围"""
        bounds = self.get_china_bounds()
        
        min_x, max_y = self.deg2num(bounds['max_lat'], bounds['min_lon'], zoom)
        max_x, min_y = self.deg2num(bounds['min_lat'], bounds['max_lon'], zoom)
        
        # 确保范围正确
        if min_x > max_x:
            min_x, max_x = max_x, min_x
        if min_y > max_y:
            min_y, max_y = max_y, min_y
        
        return min_x, max_x, min_y, max_y
    
    def download_china_tiles(self, zoom=18, max_workers=10, batch_size=1000):
        """下载中国全国瓦片"""
        print(f"🗺️  开始下载中国全国瓦片")
        print(f"   缩放级别: {zoom}")
        print(f"   地图源: {self.tile_sources[self.current_source]['name']}")
        print(f"   并发数: {max_workers}")
        print(f"   批次大小: {batch_size}")
        
        # 计算瓦片范围
        min_x, max_x, min_y, max_y = self.calculate_tile_range(zoom)
        total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        
        print(f"   瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
        print(f"   总瓦片数: {total_tiles:,}")
        
        if total_tiles > 1000000:  # 超过100万瓦片
            print(f"   ⚠️  警告: 瓦片数量巨大，可能需要很长时间")
            confirm = input("   是否继续? (y/N): ").strip().lower()
            if confirm != 'y':
                print("   已取消下载")
                return
        
        self.download_stats['total'] = total_tiles
        
        # 创建任务队列
        task_queue = Queue()
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                task_queue.put((x, y, zoom))
        
        # 分批处理
        batch_count = 0
        while not task_queue.empty():
            batch_count += 1
            batch_tasks = []
            
            # 取出一批任务
            for _ in range(min(batch_size, task_queue.qsize())):
                if not task_queue.empty():
                    batch_tasks.append(task_queue.get())
            
            if not batch_tasks:
                break
            
            print(f"\n📦 处理批次 {batch_count} ({len(batch_tasks)} 个瓦片)")
            
            # 并行下载
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_tile = {
                    executor.submit(self.download_tile, x, y, z): (x, y, z) 
                    for x, y, z in batch_tasks
                }
                
                batch_success = 0
                for future in as_completed(future_to_tile):
                    x, y, z = future_to_tile[future]
                    try:
                        success, message = future.result()
                        if success:
                            batch_success += 1
                        else:
                            print(f"   ❌ {message}")
                    except Exception as e:
                        print(f"   ❌ 瓦片 {z}/{x}/{y} 处理异常: {e}")
                
                print(f"   ✅ 批次完成: {batch_success}/{len(batch_tasks)} 成功")
                
                # 显示总体进度
                total_processed = self.download_stats['success'] + self.download_stats['failed'] + self.download_stats['skipped']
                progress = total_processed / total_tiles * 100
                print(f"   📊 总体进度: {total_processed:,}/{total_tiles:,} ({progress:.1f}%)")
            
            # 批次间暂停，避免请求过快
            time.sleep(2)
        
        # 最终统计
        print(f"\n🎉 下载完成!")
        print(f"   总瓦片数: {self.download_stats['total']:,}")
        print(f"   成功下载: {self.download_stats['success']:,}")
        print(f"   跳过文件: {self.download_stats['skipped']:,}")
        print(f"   下载失败: {self.download_stats['failed']:,}")
        
        if self.download_stats['total'] > 0:
            success_rate = (self.download_stats['success'] + self.download_stats['skipped']) / self.download_stats['total'] * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        return self.download_stats
    
    def download_region_tiles(self, center_lat, center_lon, zoom=18, radius_km=50, max_workers=8):
        """下载指定区域的瓦片"""
        print(f"🗺️  开始下载区域瓦片")
        print(f"   中心点: ({center_lat}, {center_lon})")
        print(f"   缩放级别: {zoom}")
        print(f"   半径: {radius_km} km")
        print(f"   地图源: {self.tile_sources[self.current_source]['name']}")
        
        # 计算边界框
        lat_range = radius_km / 111.0
        lon_range = radius_km / (111.0 * math.cos(math.radians(center_lat)))
        
        min_lat = center_lat - lat_range
        max_lat = center_lat + lat_range
        min_lon = center_lon - lon_range
        max_lon = center_lon + lon_range
        
        print(f"   边界框: ({min_lat:.4f}, {min_lon:.4f}) 到 ({max_lat:.4f}, {max_lon:.4f})")
        
        # 计算瓦片范围
        min_x, max_y = self.deg2num(max_lat, min_lon, zoom)
        max_x, min_y = self.deg2num(min_lat, max_lon, zoom)
        
        # 确保范围正确
        if min_x > max_x:
            min_x, max_x = max_x, min_x
        if min_y > max_y:
            min_y, max_y = max_y, min_y
        
        total_tiles = (max_x - min_x + 1) * (max_y - min_y + 1)
        print(f"   瓦片范围: X({min_x}-{max_x}), Y({min_y}-{max_y})")
        print(f"   需要下载: {total_tiles:,} 个瓦片")
        
        if total_tiles <= 0:
            print(f"   ⚠️  跳过无效范围")
            return
        
        self.download_stats['total'] = total_tiles
        
        # 创建任务列表
        tasks = []
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                tasks.append((x, y, zoom))
        
        # 并行下载
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_tile = {
                executor.submit(self.download_tile, x, y, z): (x, y, z) 
                for x, y, z in tasks
            }
            
            for future in as_completed(future_to_tile):
                x, y, z = future_to_tile[future]
                try:
                    success, message = future.result()
                    if not success:
                        print(f"   ❌ {message}")
                except Exception as e:
                    print(f"   ❌ 瓦片 {z}/{x}/{y} 处理异常: {e}")
        
        # 统计结果
        print(f"\n🎉 下载完成!")
        print(f"   总瓦片数: {self.download_stats['total']:,}")
        print(f"   成功下载: {self.download_stats['success']:,}")
        print(f"   跳过文件: {self.download_stats['skipped']:,}")
        print(f"   下载失败: {self.download_stats['failed']:,}")
        
        if self.download_stats['total'] > 0:
            success_rate = (self.download_stats['success'] + self.download_stats['skipped']) / self.download_stats['total'] * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        return self.download_stats
    
    def set_source(self, source_name):
        """设置地图源"""
        if source_name in self.tile_sources:
            self.current_source = source_name
            print(f"✅ 已切换到地图源: {self.tile_sources[source_name]['name']}")
        else:
            print(f"❌ 未知的地图源: {source_name}")
            print(f"可用地图源: {list(self.tile_sources.keys())}")
    
    def get_source_info(self):
        """获取地图源信息"""
        return self.tile_sources[self.current_source]

def main():
    """主函数"""
    print("🗺️  全国瓦片下载工具")
    print("=" * 50)
    
    downloader = NationalTileDownloader()
    
    # 显示可用地图源
    print("可用地图源:")
    for key, config in downloader.tile_sources.items():
        print(f"  {key}: {config['name']} (最大缩放: {config['max_zoom']})")
    
    # 选择地图源
    source = input("\n请选择地图源 (默认: osm): ").strip() or 'osm'
    downloader.set_source(source)
    
    # 选择下载模式
    print("\n下载模式:")
    print("1. 全国瓦片 (18级)")
    print("2. 全国瓦片 (自定义缩放级别)")
    print("3. 区域瓦片 (北京地区)")
    print("4. 自定义区域")
    
    choice = input("请选择 (默认: 1): ").strip() or '1'
    
    if choice == '1':
        # 全国18级瓦片
        print("\n🚀 下载全国18级瓦片")
        downloader.download_china_tiles(zoom=18, max_workers=8, batch_size=500)
        
    elif choice == '2':
        # 全国自定义缩放级别
        try:
            zoom = int(input("请输入缩放级别 (推荐: 16-18): ") or "18")
            if zoom < 1 or zoom > 19:
                print("❌ 缩放级别必须在1-19之间")
                return
            
            max_workers = int(input("请输入并发数 (默认: 8): ") or "8")
            batch_size = int(input("请输入批次大小 (默认: 500): ") or "500")
            
            print(f"\n🚀 下载全国{zoom}级瓦片")
            downloader.download_china_tiles(zoom=zoom, max_workers=max_workers, batch_size=batch_size)
            
        except ValueError:
            print("❌ 输入格式错误")
            return
    
    elif choice == '3':
        # 北京地区
        print("\n🚀 下载北京地区瓦片")
        downloader.download_region_tiles(
            center_lat=39.9042,
            center_lon=116.4074,
            zoom=18,
            radius_km=50,
            max_workers=8
        )
        
    else:
        # 自定义区域
        try:
            lat = float(input("请输入中心纬度: "))
            lon = float(input("请输入中心经度: "))
            radius = float(input("请输入半径(km, 默认50): ") or "50")
            zoom = int(input("请输入缩放级别 (默认18): ") or "18")
            
            print(f"\n🚀 下载自定义区域瓦片")
            downloader.download_region_tiles(
                center_lat=lat,
                center_lon=lon,
                zoom=zoom,
                radius_km=radius,
                max_workers=8
            )
            
        except ValueError:
            print("❌ 输入格式错误")
            return
    
    print(f"\n✅ 下载完成! 瓦片保存在: {downloader.output_dir}")

if __name__ == "__main__":
    main()
