<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速地图测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { width: 100%; height: 500px; border: 2px solid #ccc; }
        .info { margin-bottom: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px; }
        .status { font-weight: bold; color: #333; }
    </style>
</head>
<body>
    <h1>🗺️ 快速地图测试</h1>
    
    <div class="info">
        <h3>测试状态</h3>
        <p><strong>瓦片服务器:</strong> <span id="tile-status" class="status">检查中...</span></p>
        <p><strong>地图状态:</strong> <span id="map-status" class="status">初始化中...</span></p>
        <p><strong>瓦片加载:</strong> <span id="tile-load-status" class="status">等待中...</span></p>
    </div>
    
    <div id="map"></div>
    
    <script>
        let map;
        let tileLayer;
        
        function updateStatus(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.color = isError ? '#d32f2f' : '#2e7d32';
        }
        
        async function checkTileServer() {
            try {
                const response = await fetch('http://127.0.0.1:8080/config');
                if (response.ok) {
                    const config = await response.json();
                    updateStatus('tile-status', `正常 (缩放: ${config.min_zoom}-${config.max_zoom})`);
                    return true;
                } else {
                    updateStatus('tile-status', `错误: ${response.status}`, true);
                    return false;
                }
            } catch (error) {
                updateStatus('tile-status', `连接失败: ${error.message}`, true);
                return false;
            }
        }
        
        async function initMap() {
            try {
                updateStatus('map-status', '正在初始化地图...');
                
                // 初始化地图
                map = L.map('map', {
                    center: [39.9042, 116.4074], // 北京
                    zoom: 18,
                    minZoom: 18,
                    maxZoom: 18
                });
                
                updateStatus('map-status', '正在添加瓦片图层...');
                
                // 添加瓦片图层
                tileLayer = L.tileLayer('http://127.0.0.1:8080/tiles/{z}/{x}/{y}.png', {
                    attribution: '© 本地离线地图',
                    minZoom: 18,
                    maxZoom: 18
                });
                
                // 监听瓦片事件
                tileLayer.on('tileload', function(e) {
                    updateStatus('tile-load-status', '瓦片加载成功!');
                    console.log('瓦片加载成功:', e.tile.src);
                });
                
                tileLayer.on('tileerror', function(e) {
                    updateStatus('tile-load-status', '瓦片加载失败!', true);
                    console.error('瓦片加载失败:', e.tile.src);
                });
                
                tileLayer.addTo(map);
                
                // 添加标记
                L.marker([39.9042, 116.4074])
                    .addTo(map)
                    .bindPopup('北京天安门<br>GPS: 39.9042, 116.4074')
                    .openPopup();
                
                updateStatus('map-status', '地图初始化完成!');
                
            } catch (error) {
                updateStatus('map-status', `初始化失败: ${error.message}`, true);
                console.error('地图初始化失败:', error);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', async function() {
            // 检查瓦片服务器
            const serverOk = await checkTileServer();
            
            if (serverOk) {
                // 初始化地图
                await initMap();
            } else {
                updateStatus('map-status', '瓦片服务器不可用，无法初始化地图', true);
            }
        });
    </script>
</body>
</html>
