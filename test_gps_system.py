#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS监控系统测试脚本
"""

import requests
import json
import time

def test_gps_system():
    """测试GPS监控系统功能"""
    base_url = 'http://127.0.0.1:5000'
    
    print("🔧 GPS机井监控系统功能测试")
    print("=" * 50)
    
    # 测试1: 获取统计信息
    print("1. 测试系统统计信息...")
    try:
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 系统正常 - 总机井: {stats['total_wells']}, 正常: {stats['active_wells']}, 警告: {stats['warning_wells']}")
        else:
            print(f"   ❌ 统计信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 测试2: 获取机井列表
    print("2. 测试机井数据获取...")
    try:
        response = requests.get(f"{base_url}/api/wells")
        if response.status_code == 200:
            wells = response.json()
            print(f"   ✅ 获取到 {len(wells)} 个机井")
            for well_id, well in wells.items():
                print(f"      - {well['name']}: {well['lat']:.6f}, {well['lon']:.6f} ({well['status']})")
        else:
            print(f"   ❌ 机井数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 机井数据获取失败: {e}")
    
    # 测试3: GPS定位功能
    print("3. 测试GPS定位功能...")
    test_coords = [
        {"lat": 39.9042, "lon": 116.4074, "name": "北京天安门"},
        {"lat": 31.2304, "lon": 121.4737, "name": "上海外滩"},
        {"lat": 22.3193, "lon": 114.1694, "name": "深圳南山"}
    ]
    
    for coord in test_coords:
        try:
            response = requests.post(
                f"{base_url}/api/locate",
                json={
                    "lat": coord["lat"],
                    "lon": coord["lon"],
                    "radius": 1000
                }
            )
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {coord['name']} 定位成功 - 找到 {result['count']} 个附近机井")
            else:
                print(f"   ❌ {coord['name']} 定位失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {coord['name']} 定位失败: {e}")
    
    # 测试4: 搜索功能
    print("4. 测试搜索功能...")
    search_queries = ["WELL001", "机井", "北京"]
    
    for query in search_queries:
        try:
            response = requests.post(
                f"{base_url}/api/search-location",
                json={"query": query}
            )
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 搜索 '{query}' - 找到 {len(result['results'])} 个结果")
            else:
                print(f"   ❌ 搜索 '{query}' 失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 搜索 '{query}' 失败: {e}")
    
    # 测试5: 添加新机井
    print("5. 测试添加机井功能...")
    new_well = {
        "id": "TEST001",
        "name": "测试机井",
        "lat": 39.9000,
        "lon": 116.4000,
        "status": "active",
        "gas_level": 30,
        "fan_status": "on",
        "description": "测试用机井"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/wells",
            json=new_well
        )
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 测试机井添加成功: {result['well']['name']}")
        else:
            print(f"   ❌ 测试机井添加失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 测试机井添加失败: {e}")
    
    # 测试6: 更新GPS坐标
    print("6. 测试GPS坐标更新...")
    try:
        response = requests.post(
            f"{base_url}/api/wells/TEST001/gps",
            json={
                "lat": 39.9001,
                "lon": 116.4001
            }
        )
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ GPS坐标更新成功: {result['well']['lat']:.6f}, {result['well']['lon']:.6f}")
        else:
            print(f"   ❌ GPS坐标更新失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ GPS坐标更新失败: {e}")
    
    print("\n🎯 测试完成!")
    print("=" * 50)
    print("📱 请在浏览器中访问 http://127.0.0.1:5000 查看系统界面")
    print("🔧 使用 python gps_data_simulator.py 启动数据模拟器")

if __name__ == "__main__":
    test_gps_system()
