#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSM数据处理脚本
将china-latest.osm.pbf转换为可用的地图瓦片
"""

import os
import sys
import subprocess
import json
import math
from pathlib import Path
import requests
import time

class OSMDataProcessor:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.osm_file = Path("F:/monitor1/china-latest.osm.pbf")
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.mbtiles_file = self.base_dir / "china.mbtiles"
        
    def process_osm_by_bbox(self, bbox):
        """根据边界框增量解析OSM数据"""
        import osmium as osm
        class BBoxFilter(osm.SimpleHandler):
            def __init__(self, bbox):
                super().__init__()
                self.bbox = bbox
            def node(self, n):
                if self.bbox.contains(n.location):
                    # 处理节点
                    pass
        handler = BBoxFilter(bbox)
        handler.apply_file(str(self.osm_file))
        
        missing = []
        for tool, desc in dependencies.items():
            if not self.check_command(tool):
                missing.append(f"{tool} - {desc}")
        
        if missing:
            print("❌ 缺少以下工具:")
            for item in missing:
                print(f"   - {item}")
            print("\n📥 安装建议:")
            print("Windows:")
            print("  1. 安装WSL2 (Windows Subsystem for Linux)")
            print("  2. 在WSL中安装工具: sudo apt install osmium-tool")
            print("  3. 安装tippecanoe: https://github.com/mapbox/tippecanoe")
            print("\nLinux:")
            print("  sudo apt install osmium-tool")
            print("  pip install mbutil")
            return False
        
        print("✅ 所有依赖工具已就绪")
        return True
    
    def check_command(self, command):
        """检查命令是否可用"""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def extract_region_data(self, region_name, bbox):
        """提取指定区域的OSM数据"""
        print(f"📍 提取 {region_name} 区域数据...")
        
        output_file = self.base_dir / f"{region_name}.osm.pbf"
        
        # 使用osmium提取区域数据
        cmd = [
            'osmium', 'extract',
            '--bbox', f"{bbox['west']},{bbox['south']},{bbox['east']},{bbox['north']}",
            '--output', str(output_file),
            str(self.osm_file)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✅ {region_name} 数据提取完成: {output_file}")
            return output_file
        except subprocess.CalledProcessError as e:
            print(f"❌ 数据提取失败: {e}")
            return None
    
    def generate_vector_tiles(self, osm_file, output_mbtiles):
        """生成矢量瓦片"""
        print("🔧 生成矢量瓦片...")
        
        # 使用tippecanoe生成矢量瓦片
        cmd = [
            'tippecanoe',
            '--output', str(output_mbtiles),
            '--force',
            '--maximum-zoom', '15',
            '--minimum-zoom', '1',
            '--base-zoom', '10',
            '--drop-densest-as-needed',
            '--extend-zooms-if-still-dropping',
            str(osm_file)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✅ 矢量瓦片生成完成: {output_mbtiles}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 矢量瓦片生成失败: {e}")
            return False
    
    def convert_to_raster_tiles(self, mbtiles_file):
        """将MBTiles转换为栅格瓦片目录结构"""
        print("🖼️  转换为栅格瓦片...")
        
        try:
            # 使用mbutil转换
            import mbutil
            
            # 确保输出目录存在
            self.tiles_dir.mkdir(parents=True, exist_ok=True)
            
            # 转换MBTiles到目录结构
            mbutil.mbtiles_to_disk(str(mbtiles_file), str(self.tiles_dir))
            
            print(f"✅ 瓦片转换完成: {self.tiles_dir}")
            return True
            
        except ImportError:
            print("❌ mbutil未安装，尝试使用命令行工具...")
            return self.convert_with_command(mbtiles_file)
        except Exception as e:
            print(f"❌ 瓦片转换失败: {e}")
            return False
    
    def convert_with_command(self, mbtiles_file):
        """使用命令行工具转换"""
        cmd = ['mb-util', str(mbtiles_file), str(self.tiles_dir)]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"✅ 瓦片转换完成: {self.tiles_dir}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令行转换失败: {e}")
            return False
    
    def download_raster_tiles_alternative(self):
        """备用方案：直接下载栅格瓦片"""
        print("🌐 使用备用方案：下载栅格瓦片...")
        
        # 中国主要区域的瓦片坐标
        regions = {
            "beijing": {"lat": 39.9042, "lng": 116.4074, "name": "北京"},
            "shanghai": {"lat": 31.2304, "lng": 121.4737, "name": "上海"},
            "guangzhou": {"lat": 23.1291, "lng": 113.2644, "name": "广州"},
            "shenzhen": {"lat": 22.5431, "lng": 114.0579, "name": "深圳"}
        }
        
        base_url = "https://tile.openstreetmap.org"
        total_downloaded = 0
        
        for region_id, region_info in regions.items():
            print(f"\n📍 下载 {region_info['name']} 地区瓦片...")
            lat, lng = region_info["lat"], region_info["lng"]
            
            # 为每个缩放级别下载瓦片
            for zoom in [10, 12, 15]:
                # 计算瓦片坐标
                x = int((lng + 180.0) / 360.0 * (1 << zoom))
                y = int((1.0 - math.asinh(math.tan(math.radians(lat))) / math.pi) / 2.0 * (1 << zoom))
                
                # 下载周围的瓦片
                for dx in range(-3, 4):  # 7x7 区域
                    for dy in range(-3, 4):
                        tile_x, tile_y = x + dx, y + dy
                        if self.download_single_tile(base_url, zoom, tile_x, tile_y):
                            total_downloaded += 1
                        
                        # 添加延迟避免过于频繁的请求
                        time.sleep(0.1)
        
        print(f"\n✅ 总共下载了 {total_downloaded} 个瓦片")
        return total_downloaded > 0
    
    def download_single_tile(self, base_url, z, x, y):
        """下载单个瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 如果文件已存在，跳过
        if tile_file.exists():
            return True
        
        try:
            url = f"{base_url}/{z}/{x}/{y}.png"
            response = requests.get(url, timeout=10, headers={
                'User-Agent': 'WellMonitoringSystem/1.0'
            })
            
            if response.status_code == 200:
                with open(tile_file, 'wb') as f:
                    f.write(response.content)
                print(f"  ✅ {z}/{x}/{y}.png")
                return True
            else:
                print(f"  ❌ {z}/{x}/{y}.png (HTTP {response.status_code})")
                return False
                
        except Exception as e:
            print(f"  ❌ {z}/{x}/{y}.png ({e})")
            return False
    
    def create_style_json(self):
        """创建地图样式配置"""
        style_config = {
            "version": 8,
            "name": "野外深机井监控地图",
            "sources": {
                "openmaptiles": {
                    "type": "vector",
                    "url": "mbtiles://./china.mbtiles"
                }
            },
            "layers": [
                {
                    "id": "background",
                    "type": "background",
                    "paint": {
                        "background-color": "#f8f4f0"
                    }
                },
                {
                    "id": "water",
                    "type": "fill",
                    "source": "openmaptiles",
                    "source-layer": "water",
                    "paint": {
                        "fill-color": "#a0c8f0"
                    }
                },
                {
                    "id": "roads",
                    "type": "line",
                    "source": "openmaptiles", 
                    "source-layer": "transportation",
                    "paint": {
                        "line-color": "#ffffff",
                        "line-width": 2
                    }
                }
            ]
        }
        
        style_file = self.base_dir / "map_style.json"
        with open(style_file, 'w', encoding='utf-8') as f:
            json.dump(style_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 地图样式配置已创建: {style_file}")
        return style_file
    
    def process_osm_data(self):
        """处理OSM数据的主流程"""
        print("🗺️  开始处理OSM数据")
        print("=" * 50)
        
        # 检查输入文件
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            print("请确保 china-latest.osm.pbf 文件在当前目录")
            return False
        
        print(f"📁 输入文件: {self.osm_file}")
        print(f"📁 输出目录: {self.tiles_dir}")
        
        # 方案1: 尝试使用专业工具处理
        if self.check_dependencies():
            print("\n🔧 使用专业工具处理OSM数据...")
            
            # 提取北京周边区域数据 (减小处理量)
            beijing_bbox = {
                'west': 115.4,
                'south': 39.4, 
                'east': 117.4,
                'north': 40.4
            }
            
            region_file = self.extract_region_data("beijing", beijing_bbox)
            if region_file:
                # 生成矢量瓦片
                if self.generate_vector_tiles(region_file, self.mbtiles_file):
                    # 转换为栅格瓦片
                    if self.convert_to_raster_tiles(self.mbtiles_file):
                        print("\n🎉 OSM数据处理完成!")
                        return True
        
        # 方案2: 备用方案 - 直接下载瓦片
        print("\n🌐 使用备用方案处理...")
        if self.download_raster_tiles_alternative():
            print("\n🎉 瓦片下载完成!")
            return True
        
        print("\n❌ 所有处理方案都失败了")
        return False
    
    def get_processing_stats(self):
        """获取处理统计信息"""
        if not self.tiles_dir.exists():
            return {"status": "未处理", "tiles": 0, "size_mb": 0}
        
        total_tiles = 0
        total_size = 0
        
        for tile_file in self.tiles_dir.rglob("*.png"):
            total_tiles += 1
            total_size += tile_file.stat().st_size
        
        return {
            "status": "已处理",
            "tiles": total_tiles,
            "size_mb": round(total_size / 1024 / 1024, 2),
            "zoom_levels": len(list(self.tiles_dir.iterdir())) if self.tiles_dir.exists() else 0
        }

def main():
    processor = OSMDataProcessor()
    
    print("🗺️  OSM数据处理工具")
    print("=" * 50)
    
    # 显示当前状态
    stats = processor.get_processing_stats()
    print(f"📊 当前状态: {stats['status']}")
    print(f"📊 瓦片数量: {stats['tiles']}")
    print(f"📊 数据大小: {stats['size_mb']} MB")
    
    if stats['tiles'] > 0:
        print("\n✅ 检测到已有瓦片数据")
        choice = input("是否重新处理? (y/N): ").lower()
        if choice != 'y':
            print("👋 保持现有数据，退出处理")
            return True
    
    # 开始处理
    success = processor.process_osm_data()
    
    if success:
        # 显示最终统计
        final_stats = processor.get_processing_stats()
        print(f"\n📊 处理结果:")
        print(f"   瓦片数量: {final_stats['tiles']}")
        print(f"   数据大小: {final_stats['size_mb']} MB")
        print(f"   缩放级别: {final_stats['zoom_levels']}")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 测试地图: python start_system.py map")
        print(f"   2. 启动系统: python start_system.py test")
        print(f"   3. 访问界面: http://localhost:5000")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)