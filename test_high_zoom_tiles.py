#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高精度瓦片是否能正确提供
"""

import requests
import os
from pathlib import Path

def test_tile_access():
    """测试瓦片访问"""
    base_url = "http://localhost:8080/tiles"
    
    # 测试不同缩放级别的瓦片(使用实际存在的坐标)
    test_tiles = [
        # 低精度瓦片
        (11, 1685, 775),
        (12, 3355, 1550),
        # 中精度瓦片
        (15, 26844, 12404),
        # 高精度瓦片
        (18, 215410, 98905)
    ]
    
    print("测试瓦片访问:")
    print("=" * 50)
    
    for z, x, y in test_tiles:
        url = f"{base_url}/{z}/{x}/{y}.png"
        file_path = Path(f"static/tiles/{z}/{x}/{y}.png")
        
        print(f"测试瓦片: {z}/{x}/{y}.png")
        print(f"  本地文件: {file_path.exists()}")
        
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"  文件大小: {size} 字节")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"  HTTP状态: {response.status_code}")
            if response.status_code == 200:
                print(f"  响应大小: {len(response.content)} 字节")
                print(f"  ✓ 成功访问")
            else:
                print(f"  ✗ 访问失败")
        except Exception as e:
            print(f"  ✗ 请求错误: {e}")
        
        print()

def check_tile_directory():
    """检查瓦片目录结构"""
    tiles_dir = Path("static/tiles")
    
    if not tiles_dir.exists():
        print("错误: 瓦片目录不存在")
        return
    
    print("瓦片目录结构:")
    print("=" * 50)
    
    zoom_levels = []
    for item in tiles_dir.iterdir():
        if item.is_dir() and item.name.isdigit():
            zoom_levels.append(int(item.name))
    
    zoom_levels.sort()
    print(f"可用缩放级别: {zoom_levels}")
    
    # 检查高精度瓦片
    high_zoom_levels = [z for z in zoom_levels if z >= 12]
    print(f"高精度缩放级别 (≥12): {high_zoom_levels}")
    
    for zoom in high_zoom_levels[:3]:  # 只检查前3个高精度级别
        zoom_dir = tiles_dir / str(zoom)
        x_dirs = [d for d in zoom_dir.iterdir() if d.is_dir() and d.name.isdigit()]
        
        if x_dirs:
            sample_x = x_dirs[0]
            y_files = list(sample_x.glob("*.png"))
            print(f"  缩放级别 {zoom}: 示例 X={sample_x.name}, Y文件数={len(y_files)}")

if __name__ == "__main__":
    print("高精度瓦片测试")
    print("=" * 50)
    
    check_tile_directory()
    print()
    test_tile_access()