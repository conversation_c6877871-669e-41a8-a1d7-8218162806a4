
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSM瓦片服务器
支持基于china-latest.osm.pbf生成的瓦片
"""

import os
import json
import time
from flask import Flask, send_file, jsonify, request, render_template_string
from pathlib import Path

app = Flask(__name__)

# 配置
BASE_DIR = Path(__file__).parent
TILES_DIR = BASE_DIR / "static" / "tiles"
CONFIG_FILE = BASE_DIR / "map_config.json"

def load_config():
    """加载地图配置"""
    if CONFIG_FILE.exists():
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

@app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
def serve_tile(z, x, y):
    """服务瓦片"""
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.png"
    
    if tile_path.exists():
        return send_file(tile_path, mimetype='image/png')
    else:
        # 返回404或空白瓦片
        return '', 404

@app.route('/config')
def get_config():
    """获取地图配置"""
    config = load_config()
    return jsonify(config)

@app.route('/stats')
def get_stats():
    """获取瓦片统计信息"""
    stats = {
        "tiles_dir": str(TILES_DIR),
        "tiles_exist": TILES_DIR.exists(),
        "zoom_levels": [],
        "total_tiles": 0
    }
    
    if TILES_DIR.exists():
        for zoom_dir in TILES_DIR.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom = int(zoom_dir.name)
                tile_count = 0
                
                for x_dir in zoom_dir.iterdir():
                    if x_dir.is_dir():
                        tile_count += len([f for f in x_dir.iterdir() 
                                         if f.suffix == '.png'])
                
                stats["zoom_levels"].append({
                    "zoom": zoom,
                    "tile_count": tile_count
                })
                stats["total_tiles"] += tile_count
    
    return jsonify(stats)

@app.route('/health')
def health_check():
    """健康检查"""
    config = load_config()
    
    return jsonify({
        "status": "healthy",
        "tiles_dir": str(TILES_DIR),
        "tiles_exist": TILES_DIR.exists(),
        "config_loaded": bool(config),
        "timestamp": time.time()
    })

@app.route('/')
def index():
    """简单的测试页面"""
    template = """
<!DOCTYPE html>
<html>
<head>
    <title>OSM瓦片服务器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .info { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .stats { margin-top: 20px; }
        .zoom-level { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🗺️ OSM瓦片服务器</h1>
    <div class="info">
        <h2>服务信息</h2>
        <p><strong>瓦片目录:</strong> {{ tiles_dir }}</p>
        <p><strong>配置文件:</strong> {{ config_file }}</p>
        <p><strong>状态:</strong> <span style="color: green;">运行中</span></p>
    </div>
    
    <div class="stats">
        <h2>瓦片统计</h2>
        <div id="stats-content">加载中...</div>
    </div>
    
    <script>
        fetch('/stats')
            .then(response => response.json())
            .then(data => {
                let html = '<p><strong>总瓦片数:</strong> ' + data.total_tiles + '</p>';
                html += '<h3>缩放级别:</h3>';
                data.zoom_levels.forEach(level => {
                    html += '<div class="zoom-level">级别 ' + level.zoom + ': ' + level.tile_count + ' 个瓦片</div>';
                });
                document.getElementById('stats-content').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('stats-content').innerHTML = '统计信息加载失败';
            });
    </script>
</body>
</html>
    """
    
    return render_template_string(template, 
                                tiles_dir=str(TILES_DIR),
                                config_file=str(CONFIG_FILE))

if __name__ == '__main__':
    print("🗺️ OSM瓦片服务器启动")
    print(f"📁 瓦片目录: {TILES_DIR}")
    print(f"🌐 访问地址: http://localhost:8080")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
