<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图缩放测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map {
            height: 600px;
            width: 100%;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>地图缩放测试</h1>
    
    <div class="controls">
        <button onclick="setZoom(10)">缩放至 10 级</button>
        <button onclick="setZoom(12)">缩放至 12 级</button>
        <button onclick="setZoom(15)">缩放至 15 级</button>
        <button onclick="setZoom(18)">缩放至 18 级</button>
        <button onclick="showCurrentZoom()">显示当前缩放级别</button>
    </div>
    
    <div id="map"></div>
    
    <div id="info" style="margin: 10px 0; padding: 10px; background: #f0f0f0;">
        <p>当前缩放级别: <span id="zoom-level">-</span></p>
        <p>地图状态: <span id="map-status">-</span></p>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let offlineLayer;
        
        // 初始化地图
        function initMap() {
            map = L.map('map', {
                minZoom: 8,
                maxZoom: 18,
                zoomControl: true
            }).setView([39.9042, 116.4074], 12);
            
            // 离线瓦片图层
            offlineLayer = L.tileLayer('/static/tiles/{z}/{x}/{y}.png', {
                attribution: '© OpenMapTiles © OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 8,
                errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            });
            
            offlineLayer.addTo(map);
            
            // 监听缩放事件
            map.on('zoomend', function() {
                updateInfo();
            });
            
            // 监听瓦片加载事件
            offlineLayer.on('tileload', function(e) {
                document.getElementById('map-status').textContent = '瓦片加载成功';
                console.log('瓦片加载成功:', e.url);
            });
            
            offlineLayer.on('tileerror', function(e) {
                document.getElementById('map-status').textContent = '瓦片加载失败';
                console.error('瓦片加载失败:', e);
            });
            
            updateInfo();
        }
        
        // 设置缩放级别
        function setZoom(level) {
            if (map) {
                map.setZoom(level);
            }
        }
        
        // 显示当前缩放级别
        function showCurrentZoom() {
            if (map) {
                alert('当前缩放级别: ' + map.getZoom());
            }
        }
        
        // 更新信息显示
        function updateInfo() {
            if (map) {
                document.getElementById('zoom-level').textContent = map.getZoom();
                document.getElementById('map-status').textContent = '地图已加载';
            }
        }
        
        // 页面加载完成后初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });
    </script>
</body>
</html>