#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能地图启动器
根据GPS坐标生成高精度离线地图
"""

import sys
import time
from pathlib import Path

def main():
    print("🗺️  智能地图生成器")
    print("=" * 50)
    
    try:
        from smart_map_generator import SmartMapGenerator
        generator = SmartMapGenerator()
        
        # 显示当前状态
        current_info = generator.get_current_tiles_info()
        print(f"📊 当前瓦片: {current_info['tiles']} 个")
        print(f"📊 占用空间: {current_info['size_mb']} MB")
        print(f"📊 缩放级别: {current_info['zoom_levels']}")
        
        print(f"\n📋 生成选项:")
        print(f"1. 北京地区 (39.9042, 116.4074)")
        print(f"2. 上海地区 (31.2304, 121.4737)")
        print(f"3. 广州地区 (23.1291, 113.2644)")
        print(f"4. 深圳地区 (22.5431, 114.0579)")
        print(f"5. 成都地区 (30.5728, 104.0668)")
        print(f"6. 自定义坐标")
        print(f"7. GPS自动模式")
        
        choice = input("\n请选择 (1-7): ").strip()
        
        if choice == "1":
            lat, lon, name = 39.9042, 116.4074, "北京"
        elif choice == "2":
            lat, lon, name = 31.2304, 121.4737, "上海"
        elif choice == "3":
            lat, lon, name = 23.1291, 113.2644, "广州"
        elif choice == "4":
            lat, lon, name = 22.5431, 114.0579, "深圳"
        elif choice == "5":
            lat, lon, name = 30.5728, 104.0668, "成都"
        elif choice == "6":
            lat = float(input("请输入纬度: "))
            lon = float(input("请输入经度: "))
            name = "自定义区域"
        elif choice == "7":
            start_gps_auto_mode()
            return
        else:
            print("无效选择")
            return
        
        # 获取生成参数
        print(f"\n🎯 生成参数设置:")
        radius_km = int(input(f"覆盖半径 (公里, 默认100): ") or "100")
        max_zoom = int(input(f"最大精度级别 (8-18, 默认18): ") or "18")
        min_zoom = int(input(f"最小精度级别 (1-17, 默认8): ") or "8")
        
        # 模式选择
        print(f"\n📡 下载模式:")
        print(f"1. 在线+离线 (优先在线下载，失败时生成离线瓦片)")
        print(f"2. 纯离线 (直接生成离线瓦片，无需网络)")
        
        mode_choice = input("请选择模式 (1-2, 默认1): ").strip() or "1"
        use_online = mode_choice == "1"
        
        print(f"\n🎯 生成配置:")
        print(f"   区域: {name}")
        print(f"   中心: {lat:.6f}, {lon:.6f}")
        print(f"   半径: {radius_km} 公里")
        print(f"   精度: {min_zoom}-{max_zoom} 级")
        print(f"   模式: {'在线+离线' if use_online else '纯离线'}")
        
        # 估算数据量
        size_info = generator.estimate_data_size(lat, lon, radius_km, max_zoom)
        print(f"\n📊 预估数据量:")
        print(f"   总瓦片数: {size_info['total_tiles']:,}")
        print(f"   预估大小: {size_info['total_size_mb']} MB")
        
        if size_info['total_size_mb'] > 500:
            print(f"⚠️  数据量很大 ({size_info['total_size_mb']} MB)")
            print(f"   建议降低精度级别或减小覆盖半径")
        
        confirm = input(f"\n确认生成? (y/N): ").lower()
        if confirm == 'y':
            print(f"\n🚀 开始生成地图...")
            success = generator.generate_area_map(
                lat, lon, radius_km, max_zoom, min_zoom, use_online
            )
            
            if success:
                print(f"\n🎉 地图生成完成！")
                print(f"\n🚀 下一步:")
                print(f"   1. 启动系统: python start_system.py test")
                print(f"   2. 访问界面: http://localhost:5000")
                print(f"   3. 地图将自动定位到生成区域")
                
                # 询问是否立即启动系统
                start_now = input(f"\n是否立即启动监控系统? (y/N): ").lower()
                if start_now == 'y':
                    print(f"🚀 启动监控系统...")
                    import subprocess
                    subprocess.run([sys.executable, "start_system.py", "test"])
            else:
                print(f"❌ 地图生成失败")
        else:
            print("已取消生成")
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print(f"请确保 smart_map_generator.py 文件存在")
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断操作")
    except Exception as e:
        print(f"❌ 操作失败: {e}")

def start_gps_auto_mode():
    """启动GPS自动地图生成模式"""
    print(f"\n🛰️  GPS自动地图生成模式")
    print("=" * 40)
    
    try:
        from gps_map_integration import GPSMapIntegration
        from test_system import MockDataReceiver
        
        print(f"📍 GPS自动地图生成说明:")
        print(f"   - 系统将监控GPS位置变化")
        print(f"   - 当移动超过50公里时自动生成新地图")
        print(f"   - 每个地图覆盖半径100公里，精度18级")
        print(f"   - 检查间隔: 5分钟")
        
        confirm = input(f"\n启动GPS自动模式? (y/N): ").lower()
        if confirm != 'y':
            print("已取消")
            return
        
        # 创建模拟数据接收器
        print(f"🔧 初始化GPS数据接收器...")
        mock_receiver = MockDataReceiver()
        mock_receiver.start()
        
        # 创建GPS地图集成
        print(f"🗺️  初始化地图生成器...")
        gps_integration = GPSMapIntegration()
        gps_integration.start_gps_monitoring(mock_receiver)
        
        print(f"\n✅ GPS自动地图生成已启动")
        print(f"📍 当前使用模拟GPS数据进行测试")
        print(f"🔄 系统将根据GPS位置自动生成地图")
        print(f"\n按 Ctrl+C 停止监控")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n⏹️  GPS自动模式已停止")
            mock_receiver.stop()
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()