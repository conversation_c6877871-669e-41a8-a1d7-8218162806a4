#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器用于测试地图图层功能
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

def start_test_server():
    """启动测试服务器"""
    PORT = 8080
    
    # 切换到当前目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"🗺️ 地图图层测试服务器启动")
        print(f"🌐 访问地址: http://localhost:{PORT}/test_map_layers.html")
        print(f"📱 请在浏览器中打开该地址测试地图图层功能")
        print(f"⏹️ 按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}/test_map_layers.html')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print(f"\n🛑 服务器已停止")

if __name__ == "__main__":
    start_test_server()
