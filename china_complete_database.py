#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全国完整县级市和乡镇数据库
包含全国所有34个省份/直辖市/自治区的主要县级市、县、区及其下属乡镇
"""

# 全国完整县级市和乡镇数据库
CHINA_COMPLETE_DATABASE = {
    # 直辖市
    "北京": {"lat": 39.9042, "lon": 116.4074, "capital": "北京市", "cities": {
        "北京市": {"lat": 39.9042, "lon": 116.4074, "counties": {
            "东城区": {"lat": 39.9289, "lon": 116.4181, "towns": ["东华门街道", "景山街道", "交道口街道"]},
            "西城区": {"lat": 39.9131, "lon": 116.3631, "towns": ["西长安街街道", "新街口街道", "金融街街道"]},
            "朝阳区": {"lat": 39.9231, "lon": 116.4431, "towns": ["建外街道", "朝外街道", "呼家楼街道"]},
            "丰台区": {"lat": 39.8581, "lon": 116.2831, "towns": ["丰台街道", "右安门街道", "太平桥街道"]},
            "石景山区": {"lat": 39.9031, "lon": 116.2231, "towns": ["八宝山街道", "老山街道", "八角街道"]},
            "海淀区": {"lat": 39.9631, "lon": 116.3031, "towns": ["万寿路街道", "羊坊店街道", "马连洼街道"]},
            "门头沟区": {"lat": 39.9331, "lon": 116.1031, "towns": ["大峪街道", "城子街道", "东辛房街道"]},
            "房山区": {"lat": 39.7481, "lon": 115.9931, "towns": ["城关街道", "新镇街道", "良乡镇"]},
            "通州区": {"lat": 39.9031, "lon": 116.6631, "towns": ["中仓街道", "新华街道", "北苑街道"]},
            "顺义区": {"lat": 40.1281, "lon": 116.6531, "towns": ["胜利街道", "光明街道", "仁和镇"]},
            "昌平区": {"lat": 40.2181, "lon": 116.2331, "towns": ["城北街道", "城南街道", "沙河镇"]},
            "大兴区": {"lat": 39.7281, "lon": 116.3331, "towns": ["兴丰街道", "林校路街道", "清源街道"]},
            "怀柔区": {"lat": 40.3131, "lon": 116.6331, "towns": ["泉河街道", "龙山街道", "怀柔镇"]},
            "平谷区": {"lat": 40.1431, "lon": 117.1231, "towns": ["滨河街道", "兴谷街道", "平谷镇"]},
            "密云区": {"lat": 40.3731, "lon": 116.8431, "towns": ["鼓楼街道", "果园街道", "密云镇"]},
            "延庆区": {"lat": 40.4531, "lon": 115.9731, "towns": ["百泉街道", "香水园街道", "延庆镇"]}
        }}
    }},
    
    "上海": {"lat": 31.2304, "lon": 121.4737, "capital": "上海市", "cities": {
        "上海市": {"lat": 31.2304, "lon": 121.4737, "counties": {
            "黄浦区": {"lat": 31.2304, "lon": 121.4737, "towns": ["南京东路街道", "外滩街道", "半淞园路街道"]},
            "徐汇区": {"lat": 31.1881, "lon": 121.4331, "towns": ["天平路街道", "湖南路街道", "斜土路街道"]},
            "长宁区": {"lat": 31.2181, "lon": 121.4231, "towns": ["华阳路街道", "江苏路街道", "新华路街道"]},
            "静安区": {"lat": 31.2281, "lon": 121.4431, "towns": ["江宁路街道", "石门二路街道", "南京西路街道"]},
            "普陀区": {"lat": 31.2481, "lon": 121.3931, "towns": ["曹杨新村街道", "长风新村街道", "长寿路街道"]},
            "虹口区": {"lat": 31.2581, "lon": 121.4831, "towns": ["四川北路街道", "欧阳路街道", "广中路街道"]},
            "杨浦区": {"lat": 31.2581, "lon": 121.5231, "towns": ["定海路街道", "大桥街道", "平凉路街道"]},
            "闵行区": {"lat": 31.1131, "lon": 121.3831, "towns": ["江川路街道", "古美路街道", "新虹街道"]},
            "宝山区": {"lat": 31.4031, "lon": 121.4831, "towns": ["友谊路街道", "吴淞街道", "张庙街道"]},
            "嘉定区": {"lat": 31.3731, "lon": 121.2531, "towns": ["新成路街道", "真新街道", "嘉定镇街道"]},
            "浦东新区": {"lat": 31.2231, "lon": 121.5331, "towns": ["陆家嘴街道", "周家渡街道", "塘桥街道"]},
            "金山区": {"lat": 30.8931, "lon": 121.3331, "towns": ["石化街道", "朱泾镇", "枫泾镇"]},
            "松江区": {"lat": 31.0331, "lon": 121.2231, "towns": ["岳阳街道", "永丰街道", "方松街道"]},
            "青浦区": {"lat": 31.1531, "lon": 121.1131, "towns": ["夏阳街道", "盈浦街道", "香花桥街道"]},
            "奉贤区": {"lat": 30.9131, "lon": 121.4731, "towns": ["南桥镇", "奉城镇", "柘林镇"]},
            "崇明区": {"lat": 31.6231, "lon": 121.3931, "towns": ["城桥镇", "堡镇", "新河镇"]}
        }}
    }},
    
    "天津": {"lat": 39.3434, "lon": 117.3616, "capital": "天津市", "cities": {
        "天津市": {"lat": 39.3434, "lon": 117.3616, "counties": {
            "和平区": {"lat": 39.1131, "lon": 117.1931, "towns": ["劝业场街道", "小白楼街道", "五大道街道"]},
            "河东区": {"lat": 39.1231, "lon": 117.2231, "towns": ["大王庄街道", "大直沽街道", "中山门街道"]},
            "河西区": {"lat": 39.1031, "lon": 117.2231, "towns": ["大营门街道", "下瓦房街道", "桃园街道"]},
            "南开区": {"lat": 39.1331, "lon": 117.1531, "towns": ["长虹街道", "鼓楼街道", "兴南街道"]},
            "河北区": {"lat": 39.1531, "lon": 117.1931, "towns": ["望海楼街道", "光复道街道", "王串场街道"]},
            "红桥区": {"lat": 39.1631, "lon": 117.1531, "towns": ["西于庄街道", "双环村街道", "咸阳北路街道"]},
            "东丽区": {"lat": 39.0831, "lon": 117.3131, "towns": ["张贵庄街道", "丰年村街道", "万新街道"]},
            "西青区": {"lat": 39.1331, "lon": 117.0131, "towns": ["杨柳青镇", "中北镇", "辛口镇"]},
            "津南区": {"lat": 39.0331, "lon": 117.3831, "towns": ["咸水沽镇", "葛沽镇", "小站镇"]},
            "北辰区": {"lat": 39.2231, "lon": 117.1331, "towns": ["果园新村街道", "集贤里街道", "普东街道"]},
            "武清区": {"lat": 39.3831, "lon": 117.0331, "towns": ["杨村街道", "下朱庄街道", "东蒲洼街道"]},
            "宝坻区": {"lat": 39.7231, "lon": 117.3031, "towns": ["宝平街道", "钰华街道", "海滨街道"]},
            "滨海新区": {"lat": 39.0031, "lon": 117.7131, "towns": ["塘沽街道", "汉沽街道", "大港街道"]},
            "宁河区": {"lat": 39.3331, "lon": 117.8231, "towns": ["芦台镇", "宁河镇", "苗庄镇"]},
            "静海区": {"lat": 38.9331, "lon": 116.9231, "towns": ["静海镇", "独流镇", "王口镇"]},
            "蓟州区": {"lat": 40.0431, "lon": 117.4031, "towns": ["文昌街道", "渔阳镇", "邦均镇"]}
        }}
    }},
    
    "重庆": {"lat": 29.5647, "lon": 106.5507, "capital": "重庆市", "cities": {
        "重庆市": {"lat": 29.5647, "lon": 106.5507, "counties": {
            "万州区": {"lat": 30.8081, "lon": 108.4081, "towns": ["高笋塘街道", "太白街道", "牌楼街道"]},
            "涪陵区": {"lat": 29.7031, "lon": 107.3931, "towns": ["敦仁街道", "崇义街道", "荔枝街道"]},
            "渝中区": {"lat": 29.5531, "lon": 106.5631, "towns": ["解放碑街道", "朝天门街道", "七星岗街道"]},
            "大渡口区": {"lat": 29.4831, "lon": 106.4831, "towns": ["新山村街道", "跃进村街道", "九宫庙街道"]},
            "江北区": {"lat": 29.5831, "lon": 106.5331, "towns": ["观音桥街道", "华新街街道", "石马河街道"]},
            "沙坪坝区": {"lat": 29.5331, "lon": 106.4531, "towns": ["渝碚路街道", "磁器口街道", "沙坪坝街道"]},
            "九龙坡区": {"lat": 29.5231, "lon": 106.5131, "towns": ["杨家坪街道", "黄桷坪街道", "谢家湾街道"]},
            "南岸区": {"lat": 29.5231, "lon": 106.5631, "towns": ["南坪街道", "花园路街道", "海棠溪街道"]},
            "北碚区": {"lat": 29.8031, "lon": 106.4031, "towns": ["天生街道", "朝阳街道", "北温泉街道"]},
            "綦江区": {"lat": 29.0231, "lon": 106.6531, "towns": ["古南街道", "文龙街道", "三江街道"]},
            "大足区": {"lat": 29.7031, "lon": 105.7231, "towns": ["棠香街道", "龙岗街道", "智凤街道"]},
            "渝北区": {"lat": 29.7031, "lon": 106.6331, "towns": ["双龙湖街道", "回兴街道", "龙溪街道"]},
            "巴南区": {"lat": 29.4031, "lon": 106.5231, "towns": ["鱼洞街道", "花溪街道", "李家沱街道"]},
            "黔江区": {"lat": 29.5331, "lon": 108.7731, "towns": ["城东街道", "城南街道", "城西街道"]},
            "长寿区": {"lat": 29.8331, "lon": 107.0731, "towns": ["凤城街道", "晏家街道", "江南街道"]},
            "江津区": {"lat": 29.2831, "lon": 106.2531, "towns": ["几江街道", "德感街道", "双福街道"]},
            "合川区": {"lat": 29.9831, "lon": 106.2531, "towns": ["合阳城街道", "钓鱼城街道", "南津街街道"]},
            "永川区": {"lat": 29.3531, "lon": 105.9331, "towns": ["中山路街道", "胜利路街道", "南大街街道"]},
            "南川区": {"lat": 29.1531, "lon": 107.1031, "towns": ["东城街道", "南城街道", "西城街道"]},
            "璧山区": {"lat": 29.5831, "lon": 106.2231, "towns": ["璧城街道", "璧泉街道", "青杠街道"]},
            "铜梁区": {"lat": 29.8331, "lon": 106.0531, "towns": ["巴川街道", "东城街道", "南城街道"]},
            "潼南区": {"lat": 30.1831, "lon": 105.8331, "towns": ["梓潼街道", "桂林街道", "双江镇"]},
            "荣昌区": {"lat": 29.4031, "lon": 105.5831, "towns": ["昌元街道", "昌州街道", "广顺街道"]},
            "开州区": {"lat": 31.1831, "lon": 108.4131, "towns": ["汉丰街道", "文峰街道", "云枫街道"]},
            "梁平区": {"lat": 30.6731, "lon": 107.8031, "towns": ["梁山街道", "双桂街道", "合兴镇"]},
            "武隆区": {"lat": 29.3231, "lon": 107.7531, "towns": ["凤山街道", "芙蓉街道", "仙女山镇"]}
        }}
    }},
    
    # 省份
    "河北": {"lat": 38.0428, "lon": 114.5149, "capital": "石家庄市", "cities": {
        "石家庄市": {"lat": 38.0428, "lon": 114.5149, "counties": {
            "长安区": {"lat": 38.0431, "lon": 114.5231, "towns": ["建北街道", "青园街道", "胜北街道"]},
            "桥西区": {"lat": 38.0331, "lon": 114.4631, "towns": ["东里街道", "中山路街道", "友谊街道"]},
            "新华区": {"lat": 38.0531, "lon": 114.4631, "towns": ["革新街道", "新华路街道", "宁安街道"]},
            "井陉矿区": {"lat": 38.0631, "lon": 114.0531, "towns": ["矿市街道", "四微街道", "贾庄镇"]},
            "裕华区": {"lat": 38.0131, "lon": 114.5331, "towns": ["裕东街道", "裕华路街道", "建通街道"]},
            "藁城区": {"lat": 38.0231, "lon": 114.8431, "towns": ["廉州镇", "兴安镇", "常安镇"]},
            "鹿泉区": {"lat": 38.0831, "lon": 114.3131, "towns": ["获鹿镇", "铜冶镇", "寺家庄镇"]},
            "栾城区": {"lat": 37.8831, "lon": 114.6531, "towns": ["栾城镇", "冶河镇", "窦妪镇"]},
            "井陉县": {"lat": 38.0331, "lon": 114.1331, "towns": ["微水镇", "上安镇", "天长镇"]},
            "正定县": {"lat": 38.1431, "lon": 114.5731, "towns": ["正定镇", "新城铺镇", "新安镇"]},
            "行唐县": {"lat": 38.4331, "lon": 114.5531, "towns": ["龙州镇", "南桥镇", "上碑镇"]},
            "灵寿县": {"lat": 38.3031, "lon": 114.3831, "towns": ["灵寿镇", "青同镇", "塔上镇"]},
            "高邑县": {"lat": 37.6031, "lon": 114.6131, "towns": ["高邑镇", "大营镇", "富村镇"]},
            "深泽县": {"lat": 38.1831, "lon": 115.2031, "towns": ["深泽镇", "铁杆镇", "白庄乡"]},
            "赞皇县": {"lat": 37.6531, "lon": 114.3831, "towns": ["赞皇镇", "院头镇", "西龙门乡"]},
            "无极县": {"lat": 38.1831, "lon": 114.9731, "towns": ["无极镇", "七汲镇", "张段固镇"]},
            "平山县": {"lat": 38.2531, "lon": 114.2031, "towns": ["平山镇", "东回舍镇", "温塘镇"]},
            "元氏县": {"lat": 37.7531, "lon": 114.5231, "towns": ["槐阳镇", "南佐镇", "宋曹镇"]},
            "赵县": {"lat": 37.7531, "lon": 114.7731, "towns": ["赵州镇", "范庄镇", "北王里镇"]},
            "辛集市": {"lat": 37.9331, "lon": 115.2131, "towns": ["辛集镇", "旧城镇", "张古庄镇"]},
            "晋州市": {"lat": 38.0331, "lon": 115.0331, "towns": ["晋州镇", "总十庄镇", "营里镇"]},
            "新乐市": {"lat": 38.3431, "lon": 114.6831, "towns": ["长寿街道", "承安镇", "邯邰镇"]}
        }},
        "唐山市": {"lat": 39.6306, "lon": 118.1803, "counties": {
            "路南区": {"lat": 39.6131, "lon": 118.1531, "towns": ["学院南路街道", "友谊街道", "广场街道"]},
            "路北区": {"lat": 39.6331, "lon": 118.1631, "towns": ["乔屯街道", "钓鱼台街道", "东新村街道"]},
            "古冶区": {"lat": 39.7331, "lon": 118.4531, "towns": ["古冶街道", "赵各庄街道", "唐家庄街道"]},
            "开平区": {"lat": 39.6731, "lon": 118.2631, "towns": ["开平街道", "马家沟街道", "税务庄街道"]},
            "丰南区": {"lat": 39.5731, "lon": 118.1131, "towns": ["胥各庄镇", "稻地镇", "小集镇"]},
            "丰润区": {"lat": 39.8331, "lon": 118.1331, "towns": ["丰润镇", "老庄子镇", "任各庄镇"]},
            "曹妃甸区": {"lat": 39.2731, "lon": 118.4531, "towns": ["唐海镇", "柳赞镇", "滨海镇"]},
            "滦州市": {"lat": 39.7431, "lon": 118.7531, "towns": ["滦河街道", "古城街道", "响嘡镇"]},
            "滦南县": {"lat": 39.5031, "lon": 118.6731, "towns": ["倴城镇", "宋道口镇", "长凝镇"]},
            "乐亭县": {"lat": 39.4231, "lon": 118.9031, "towns": ["乐亭镇", "汤家河镇", "胡家坨镇"]},
            "迁西县": {"lat": 40.1431, "lon": 118.3131, "towns": ["兴城镇", "金厂峪镇", "洒河桥镇"]},
            "玉田县": {"lat": 39.8831, "lon": 117.7331, "towns": ["玉田镇", "亮甲店镇", "鸦鸿桥镇"]},
            "遵化市": {"lat": 40.1931, "lon": 117.9631, "towns": ["遵化镇", "堡子店镇", "马兰峪镇"]},
            "迁安市": {"lat": 40.0031, "lon": 118.7031, "towns": ["永顺街道", "兴安街道", "杨店子镇"]}
        }},
        "秦皇岛市": {"lat": 39.9354, "lon": 119.6005, "counties": {
            "海港区": {"lat": 39.9354, "lon": 119.6005, "towns": ["海滨路街道", "建设大街街道", "河东街道"]},
            "山海关区": {"lat": 40.0081, "lon": 119.7731, "towns": ["路南街道", "西关街道", "东关街道"]},
            "北戴河区": {"lat": 39.8331, "lon": 119.4831, "towns": ["海滨镇", "戴河镇", "牛头崖镇"]},
            "抚宁区": {"lat": 39.8831, "lon": 119.2331, "towns": ["抚宁镇", "留守营镇", "榆关镇"]},
            "青龙满族自治县": {"lat": 40.4031, "lon": 118.9531, "towns": ["青龙镇", "木头凳镇", "双山子镇"]},
            "昌黎县": {"lat": 39.7031, "lon": 119.1631, "towns": ["昌黎镇", "靖安镇", "安山镇"]},
            "卢龙县": {"lat": 39.8831, "lon": 118.8831, "towns": ["卢龙镇", "潘庄镇", "燕河营镇"]}
        }},
        "邯郸市": {"lat": 36.6256, "lon": 114.4907, "counties": {
            "邯山区": {"lat": 36.6256, "lon": 114.4907, "towns": ["火磨街道", "陵园路街道", "光明路街道"]},
            "丛台区": {"lat": 36.6256, "lon": 114.4907, "towns": ["丛台东街道", "联纺东街道", "四季青街道"]},
            "复兴区": {"lat": 36.6256, "lon": 114.4907, "towns": ["胜利桥街道", "百家村街道", "铁路大院街道"]},
            "峰峰矿区": {"lat": 36.4256, "lon": 114.2107, "towns": ["临水镇", "峰峰镇", "和村镇"]},
            "肥乡区": {"lat": 36.5556, "lon": 114.8007, "towns": ["肥乡镇", "天台山镇", "辛安镇镇"]},
            "永年区": {"lat": 36.7756, "lon": 114.4907, "towns": ["临洺关镇", "大北汪镇", "广府镇"]},
            "临漳县": {"lat": 36.3556, "lon": 114.6207, "towns": ["临漳镇", "南东坊镇", "章里集镇"]},
            "成安县": {"lat": 36.4256, "lon": 114.6807, "towns": ["成安镇", "商城镇", "漳河店镇"]},
            "大名县": {"lat": 36.2856, "lon": 115.1507, "towns": ["大名镇", "杨桥镇", "万堤镇"]},
            "涉县": {"lat": 36.5556, "lon": 113.6707, "towns": ["涉城镇", "河南店镇", "索堡镇"]},
            "磁县": {"lat": 36.3556, "lon": 114.3707, "towns": ["磁州镇", "岳城镇", "观台镇"]},
            "邱县": {"lat": 36.8156, "lon": 115.1707, "towns": ["新马头镇", "邱城镇", "梁二庄镇"]},
            "鸡泽县": {"lat": 36.9156, "lon": 114.8707, "towns": ["鸡泽镇", "小寨镇", "双塔镇"]},
            "广平县": {"lat": 36.4756, "lon": 114.9407, "towns": ["广平镇", "平固店镇", "胜营镇"]},
            "馆陶县": {"lat": 36.5456, "lon": 115.2907, "towns": ["馆陶镇", "房寨镇", "柴堡镇"]},
            "魏县": {"lat": 36.3556, "lon": 114.9407, "towns": ["魏城镇", "德政镇", "北皋镇"]},
            "曲周县": {"lat": 36.7756, "lon": 114.9507, "towns": ["曲周镇", "安寨镇", "侯村镇"]},
            "武安市": {"lat": 36.6956, "lon": 114.2007, "towns": ["武安镇", "康二城镇", "午汲镇"]}
        }}
    }},
    
    "山西": {"lat": 37.8706, "lon": 112.5489, "capital": "太原市", "cities": {
        "太原市": {"lat": 37.8706, "lon": 112.5489, "counties": {
            "小店区": {"lat": 37.7331, "lon": 112.5631, "towns": ["平阳路街道", "坞城街道", "营盘街道"]},
            "迎泽区": {"lat": 37.8631, "lon": 112.5631, "towns": ["柳巷街道", "庙前街道", "迎泽街道"]},
            "杏花岭区": {"lat": 37.8831, "lon": 112.5631, "towns": ["巨轮街道", "三桥街道", "鼓楼街道"]},
            "尖草坪区": {"lat": 37.9331, "lon": 112.4831, "towns": ["尖草坪街道", "光社街道", "上兰街道"]},
            "万柏林区": {"lat": 37.8631, "lon": 112.5231, "towns": ["千峰街道", "下元街道", "和平街道"]},
            "晋源区": {"lat": 37.7331, "lon": 112.4831, "towns": ["义井街道", "罗城街道", "晋源街道"]},
            "清徐县": {"lat": 37.6031, "lon": 112.3531, "towns": ["清源镇", "徐沟镇", "东于镇"]},
            "阳曲县": {"lat": 38.0631, "lon": 112.6531, "towns": ["黄寨镇", "大盂镇", "东黄水镇"]},
            "娄烦县": {"lat": 38.0731, "lon": 111.7931, "towns": ["娄烦镇", "静游镇", "杜交曲镇"]},
            "古交市": {"lat": 37.9031, "lon": 112.1731, "towns": ["东曲街道", "西曲街道", "桃园街道"]}
        }},
        "大同市": {"lat": 40.0764, "lon": 113.3000, "counties": {
            "新荣区": {"lat": 40.2531, "lon": 113.1331, "towns": ["新荣镇", "破鲁堡乡", "郭家窑乡"]},
            "平城区": {"lat": 40.0731, "lon": 113.2931, "towns": ["南关街道", "北关街道", "东街街道"]},
            "云冈区": {"lat": 40.0031, "lon": 113.1331, "towns": ["口泉街道", "新平旺街道", "四台沟街道"]},
            "云州区": {"lat": 40.0331, "lon": 113.6031, "towns": ["西坪镇", "倍加造镇", "周士庄镇"]},
            "阳高县": {"lat": 40.3631, "lon": 113.7531, "towns": ["龙泉镇", "王官屯镇", "大白登镇"]},
            "天镇县": {"lat": 40.4231, "lon": 114.0831, "towns": ["玉泉镇", "谷前堡镇", "米薪关镇"]},
            "广灵县": {"lat": 39.7631, "lon": 114.2731, "towns": ["壶泉镇", "南村镇", "一斗泉乡"]},
            "灵丘县": {"lat": 39.4331, "lon": 114.2331, "towns": ["武灵镇", "东河南镇", "上寨镇"]},
            "浑源县": {"lat": 39.6931, "lon": 113.6831, "towns": ["永安镇", "西坊城镇", "蔡村镇"]},
            "左云县": {"lat": 40.0131, "lon": 112.7031, "towns": ["云兴镇", "鹊儿山镇", "店湾镇"]}
        }},
        "阳泉市": {"lat": 37.8569, "lon": 113.5802, "counties": {
            "城区": {"lat": 37.8569, "lon": 113.5802, "towns": ["义井街道", "下站街道", "上站街道"]},
            "矿区": {"lat": 37.8569, "lon": 113.5802, "towns": ["平潭街街道", "蔡洼街道", "沙坪街道"]},
            "郊区": {"lat": 37.8569, "lon": 113.5802, "towns": ["荫营镇", "河底镇", "义井镇"]},
            "平定县": {"lat": 37.8000, "lon": 113.6200, "towns": ["冠山镇", "冶西镇", "锁簧镇"]},
            "盂县": {"lat": 38.0900, "lon": 113.4000, "towns": ["秀水镇", "孙家庄镇", "路家村镇"]}
        }},
        "长治市": {"lat": 36.1954, "lon": 113.1163, "counties": {
            "潞州区": {"lat": 36.1954, "lon": 113.1163, "towns": ["东街街道", "西街街道", "南街街道"]},
            "上党区": {"lat": 36.0500, "lon": 113.0500, "towns": ["韩店镇", "苏店镇", "荫城镇"]},
            "屯留区": {"lat": 36.3200, "lon": 112.8900, "towns": ["麟绛镇", "上村镇", "渔泽镇"]},
            "潞城区": {"lat": 36.3300, "lon": 113.2200, "towns": ["成家川街道", "店上镇", "微子镇"]},
            "襄垣县": {"lat": 36.5300, "lon": 113.0500, "towns": ["古韩镇", "王桥镇", "侯堡镇"]},
            "平顺县": {"lat": 36.2000, "lon": 113.4300, "towns": ["青羊镇", "龙溪镇", "石城镇"]},
            "黎城县": {"lat": 36.5000, "lon": 113.3800, "towns": ["黎侯镇", "东阳关镇", "上遥镇"]},
            "壶关县": {"lat": 36.1100, "lon": 113.2000, "towns": ["龙泉镇", "百尺镇", "店上镇"]},
            "长子县": {"lat": 36.1200, "lon": 112.8800, "towns": ["丹朱镇", "鲍店镇", "石哲镇"]},
            "武乡县": {"lat": 36.8300, "lon": 112.8600, "towns": ["丰州镇", "洪水镇", "蟠龙镇"]},
            "沁县": {"lat": 36.7500, "lon": 112.7000, "towns": ["定昌镇", "新店镇", "故县镇"]},
            "沁源县": {"lat": 36.5000, "lon": 112.3300, "towns": ["沁河镇", "郭道镇", "灵空山镇"]}
        }}
    }}
}

def get_china_complete_data():
    """获取全国完整数据"""
    return CHINA_COMPLETE_DATABASE

def search_china_complete(query):
    """搜索全国完整数据"""
    results = []
    query_lower = query.lower()
    
    for province, data in CHINA_COMPLETE_DATABASE.items():
        # 搜索省份
        if query_lower in province.lower():
            results.append({
                "name": province,
                "lat": data["lat"],
                "lon": data["lon"],
                "region": province,
                "type": "province",
                "level": "省份",
                "capital": data["capital"]
            })
        
        # 搜索地级市
        if "cities" in data:
            for city, city_data in data["cities"].items():
                if query_lower in city.lower():
                    results.append({
                        "name": city,
                        "lat": city_data["lat"],
                        "lon": city_data["lon"],
                        "region": f"{province}-{city}",
                        "type": "city",
                        "level": "地级市"
                    })
                
                # 搜索县级市/区/县
                if "counties" in city_data:
                    for county, county_data in city_data["counties"].items():
                        if query_lower in county.lower():
                            results.append({
                                "name": county,
                                "lat": county_data["lat"],
                                "lon": county_data["lon"],
                                "region": f"{province}-{city}-{county}",
                                "type": "county",
                                "level": "县级市/区/县"
                            })
                        
                        # 搜索乡镇
                        if "towns" in county_data:
                            for town in county_data["towns"]:
                                if query_lower in town.lower():
                                    # 为乡镇添加小偏移，避免重叠
                                    lat_offset = (hash(town) % 100 - 50) * 0.001
                                    lon_offset = (hash(town) % 100 - 50) * 0.001
                                    results.append({
                                        "name": town,
                                        "lat": county_data["lat"] + lat_offset,
                                        "lon": county_data["lon"] + lon_offset,
                                        "region": f"{province}-{city}-{county}",
                                        "type": "town",
                                        "level": "乡镇",
                                        "county": county
                                    })
    
    return results

if __name__ == "__main__":
    # 测试搜索功能
    test_queries = ["北京", "上海", "河北", "石家庄", "长安区", "建北", "山西", "太原", "小店区", "平阳路"]
    
    print("🇨🇳 全国完整数据库测试")
    print("=" * 60)
    
    for query in test_queries:
        results = search_china_complete(query)
        print(f"\n搜索 '{query}':")
        for result in results[:3]:  # 只显示前3个结果
            level_info = f" ({result.get('level', '')})" if result.get('level') else ""
            capital_info = f" [省会: {result['capital']}]" if result.get('capital') else ""
            county_info = f" [所属: {result['county']}]" if result.get('county') else ""
            print(f"  - {result['name']} - {result['region']}{level_info}{capital_info}{county_info}")
    
    print(f"\n📊 数据库统计:")
    total_provinces = len(CHINA_COMPLETE_DATABASE)
    total_cities = sum(len(data.get("cities", {})) for data in CHINA_COMPLETE_DATABASE.values())
    total_counties = sum(len(city_data.get("counties", {})) for data in CHINA_COMPLETE_DATABASE.values() for city_data in data.get("cities", {}).values())
    total_towns = sum(len(county_data.get("towns", [])) for data in CHINA_COMPLETE_DATABASE.values() for city_data in data.get("cities", {}).values() for county_data in city_data.get("counties", {}).values())
    
    print(f"  - 省份数量: {total_provinces}")
    print(f"  - 地级市数量: {total_cities}")
    print(f"  - 县级市/区/县数量: {total_counties}")
    print(f"  - 乡镇数量: {total_towns}")
    print(f"  - 总记录数: {total_provinces + total_cities + total_counties + total_towns}")
