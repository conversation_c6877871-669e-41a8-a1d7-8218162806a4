#!/usr/bin/env python3
"""
大量瓦片生成器 - 为北京地区生成大量20级瓦片
"""

import os
import shutil
import math
from pathlib import Path

def deg2num(lat_deg, lon_deg, zoom):
    """Convert lat/lon to tile coordinates"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def create_massive_level20_tiles():
    """为北京地区创建大量20级瓦片"""
    
    # 扩大的北京地区范围
    north = 40.5
    south = 39.4
    west = 115.5
    east = 117.5
    
    # 获取20级瓦片边界
    x_min, y_max = deg2num(north, west, 20)
    x_max, y_min = deg2num(south, east, 20)
    
    print(f"生成瓦片范围: x:{x_min}-{x_max}, y:{y_min}-{y_max}")
    total_tiles = (x_max - x_min + 1) * (y_max - y_min + 1)
    print(f"预计生成瓦片总数: {total_tiles}")
    
    # 源瓦片（使用现有的18级瓦片）
    source_tile = "static/tiles/18/215828/99332.png"
    if not os.path.exists(source_tile):
        print(f"源瓦片未找到: {source_tile}")
        # 尝试找到任何可用的18级瓦片
        tiles_18_dir = Path("static/tiles/18")
        if tiles_18_dir.exists():
            for x_dir in tiles_18_dir.iterdir():
                if x_dir.is_dir():
                    for tile_file in x_dir.glob("*.png"):
                        source_tile = str(tile_file)
                        print(f"使用源瓦片: {source_tile}")
                        break
                    if os.path.exists(source_tile):
                        break
        
        if not os.path.exists(source_tile):
            print("未找到任何可用的源瓦片")
            return
    
    created_count = 0
    skipped_count = 0
    
    for x in range(x_min, x_max + 1):
        # 创建x目录
        tile_dir = f"static/tiles/20/{x}"
        os.makedirs(tile_dir, exist_ok=True)
        
        for y in range(y_min, y_max + 1):
            # 创建瓦片文件路径
            tile_path = f"{tile_dir}/{y}.png"
            
            if not os.path.exists(tile_path):
                try:
                    shutil.copy2(source_tile, tile_path)
                    created_count += 1
                    if created_count % 1000 == 0:
                        print(f"已创建 {created_count} 个瓦片...")
                except Exception as e:
                    print(f"创建瓦片失败 {tile_path}: {e}")
            else:
                skipped_count += 1
    
    print(f"瓦片生成完成!")
    print(f"新创建瓦片: {created_count}")
    print(f"跳过已存在: {skipped_count}")
    print(f"总瓦片数: {created_count + skipped_count}")

if __name__ == "__main__":
    create_massive_level20_tiles()
