<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>野外深机井监控系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>野外深机井监控系统</h1>
            <div class="status-bar">
                <span id="connection-status" class="status-indicator">连接状态: 未连接</span>
                <span id="last-update">最后更新: --</span>
            </div>
        </header>

        <div class="main-content">
            <!-- 地图区域 -->
            <div class="map-section">
                <div class="panel-header">
                    <h2>GPS定位</h2>
                    <div class="gps-info">
                        <span>纬度: <span id="current-lat">--</span></span>
                        <span>经度: <span id="current-lng">--</span></span>
                        <span>海拔: <span id="current-alt">--</span>m</span>
                    </div>
                </div>
                <div id="map"></div>
            </div>

            <!-- 数据监控区域 -->
            <div class="data-section">
                <!-- 气体监测 -->
                <div class="panel gas-panel">
                    <div class="panel-header">
                        <h3>气体监测</h3>
                    </div>
                    <div class="gas-grid">
                        <div class="gas-item">
                            <div class="gas-label">一氧化碳 (CO)</div>
                            <div class="gas-value" id="co-value">0 ppm</div>
                            <div class="gas-status" id="co-status">正常</div>
                        </div>
                        <div class="gas-item">
                            <div class="gas-label">硫化氢 (H₂S)</div>
                            <div class="gas-value" id="h2s-value">0 ppm</div>
                            <div class="gas-status" id="h2s-status">正常</div>
                        </div>
                        <div class="gas-item">
                            <div class="gas-label">甲烷 (CH₄)</div>
                            <div class="gas-value" id="ch4-value">0 %LEL</div>
                            <div class="gas-status" id="ch4-status">正常</div>
                        </div>
                        <div class="gas-item">
                            <div class="gas-label">氧气 (O₂)</div>
                            <div class="gas-value" id="o2-value">21 %</div>
                            <div class="gas-status" id="o2-status">正常</div>
                        </div>
                    </div>
                </div>

                <!-- 设备状态 -->
                <div class="panel equipment-panel">
                    <div class="panel-header">
                        <h3>设备状态</h3>
                    </div>
                    <div class="equipment-grid">
                        <div class="equipment-item">
                            <div class="equipment-label">送风机1</div>
                            <div class="equipment-status" id="fan1-status">停止</div>
                            <div class="status-indicator" id="fan1-indicator"></div>
                        </div>
                        <div class="equipment-item">
                            <div class="equipment-label">送风机2</div>
                            <div class="equipment-status" id="fan2-status">停止</div>
                            <div class="status-indicator" id="fan2-indicator"></div>
                        </div>
                    </div>
                </div>

                <!-- 环境监测 -->
                <div class="panel environment-panel">
                    <div class="panel-header">
                        <h3>环境监测</h3>
                    </div>
                    <div class="environment-grid">
                        <div class="env-item">
                            <div class="env-label">温度</div>
                            <div class="env-value" id="temperature-value">25°C</div>
                        </div>
                        <div class="env-item">
                            <div class="env-label">湿度</div>
                            <div class="env-value" id="humidity-value">60%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>