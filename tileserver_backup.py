#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的瓦片服务器
用于提供离线地图瓦片
"""

from flask import Flask, send_file, abort, jsonify
from pathlib import Path
import os
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
TILES_DIR = Path(__file__).parent / "static" / "tiles"

# 简单的请求统计
request_stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0
}

@app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
def serve_tile(z, x, y):
    """提供地图瓦片"""
    request_stats['total_requests'] += 1
    
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.png"
    
    # 记录请求信息
    logger.info(f"请求瓦片: {z}/{x}/{y}.png")
    
    if tile_path.exists():
        logger.info(f"瓦片存在: {tile_path}")
        request_stats['successful_requests'] += 1
        return send_file(tile_path, mimetype='image/png')
    else:
        logger.warning(f"瓦片不存在: {tile_path}")
        request_stats['failed_requests'] += 1
        # 返回404
        abort(404)

@app.route('/tiles/<int:z>/<int:x>/<int:y>')
def serve_tile_no_ext(z, x, y):
    """提供地图瓦片 (无扩展名)"""
    return serve_tile(z, x, y)

@app.route('/stats')
def get_stats():
    """获取服务器统计信息"""
    return jsonify(request_stats)

@app.route('/health')
def health_check():
    """健康检查"""
    health_status = {
        'status': 'healthy',
        'tiles_dir': str(TILES_DIR),
        'tiles_dir_exists': TILES_DIR.exists(),
        'request_stats': request_stats
    }
    
    # 检查瓦片目录
    if TILES_DIR.exists():
        zoom_stats = {}
        for zoom_dir in TILES_DIR.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom_level = int(zoom_dir.name)
                tile_count = sum(1 for _ in zoom_dir.rglob("*.png"))
                zoom_stats[zoom_level] = tile_count
        
        health_status['zoom_stats'] = zoom_stats
    
    return jsonify(health_status)

@app.route('/')
def index():
    """服务器主页"""
    return f"""
    <h1>瓦片服务器</h1>
    <p>瓦片目录: {TILES_DIR}</p>
    <p>支持缩放级别: 8-18</p>
    <p><a href="/stats">查看统计信息</a></p>
    <p><a href="/health">健康检查</a></p>
    """

if __name__ == '__main__':
    print(f"瓦片目录: {TILES_DIR}")
    print("瓦片服务器启动在: http://localhost:8080")
    # 检查瓦片目录是否存在
    if not TILES_DIR.exists():
        print(f"警告: 瓦片目录不存在: {TILES_DIR}")
    else:
        # 统计各缩放级别的瓦片数量
        zoom_stats = {}
        for zoom_dir in TILES_DIR.iterdir():
            if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                zoom_level = int(zoom_dir.name)
                tile_count = sum(1 for _ in zoom_dir.rglob("*.png"))
                zoom_stats[zoom_level] = tile_count
        
        print("瓦片统计:")
        for zoom in sorted(zoom_stats.keys()):
            print(f"  缩放级别 {zoom}: {zoom_stats[zoom]} 个瓦片")
    
    app.run(host='0.0.0.0', port=8080, debug=False)