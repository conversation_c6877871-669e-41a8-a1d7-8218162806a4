#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载问题诊断工具
诊断HTTP 400错误和其他下载问题
"""

import requests
import time
import json
from pathlib import Path

class DownloadDiagnostic:
    def __init__(self):
        self.base_url = "https://overpass-api.de/api/interpreter"
        self.test_queries = {
            "simple": """
[out:xml][timeout:30];
(
  way["highway"="primary"](39.9,116.4,40.0,116.5);
);
out geom;
""",
            "medium": """
[out:xml][timeout:60];
(
  way["highway"~"^(primary|secondary|tertiary)"](39.9,116.4,40.0,116.5);
  way["building"](39.9,116.4,40.0,116.5);
);
out geom;
""",
            "complex": """
[out:xml][timeout:60];
(
  way["highway"~"^(motorway|trunk|primary|secondary|tertiary|unclassified|residential|service|track|path|footway|cycleway|bridleway|steps|pedestrian|living_street|bus_guideway|escape|raceway|road)"](39.9,116.4,40.0,116.5);
  way["building"](39.9,116.4,40.0,116.5);
  node["amenity"](39.9,116.4,40.0,116.5);
);
out geom;
"""
        }
    
    def test_network_connectivity(self):
        """测试网络连接"""
        print("🔍 测试网络连接...")
        
        test_urls = [
            "https://overpass-api.de",
            "https://www.google.com",
            "https://www.baidu.com"
        ]
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                print(f"✅ {url}: 连接正常 (状态码: {response.status_code})")
            except requests.exceptions.Timeout:
                print(f"❌ {url}: 连接超时")
            except requests.exceptions.ConnectionError:
                print(f"❌ {url}: 连接失败")
            except Exception as e:
                print(f"❌ {url}: 其他错误 ({e})")
    
    def test_overpass_api(self):
        """测试Overpass API"""
        print("\n🔍 测试Overpass API...")
        
        # 测试简单查询
        simple_query = """
[out:xml][timeout:30];
(
  way["highway"="primary"](39.9,116.4,40.0,116.5);
);
out geom;
"""
        
        try:
            print("📤 发送简单查询...")
            response = requests.post(
                self.base_url,
                data={"data": simple_query},
                timeout=60,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/xml, text/xml, */*',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ Overpass API 响应正常")
                print(f"📊 响应大小: {len(response.content)} 字节")
                
                # 检查响应内容
                if response.text.startswith('<?xml'):
                    print("✅ 响应格式正确 (XML)")
                else:
                    print("⚠️ 响应格式异常")
                    print(f"📄 响应内容前100字符: {response.text[:100]}")
                
                return True
            else:
                print(f"❌ Overpass API 响应异常: {response.status_code}")
                print(f"📄 错误内容: {response.text[:200]}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ Overpass API 请求超时")
            return False
        except requests.exceptions.ConnectionError:
            print("❌ Overpass API 连接失败")
            return False
        except Exception as e:
            print(f"❌ Overpass API 其他错误: {e}")
            return False
    
    def test_query_complexity(self):
        """测试查询复杂度"""
        print("\n🔍 测试查询复杂度...")
        
        for name, query in self.test_queries.items():
            print(f"\n📋 测试 {name} 查询...")
            
            try:
                start_time = time.time()
                response = requests.post(
                    self.base_url,
                    data={"data": query},
                    timeout=60
                )
                end_time = time.time()
                
                duration = end_time - start_time
                
                if response.status_code == 200:
                    print(f"✅ {name} 查询成功: {duration:.2f}秒, {len(response.content)}字节")
                else:
                    print(f"❌ {name} 查询失败: HTTP {response.status_code}")
                    print(f"📄 错误内容: {response.text[:200]}")
                    
            except requests.exceptions.Timeout:
                print(f"❌ {name} 查询超时")
            except Exception as e:
                print(f"❌ {name} 查询异常: {e}")
    
    def test_bbox_formats(self):
        """测试边界框格式"""
        print("\n🔍 测试边界框格式...")
        
        test_bboxes = [
            "39.9,116.4,40.0,116.5",  # 正确格式
            "39.9, 116.4, 40.0, 116.5",  # 带空格
            "39.9,116.4,40.0,116.5,",  # 多余逗号
            "39.9,116.4,40.0",  # 缺少坐标
            "39.9,116.4,40.0,116.5,116.6",  # 多余坐标
        ]
        
        base_query = """
[out:xml][timeout:30];
(
  way["highway"="primary"]({bbox});
);
out geom;
"""
        
        for bbox in test_bboxes:
            print(f"\n📋 测试边界框: {bbox}")
            
            try:
                query = base_query.format(bbox=bbox)
                response = requests.post(
                    self.base_url,
                    data={"data": query},
                    timeout=30
                )
                
                if response.status_code == 200:
                    print(f"✅ 边界框格式正确")
                else:
                    print(f"❌ 边界框格式错误: HTTP {response.status_code}")
                    print(f"📄 错误内容: {response.text[:200]}")
                    
            except Exception as e:
                print(f"❌ 边界框测试异常: {e}")
    
    def test_timeout_settings(self):
        """测试超时设置"""
        print("\n🔍 测试超时设置...")
        
        timeout_values = [30, 60, 120, 300]
        
        query = """
[out:xml][timeout:{timeout}];
(
  way["highway"~"^(primary|secondary|tertiary)"](39.9,116.4,40.0,116.5);
);
out geom;
"""
        
        for timeout in timeout_values:
            print(f"\n📋 测试超时: {timeout}秒")
            
            try:
                test_query = query.format(timeout=timeout)
                start_time = time.time()
                response = requests.post(
                    self.base_url,
                    data={"data": test_query},
                    timeout=timeout + 10  # 给请求额外10秒
                )
                end_time = time.time()
                
                duration = end_time - start_time
                
                if response.status_code == 200:
                    print(f"✅ 超时 {timeout}秒 成功: {duration:.2f}秒")
                else:
                    print(f"❌ 超时 {timeout}秒 失败: HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"❌ 超时 {timeout}秒 请求超时")
            except Exception as e:
                print(f"❌ 超时 {timeout}秒 异常: {e}")
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        print("\n🔍 测试并发请求...")
        
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        def single_request():
            query = """
[out:xml][timeout:30];
(
  way["highway"="primary"](39.9,116.4,40.0,116.5);
);
out geom;
"""
            try:
                response = requests.post(
                    self.base_url,
                    data={"data": query},
                    timeout=30
                )
                return response.status_code
            except Exception as e:
                return f"Error: {e}"
        
        concurrent_counts = [1, 2, 4, 8]
        
        for count in concurrent_counts:
            print(f"\n📋 测试 {count} 个并发请求...")
            
            start_time = time.time()
            results = []
            
            with ThreadPoolExecutor(max_workers=count) as executor:
                futures = [executor.submit(single_request) for _ in range(count)]
                
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_count = sum(1 for r in results if r == 200)
            print(f"✅ 并发 {count}: {success_count}/{count} 成功, 耗时 {duration:.2f}秒")
    
    def generate_diagnostic_report(self):
        """生成诊断报告"""
        print("\n📊 生成诊断报告...")
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": {}
        }
        
        # 运行所有测试
        tests = [
            ("network_connectivity", self.test_network_connectivity),
            ("overpass_api", self.test_overpass_api),
            ("query_complexity", self.test_query_complexity),
            ("bbox_formats", self.test_bbox_formats),
            ("timeout_settings", self.test_timeout_settings),
            ("concurrent_requests", self.test_concurrent_requests)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 运行测试: {test_name}")
            try:
                result = test_func()
                report["tests"][test_name] = {"status": "completed", "result": result}
            except Exception as e:
                report["tests"][test_name] = {"status": "failed", "error": str(e)}
        
        # 保存报告
        report_file = "download_diagnostic_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 诊断报告已保存: {report_file}")
        
        return report
    
    def provide_solutions(self):
        """提供解决方案"""
        print("\n💡 常见问题解决方案:")
        print("=" * 50)
        
        print("\n1. HTTP 400错误:")
        print("   - 检查查询语句格式")
        print("   - 验证边界框坐标")
        print("   - 减少查询复杂度")
        print("   - 降低超时时间")
        
        print("\n2. 超时错误:")
        print("   - 减少查询范围")
        print("   - 降低并发数")
        print("   - 增加超时时间")
        print("   - 使用更简单的查询")
        
        print("\n3. 连接错误:")
        print("   - 检查网络连接")
        print("   - 使用代理服务器")
        print("   - 更换API服务器")
        print("   - 检查防火墙设置")
        
        print("\n4. 性能优化:")
        print("   - 使用合适的并发数")
        print("   - 分批下载数据")
        print("   - 缓存已下载数据")
        print("   - 使用断点续传")

def main():
    """主函数"""
    diagnostic = DownloadDiagnostic()
    
    print("🔧 下载问题诊断工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择诊断类型:")
        print("1. 完整诊断 (推荐)")
        print("2. 网络连接测试")
        print("3. Overpass API测试")
        print("4. 查询复杂度测试")
        print("5. 边界框格式测试")
        print("6. 超时设置测试")
        print("7. 并发请求测试")
        print("8. 查看解决方案")
        print("9. 退出")
        
        choice = input("\n请输入选择 (1-9): ").strip()
        
        if choice == "1":
            diagnostic.generate_diagnostic_report()
        
        elif choice == "2":
            diagnostic.test_network_connectivity()
        
        elif choice == "3":
            diagnostic.test_overpass_api()
        
        elif choice == "4":
            diagnostic.test_query_complexity()
        
        elif choice == "5":
            diagnostic.test_bbox_formats()
        
        elif choice == "6":
            diagnostic.test_timeout_settings()
        
        elif choice == "7":
            diagnostic.test_concurrent_requests()
        
        elif choice == "8":
            diagnostic.provide_solutions()
        
        elif choice == "9":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
