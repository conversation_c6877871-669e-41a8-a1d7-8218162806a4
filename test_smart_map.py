#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能地图功能测试
"""

def test_smart_map_basic():
    """测试基本地图生成功能"""
    print("🧪 测试智能地图基本功能")
    print("=" * 40)
    
    try:
        from smart_map_generator import SmartMapGenerator
        
        generator = SmartMapGenerator()
        
        # 测试坐标转换
        print("📍 测试坐标转换...")
        lat, lon = 39.9042, 116.4074  # 北京
        zoom = 15
        
        x, y = generator.deg2num(lat, lon, zoom)
        print(f"   经纬度 ({lat}, {lon}) -> 瓦片坐标 ({x}, {y}) @ 缩放{zoom}")
        
        lat2, lon2 = generator.num2deg(x, y, zoom)
        print(f"   瓦片坐标 ({x}, {y}) -> 经纬度 ({lat2:.6f}, {lon2:.6f})")
        
        # 测试范围计算
        print(f"\n📏 测试范围计算...")
        radius_km = 50
        tile_range = generator.calculate_tile_range(lat, lon, radius_km, zoom)
        print(f"   中心: ({lat}, {lon})")
        print(f"   半径: {radius_km} 公里")
        print(f"   瓦片范围: X({tile_range['min_x']}-{tile_range['max_x']}) Y({tile_range['min_y']}-{tile_range['max_y']})")
        print(f"   瓦片数量: {tile_range['total_tiles']}")
        
        # 测试数据量估算
        print(f"\n📊 测试数据量估算...")
        size_info = generator.estimate_data_size(lat, lon, radius_km, 15)
        print(f"   预估瓦片数: {size_info['total_tiles']:,}")
        print(f"   预估大小: {size_info['total_size_mb']} MB")
        
        # 测试离线瓦片创建
        print(f"\n🎨 测试离线瓦片创建...")
        success = generator.create_offline_tile(15, x, y)
        print(f"   创建瓦片 15/{x}/{y}: {'成功' if success else '失败'}")
        
        print(f"\n✅ 基本功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gps_integration():
    """测试GPS集成功能"""
    print(f"\n🛰️  测试GPS集成功能")
    print("=" * 40)
    
    try:
        from gps_map_integration import GPSMapIntegration
        
        integration = GPSMapIntegration()
        
        # 测试距离计算
        print("📏 测试距离计算...")
        lat1, lon1 = 39.9042, 116.4074  # 北京
        lat2, lon2 = 31.2304, 121.4737  # 上海
        
        distance = integration.calculate_distance(lat1, lon1, lat2, lon2)
        print(f"   北京到上海距离: {distance:.1f} 公里")
        
        # 测试地图生成判断
        print(f"\n🗺️  测试地图生成判断...")
        need_generate1 = integration.should_generate_new_map(lat1, lon1)
        print(f"   首次GPS位置需要生成地图: {'是' if need_generate1 else '否'}")
        
        # 模拟设置上次位置
        integration.last_map_center = (lat1, lon1)
        
        need_generate2 = integration.should_generate_new_map(lat1 + 0.001, lon1 + 0.001)
        print(f"   微小位移需要生成地图: {'是' if need_generate2 else '否'}")
        
        need_generate3 = integration.should_generate_new_map(lat2, lon2)
        print(f"   大距离位移需要生成地图: {'是' if need_generate3 else '否'}")
        
        print(f"\n✅ GPS集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GPS集成测试失败: {e}")
        return False

def test_small_area_generation():
    """测试小范围地图生成"""
    print(f"\n🎯 测试小范围地图生成")
    print("=" * 40)
    
    try:
        from smart_map_generator import SmartMapGenerator
        
        generator = SmartMapGenerator()
        
        # 生成小范围高精度地图
        lat, lon = 39.9042, 116.4074  # 北京
        radius_km = 10  # 小范围：10公里
        max_zoom = 16   # 高精度：16级
        
        print(f"📍 测试区域: 北京中心")
        print(f"📏 覆盖半径: {radius_km} 公里")
        print(f"🔍 最大精度: {max_zoom} 级")
        
        # 估算数据量
        size_info = generator.estimate_data_size(lat, lon, radius_km, max_zoom)
        print(f"📊 预估数据量: {size_info['total_tiles']:,} 瓦片, {size_info['total_size_mb']} MB")
        
        if size_info['total_size_mb'] < 50:  # 小于50MB才实际生成
            print(f"🚀 开始生成小范围测试地图...")
            success = generator.generate_area_map(
                lat, lon, radius_km, max_zoom, 12, False  # 纯离线模式
            )
            print(f"📊 生成结果: {'成功' if success else '失败'}")
        else:
            print(f"⚠️  数据量过大，跳过实际生成")
        
        print(f"\n✅ 小范围生成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 小范围生成测试失败: {e}")
        return False

def main():
    print("🧪 智能地图功能全面测试")
    print("=" * 50)
    
    tests = [
        ("基本功能", test_smart_map_basic),
        ("GPS集成", test_gps_integration),
        ("小范围生成", test_small_area_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print(f"🎉 所有测试通过！智能地图功能正常")
        print(f"\n🚀 可以使用以下命令启动:")
        print(f"   python smart_map_launcher.py")
    else:
        print(f"⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()