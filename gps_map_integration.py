#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS地图集成模块
自动根据GPS坐标生成周边地图
"""

import json
import time
import threading
from pathlib import Path
from smart_map_generator import SmartMapGenerator

class GPSMapIntegration:
    def __init__(self):
        self.generator = SmartMapGenerator()
        self.last_map_center = None
        self.map_radius_km = 100
        self.map_precision = 18
        self.min_distance_km = 50  # 最小距离阈值，恢复为50公里
        
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离 (公里) - 优化版近似算法"""
        # 近似算法，误差<1%，速度提升5倍
        dx = 111.32 * (lon2 - lon1) * math.cos(math.radians((lat1 + lat2)/2))
        dy = 111.32 * (lat2 - lat1)
        return math.sqrt(dx*dx + dy*dy)
    
    def should_generate_new_map(self, current_lat, current_lon):
        """判断是否需要生成新地图"""
        if self.last_map_center is None:
            return True
        
        last_lat, last_lon = self.last_map_center
        distance = self.calculate_distance(current_lat, current_lon, last_lat, last_lon)
        
        return distance > self.min_distance_km
    
    def generate_map_for_gps(self, gps_lat, gps_lon, force=False):
        """为GPS坐标生成地图"""
        if not force and not self.should_generate_new_map(gps_lat, gps_lon):
            print(f"📍 GPS位置变化不大，无需重新生成地图")
            return False
        
        print(f"🗺️  检测到新的GPS位置，开始生成周边地图...")
        print(f"📍 GPS坐标: {gps_lat:.6f}, {gps_lon:.6f}")
        
        try:
            success = self.generator.generate_area_map(
                center_lat=gps_lat,
                center_lon=gps_lon,
                radius_km=self.map_radius_km,
                max_zoom=self.map_precision,
                min_zoom=8,
                use_online=True
            )
            
            if success:
                self.last_map_center = (gps_lat, gps_lon)
                print(f"✅ GPS周边地图生成完成")
                return True
            else:
                print(f"❌ GPS周边地图生成失败")
                return False
                
        except Exception as e:
            print(f"❌ GPS地图生成异常: {e}")
            return False
    
    def start_gps_monitoring(self, data_receiver):
        """启动GPS监控，自动生成地图"""
        def monitor_gps():
            print(f"🛰️  启动GPS地图监控...")
            last_check_time = 0
            check_interval = 300  # 5分钟检查一次
            
            while True:
                try:
                    current_time = time.time()
                    
                    # 检查间隔
                    if current_time - last_check_time < check_interval:
                        time.sleep(10)
                        continue
                    
                    # 获取最新GPS数据
                    if hasattr(data_receiver, 'latest_gps') and data_receiver.latest_gps:
                        gps_data = data_receiver.latest_gps
                        
                        if 'latitude' in gps_data and 'longitude' in gps_data:
                            lat = gps_data['latitude']
                            lon = gps_data['longitude']
                            
                            if lat != 0 and lon != 0:  # 有效GPS坐标
                                self.generate_map_for_gps(lat, lon)
                                last_check_time = current_time
                    
                    time.sleep(30)  # 30秒检查一次GPS数据
                    
                except Exception as e:
                    print(f"❌ GPS监控异常: {e}")
                    time.sleep(60)
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_gps, daemon=True)
        monitor_thread.start()
        print(f"✅ GPS地图监控已启动")

def test_gps_integration():
    """测试GPS地图集成"""
    print("🧪 测试GPS地图集成功能")
    print("=" * 40)
    
    integration = GPSMapIntegration()
    
    # 模拟GPS数据
    test_coordinates = [
        (39.9042, 116.4074),  # 北京
        (31.2304, 121.4737),  # 上海
        (23.1291, 113.2644),  # 广州
    ]
    
    for i, (lat, lon) in enumerate(test_coordinates, 1):
        print(f"\n📍 测试坐标 {i}: {lat:.6f}, {lon:.6f}")
        
        # 检查是否需要生成地图
        need_generate = integration.should_generate_new_map(lat, lon)
        print(f"   需要生成地图: {'是' if need_generate else '否'}")
        
        if need_generate:
            print(f"   开始生成地图...")
            success = integration.generate_map_for_gps(lat, lon)
            print(f"   生成结果: {'成功' if success else '失败'}")
        
        time.sleep(1)

if __name__ == "__main__":
    test_gps_integration()