#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 检查所有瓦片是否正常工作
"""

import os
import requests
import math
from pathlib import Path
from PIL import Image

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def test_tile_via_server(z, x, y):
    """通过服务器测试瓦片"""
    url = f"http://localhost:8080/tiles/{z}/{x}/{y}.png"
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return True, f"服务器返回 {len(response.content)} bytes"
        else:
            return False, f"服务器返回状态码 {response.status_code}"
    except Exception as e:
        return False, f"服务器请求失败: {e}"

def test_tile_file(z, x, y):
    """测试瓦片文件"""
    tile_path = Path(f"static/tiles/{z}/{x}/{y}.png")
    
    if not tile_path.exists():
        return False, "文件不存在"
    
    try:
        file_size = tile_path.stat().st_size
        if file_size < 1000:
            return False, f"文件太小 ({file_size} bytes)"
        
        with Image.open(tile_path) as img:
            width, height = img.size
            if width != 256 or height != 256:
                return False, f"尺寸错误 ({width}x{height})"
            
            # 检查是否有内容
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                unique_colors = len(colors)
                if unique_colors < 10:
                    return False, f"颜色太少 ({unique_colors}色)"
                
                return True, f"正常 ({file_size} bytes, {unique_colors}色)"
            else:
                return False, "无法分析颜色"
                
    except Exception as e:
        return False, f"文件错误: {e}"

def main():
    """主函数"""
    print("🔍 最终验证 - 检查瓦片系统")
    print("=" * 50)
    
    # 北京天安门坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 测试不同缩放级别
    test_zooms = [10, 11, 12, 13, 14, 15]
    
    print("📊 测试文件系统瓦片")
    print("-" * 30)
    
    file_results = {}
    for z in test_zooms:
        x, y = deg2num(beijing_lat, beijing_lon, z)
        success, message = test_tile_file(z, x, y)
        file_results[z] = (success, message)
        
        status = "✅" if success else "❌"
        print(f"{status} 缩放级别 {z} ({z}/{x}/{y}): {message}")
    
    print(f"\n📊 测试服务器瓦片")
    print("-" * 30)
    
    server_results = {}
    for z in test_zooms:
        x, y = deg2num(beijing_lat, beijing_lon, z)
        success, message = test_tile_via_server(z, x, y)
        server_results[z] = (success, message)
        
        status = "✅" if success else "❌"
        print(f"{status} 缩放级别 {z} ({z}/{x}/{y}): {message}")
    
    # 统计结果
    print(f"\n📈 统计结果")
    print("-" * 30)
    
    file_success = sum(1 for success, _ in file_results.values() if success)
    server_success = sum(1 for success, _ in server_results.values() if success)
    
    print(f"文件系统: {file_success}/{len(test_zooms)} 成功")
    print(f"服务器: {server_success}/{len(test_zooms)} 成功")
    
    # 检查14级瓦片特别情况
    print(f"\n🎯 14级瓦片特别检查")
    print("-" * 30)
    
    z = 14
    x, y = deg2num(beijing_lat, beijing_lon, z)
    
    print(f"14级瓦片坐标: {z}/{x}/{y}")
    
    # 检查周围瓦片
    for dx in [-1, 0, 1]:
        for dy in [-1, 0, 1]:
            test_x, test_y = x + dx, y + dy
            success, message = test_tile_file(z, test_x, test_y)
            status = "✅" if success else "❌"
            print(f"{status} {z}/{test_x}/{test_y}: {message}")
    
    # 最终结论
    print(f"\n🎉 最终结论")
    print("-" * 30)
    
    if file_success >= len(test_zooms) * 0.8:  # 80%以上成功
        print("✅ 瓦片系统基本正常")
        if server_success >= len(test_zooms) * 0.8:
            print("✅ 服务器工作正常")
        else:
            print("⚠️  服务器可能有问题")
    else:
        print("❌ 瓦片系统存在问题")
    
    # 特别检查14级
    if file_results.get(14, (False, ""))[0]:
        print("✅ 14级瓦片问题已解决")
    else:
        print("❌ 14级瓦片仍有问题")

if __name__ == "__main__":
    main()
