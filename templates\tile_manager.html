<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瓦片管理器</title>
    <style>
        body { margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        .header { background: #333; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { margin: 0 0 15px 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .stat { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .stat-label { font-weight: bold; }
        .stat-value { color: #007bff; }
        .system-status { display: flex; align-items: center; margin-bottom: 10px; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; margin-right: 10px; }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎛️ 瓦片管理器</h1>
        <p>20米精度离线地图系统 - 瓦片监控与管理</p>
    </div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 瓦片统计</h3>
            <div id="tile-stats">加载中...</div>
            <button class="btn" onclick="refreshStats()">刷新统计</button>
        </div>
        
        <div class="card">
            <h3>🌐 系统状态</h3>
            <div id="system-status">检查中...</div>
            <button class="btn" onclick="refreshStatus()">刷新状态</button>
        </div>
        
        <div class="card">
            <h3>📁 OSM文件状态</h3>
            <div id="osm-status">检查中...</div>
        </div>
        
        <div class="card">
            <h3>🗺️ 覆盖范围</h3>
            <div id="coverage-info">分析中...</div>
            <button class="btn" onclick="analyzeCoverage()">分析覆盖</button>
        </div>
    </div>

    <script>
        function refreshStats() {
            document.getElementById('tile-stats').innerHTML = '加载中...';
            fetch('/api/tile-stats')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    for (const [system, stats] of Object.entries(data)) {
                        html += `<div style="margin-bottom: 15px; border-left: 3px solid #007bff; padding-left: 10px;">`;
                        html += `<strong>${system}</strong><br>`;
                        if (stats.exists) {
                            html += `<div class="stat"><span class="stat-label">瓦片数量:</span><span class="stat-value">${stats.total_tiles}</span></div>`;
                            html += `<div class="stat"><span class="stat-label">占用空间:</span><span class="stat-value">${stats.total_size_mb} MB</span></div>`;
                            html += `<div class="stat"><span class="stat-label">缩放级别:</span><span class="stat-value">${stats.zoom_levels.join(', ')}</span></div>`;
                        } else {
                            html += `<div style="color: #dc3545;">目录不存在</div>`;
                        }
                        html += `</div>`;
                    }
                    document.getElementById('tile-stats').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('tile-stats').innerHTML = '加载失败: ' + error.message;
                });
        }
        
        function refreshStatus() {
            document.getElementById('system-status').innerHTML = '检查中...';
            fetch('/api/system-status')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    
                    // OSM文件状态
                    if (data.osm_file.exists) {
                        html += `<div class="system-status"><div class="status-indicator status-online"></div>OSM文件: ${data.osm_file.size_gb}GB</div>`;
                    } else {
                        html += `<div class="system-status"><div class="status-indicator status-offline"></div>OSM文件: 不存在</div>`;
                    }
                    
                    // 系统状态
                    data.systems.forEach(system => {
                        const statusClass = system.status ? 'status-online' : 'status-offline';
                        const statusText = system.status ? '运行中' : '离线';
                        html += `<div class="system-status">`;
                        html += `<div class="status-indicator ${statusClass}"></div>`;
                        html += `${system.name}: ${statusText}`;
                        if (system.status) {
                            html += ` <a href="${system.url}" target="_blank" style="margin-left: 10px; color: #007bff;">访问</a>`;
                        }
                        html += `</div>`;
                    });
                    
                    document.getElementById('system-status').innerHTML = html;
                    document.getElementById('osm-status').innerHTML = data.osm_file.exists ? 
                        `✅ 文件存在<br>📁 大小: ${data.osm_file.size_gb}GB<br>📍 路径: ${data.osm_file.path}` :
                        `❌ 文件不存在<br>📍 期望路径: ${data.osm_file.path}`;
                })
                .catch(error => {
                    document.getElementById('system-status').innerHTML = '检查失败: ' + error.message;
                });
        }
        
        function analyzeCoverage() {
            document.getElementById('coverage-info').innerHTML = '分析中...';
            // 这里可以添加覆盖范围分析的具体实现
            setTimeout(() => {
                document.getElementById('coverage-info').innerHTML = '覆盖范围分析功能开发中...';
            }, 1000);
        }
        
        // 自动刷新
        setInterval(() => {
            refreshStats();
            refreshStatus();
        }, 30000); // 30秒刷新一次
        
        // 初始加载
        refreshStats();
        refreshStatus();
    </script>
</body>
</html>