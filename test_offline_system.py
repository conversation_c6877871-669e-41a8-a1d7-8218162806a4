#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线GPS监控系统测试脚本
"""

import requests
import time

def test_offline_system():
    """测试离线GPS监控系统"""
    print("🔧 离线GPS机井监控系统测试")
    print("=" * 50)
    
    # 等待系统启动
    print("⏳ 等待系统启动...")
    time.sleep(3)
    
    # 测试1: 检查GPS监控系统
    print("1. 测试GPS监控系统...")
    try:
        response = requests.get('http://127.0.0.1:5000/api/statistics', timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ GPS监控系统正常 - 总机井: {stats['total_wells']}")
        else:
            print(f"   ❌ GPS监控系统异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ GPS监控系统连接失败: {e}")
    
    # 测试2: 检查地图配置
    print("2. 测试地图配置...")
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ 地图配置正常 - 离线模式: {config['offline_mode']}")
            print(f"   📍 瓦片服务器: {config['tile_server_url']}")
        else:
            print(f"   ❌ 地图配置异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 地图配置获取失败: {e}")
    
    # 测试3: 检查瓦片服务器
    print("3. 测试瓦片服务器...")
    try:
        response = requests.get('http://127.0.0.1:8080/config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ 瓦片服务器正常 - 状态: {config.get('status', 'unknown')}")
        else:
            print(f"   ❌ 瓦片服务器异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 瓦片服务器连接失败: {e}")
    
    # 测试4: 测试GPS定位功能
    print("4. 测试GPS定位功能...")
    try:
        response = requests.post(
            'http://127.0.0.1:5000/api/locate',
            json={
                "lat": 39.9042,
                "lon": 116.4074,
                "radius": 1000
            },
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ GPS定位功能正常 - 找到 {result['count']} 个附近机井")
        else:
            print(f"   ❌ GPS定位功能异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ GPS定位功能失败: {e}")
    
    print("\n🎯 测试完成!")
    print("=" * 50)
    print("📱 请在浏览器中访问 http://127.0.0.1:5000 查看系统界面")
    print("🗺️  瓦片服务器运行在 http://127.0.0.1:8080")
    print("🔧 使用 python gps_data_simulator.py 启动数据模拟器")

if __name__ == "__main__":
    test_offline_system()
