#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终瓦片测试
验证第14级瓦片是否包含真实地理信息
"""

import os
import sys
from pathlib import Path
from PIL import Image

def test_tile_content():
    """测试瓦片内容"""
    print("🧪 最终瓦片内容测试")
    print("=" * 50)
    
    tiles_dir = Path("F:/monitor1/static/tiles")
    if not tiles_dir.exists():
        print(f"❌ 瓦片目录不存在: {tiles_dir}")
        return False
    
    # 测试第14级瓦片
    test_tile = tiles_dir / "14" / "13459" / "6106.png"
    
    if not test_tile.exists():
        print(f"❌ 测试瓦片不存在: {test_tile}")
        return False
    
    print(f"🔍 测试瓦片: {test_tile}")
    
    try:
        # 检查文件大小
        file_size = test_tile.stat().st_size
        print(f"📊 文件大小: {file_size} 字节")
        
        # 检查图像内容
        with Image.open(test_tile) as img:
            width, height = img.size
            mode = img.mode
            
            print(f"📐 图像尺寸: {width}x{height}")
            print(f"🎨 颜色模式: {mode}")
            
            # 分析图像内容
            if file_size < 1000:
                print(f"❌ 文件过小，可能是空白瓦片")
                return False
            elif file_size > 10000:
                print(f"⚠️  文件较大，可能包含过多细节")
            else:
                print(f"✅ 文件大小正常")
            
            # 检查图像是否包含有意义的内容
            # 转换为RGB模式进行分析
            if mode != 'RGB':
                img = img.convert('RGB')
            
            # 获取图像数据
            pixels = list(img.getdata())
            
            # 分析颜色分布
            color_counts = {}
            for pixel in pixels:
                color_counts[pixel] = color_counts.get(pixel, 0) + 1
            
            # 检查是否有足够的颜色变化
            unique_colors = len(color_counts)
            print(f"🎨 唯一颜色数: {unique_colors}")
            
            if unique_colors < 10:
                print(f"❌ 颜色变化太少，可能是简单图形")
                return False
            elif unique_colors > 1000:
                print(f"✅ 颜色变化丰富，可能包含真实地理信息")
            else:
                print(f"⚠️  颜色变化适中")
            
            # 检查是否有非背景色的像素
            background_color = (240, 248, 255)  # #f0f8ff
            non_background_pixels = 0
            
            for pixel in pixels:
                if pixel != background_color:
                    non_background_pixels += 1
            
            non_background_ratio = non_background_pixels / len(pixels)
            print(f"📊 非背景像素比例: {non_background_ratio:.2%}")
            
            if non_background_ratio < 0.1:
                print(f"❌ 非背景像素太少，可能是空白瓦片")
                return False
            elif non_background_ratio > 0.5:
                print(f"✅ 非背景像素丰富，包含大量内容")
            else:
                print(f"⚠️  非背景像素适中")
            
            return True
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_multiple_tiles():
    """测试多个瓦片"""
    print(f"\n🔍 测试多个第14级瓦片...")
    
    tiles_dir = Path("F:/monitor1/static/tiles/14")
    if not tiles_dir.exists():
        print(f"❌ 第14级瓦片目录不存在")
        return False
    
    test_results = []
    
    # 测试几个不同的瓦片
    test_coords = [
        (13459, 6106),
        (13460, 6107),
        (13461, 6108),
        (13462, 6109),
        (13463, 6110),
    ]
    
    for x, y in test_coords:
        tile_path = tiles_dir / str(x) / f"{y}.png"
        
        if tile_path.exists():
            file_size = tile_path.stat().st_size
            status = "正常" if file_size > 1000 else "异常"
            test_results.append((x, y, file_size, status))
            print(f"  📍 {x}/{y}: {file_size} 字节 - {status}")
        else:
            print(f"  ❌ {x}/{y}: 文件不存在")
    
    # 统计结果
    if test_results:
        normal_count = sum(1 for _, _, _, status in test_results if status == "正常")
        total_count = len(test_results)
        success_rate = normal_count / total_count * 100
        
        print(f"\n📊 测试结果统计:")
        print(f"   总测试数: {total_count}")
        print(f"   正常数: {normal_count}")
        print(f"   成功率: {success_rate:.1f}%")
        
        return success_rate >= 80
    else:
        print(f"❌ 没有找到可测试的瓦片")
        return False

def main():
    """主函数"""
    print("🚀 最终瓦片测试")
    print("=" * 50)
    
    try:
        # 测试单个瓦片内容
        content_ok = test_tile_content()
        
        # 测试多个瓦片
        multiple_ok = test_multiple_tiles()
        
        print(f"\n🎯 最终结果:")
        print(f"=" * 50)
        
        if content_ok and multiple_ok:
            print(f"🎉 第14级瓦片修复成功！")
            print(f"✅ 瓦片包含真实地理信息")
            print(f"✅ 多个瓦片质量正常")
            print(f"\n💡 建议:")
            print(f"   1. 启动系统: python start_system.py")
            print(f"   2. 访问地图: http://localhost:5000")
            print(f"   3. 测试第14级缩放显示效果")
        else:
            print(f"⚠️  第14级瓦片修复部分成功")
            if not content_ok:
                print(f"❌ 瓦片内容有问题")
            if not multiple_ok:
                print(f"❌ 多个瓦片质量有问题")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False
    
    return content_ok and multiple_ok

if __name__ == "__main__":
    main()

