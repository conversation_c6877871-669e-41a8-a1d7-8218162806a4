#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载的瓦片内容
"""

import os
from pathlib import Path
from PIL import Image
import math

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return x, y

def analyze_tile(tile_path):
    """分析瓦片内容"""
    if not tile_path.exists():
        return "文件不存在"
    
    try:
        with Image.open(tile_path) as img:
            # 基本信息
            width, height = img.size
            mode = img.mode
            file_size = tile_path.stat().st_size
            
            # 分析颜色
            colors = img.getcolors(maxcolors=256*256*256)
            if colors:
                unique_colors = len(colors)
                # 检查是否主要是白色背景
                white_pixels = 0
                total_pixels = width * height
                
                for count, color in colors:
                    if isinstance(color, tuple) and len(color) >= 3:
                        r, g, b = color[:3]
                        if r > 240 and g > 240 and b > 240:  # 接近白色
                            white_pixels += count
                
                white_ratio = white_pixels / total_pixels if total_pixels > 0 else 0
                
                return {
                    'size': f"{width}x{height}",
                    'mode': mode,
                    'file_size': file_size,
                    'unique_colors': unique_colors,
                    'white_ratio': white_ratio,
                    'has_content': white_ratio < 0.9  # 如果白色像素少于90%，认为有内容
                }
            else:
                return "无法分析颜色"
                
    except Exception as e:
        return f"分析失败: {e}"

def main():
    """主函数"""
    print("🔍 测试下载的瓦片内容")
    print("=" * 50)
    
    # 北京天安门坐标
    beijing_lat = 39.9042
    beijing_lon = 116.4074
    
    # 测试不同缩放级别
    test_zooms = [10, 14]
    
    for z in test_zooms:
        print(f"\n📊 测试缩放级别 {z}")
        print("-" * 30)
        
        # 计算对应的瓦片坐标
        x, y = deg2num(beijing_lat, beijing_lon, z)
        tile_path = Path(f"static/tiles/{z}/{x}/{y}.png")
        
        print(f"瓦片坐标: {z}/{x}/{y}")
        print(f"文件路径: {tile_path}")
        
        result = analyze_tile(tile_path)
        
        if isinstance(result, dict):
            print(f"✅ 瓦片存在")
            print(f"   尺寸: {result['size']}")
            print(f"   模式: {result['mode']}")
            print(f"   文件大小: {result['file_size']} bytes")
            print(f"   唯一颜色数: {result['unique_colors']}")
            print(f"   白色像素比例: {result['white_ratio']:.2%}")
            
            if result['has_content']:
                print(f"   ✅ 瓦片包含有效内容")
            else:
                print(f"   ❌ 瓦片主要是空白")
        else:
            print(f"❌ {result}")
    
    # 测试14级瓦片周围的几个瓦片
    print(f"\n🔍 测试14级瓦片周围内容")
    print("-" * 30)
    
    z = 14
    center_x, center_y = deg2num(beijing_lat, beijing_lon, z)
    
    test_coords = [
        (center_x, center_y),      # 中心
        (center_x-1, center_y),    # 左边
        (center_x+1, center_y),    # 右边
        (center_x, center_y-1),    # 上边
        (center_x, center_y+1),    # 下边
    ]
    
    for x, y in test_coords:
        tile_path = Path(f"static/tiles/{z}/{x}/{y}.png")
        result = analyze_tile(tile_path)
        
        if isinstance(result, dict):
            status = "✅" if result['has_content'] else "❌"
            print(f"{status} {z}/{x}/{y}: {result['unique_colors']}色, {result['white_ratio']:.1%}白")
        else:
            print(f"❌ {z}/{x}/{y}: {result}")

if __name__ == "__main__":
    main()
