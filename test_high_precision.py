#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高精度GPS监控系统
"""

import requests
import webbrowser
import time

def test_high_precision_system():
    """测试高精度GPS监控系统"""
    print("🎯 高精度GPS监控系统测试")
    print("=" * 60)
    
    # 测试GPS监控系统
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ GPS监控系统正常")
            print(f"📍 中心点: {config['center']}")
            print(f"🔍 初始缩放: {config['initial_zoom']}")
            print(f"📊 缩放范围: {config['min_zoom']}-{config['max_zoom']}")
            print(f"🎯 GPS定位缩放: {config['gps_zoom']}")
            print(f"🌐 矢量数据: {config['vector_data_available']}")
            
            # 检查精度提升
            if config['max_zoom'] >= 20:
                print("✅ 最高精度支持: 20级 (最高精度)")
            else:
                print(f"⚠️  当前最高精度: {config['max_zoom']}级")
                
        else:
            print(f"❌ GPS监控系统异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GPS监控系统连接失败: {e}")
        return False
    
    print("\n🎯 精度级别说明:")
    print("=" * 60)
    print("15级 - 城市级别 (约1-2公里精度)")
    print("16级 - 区域级别 (约500米精度)")
    print("17级 - 街道级别 (约100-200米精度)")
    print("18级 - 高精度 (约50-100米精度)")
    print("19级 - 超高精度 (约10-20米精度)")
    print("20级 - 最高精度 (约1-5米精度)")
    
    print("\n📍 高精度定位测试坐标:")
    print("=" * 60)
    print("北京天安门: 纬度 39.9042, 经度 116.4074")
    print("上海外滩: 纬度 31.2397, 经度 121.4999")
    print("广州塔: 纬度 23.1090, 经度 113.3245")
    print("成都春熙路: 纬度 30.6624, 经度 104.0633")
    print("恩施市中心: 纬度 30.295, 经度 109.486")
    
    print("\n🎯 使用说明:")
    print("=" * 60)
    print("1. 输入GPS坐标")
    print("2. 选择精度级别 (15-20级)")
    print("3. 点击'定位到GPS坐标'进行普通定位")
    print("4. 点击'最高精度定位'直接定位到20级精度")
    print("5. 系统会自动加载该区域的矢量数据")
    
    return True

def show_precision_comparison():
    """显示精度对比"""
    print("\n📊 精度对比表:")
    print("=" * 60)
    print("级别    精度范围      适用场景")
    print("-" * 60)
    print("15级    1-2公里      城市概览")
    print("16级    500米        区域监控")
    print("17级    100-200米    街道监控")
    print("18级    50-100米     机井监控")
    print("19级    10-20米      高精度监控")
    print("20级    1-5米        最高精度监控")
    
    print("\n🎯 监控应用建议:")
    print("=" * 60)
    print("• 机井位置监控: 建议使用18-19级")
    print("• 精确位置定位: 建议使用20级")
    print("• 区域概览: 建议使用15-16级")
    print("• 街道导航: 建议使用17-18级")

if __name__ == "__main__":
    success = test_high_precision_system()
    show_precision_comparison()
    
    if success:
        print("\n✅ 高精度GPS监控系统测试完成！")
        print("🌐 正在打开系统...")
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
        print("\n💡 提示: 在系统中选择20级精度进行最高精度定位！")
    else:
        print("\n❌ 系统测试失败，请检查错误信息！")