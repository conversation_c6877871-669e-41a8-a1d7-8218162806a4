#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试省会城市定位功能
"""

import requests
import json

def test_capital_cities():
    """测试省会城市定位功能"""
    print("🏛️ 测试省会城市定位功能")
    print("=" * 50)
    
    # 测试几个主要省份
    test_provinces = ["四川", "北京", "广东", "江苏", "浙江", "山东", "河南", "湖北", "湖南"]
    
    for province in test_provinces:
        print(f"\n🔍 测试省份: {province}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/search-poi?q={province}")
            if response.status_code == 200:
                results = response.json()
                # 查找区域类型的结果
                region_results = [r for r in results if r['type'] == 'region']
                if region_results:
                    result = region_results[0]
                    print(f"   ✅ 省份: {result['name']}")
                    print(f"   📍 坐标: {result['lat']:.4f}, {result['lon']:.4f}")
                    if 'capital_name' in result:
                        print(f"   🏛️ 省会: {result['capital_name']}")
                    else:
                        print(f"   ⚠️ 未找到省会信息")
                else:
                    print(f"   ❌ 未找到区域结果")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    # 测试快速搜索
    print(f"\n🚀 测试快速搜索:")
    for province in test_provinces[:3]:  # 只测试前3个
        print(f"\n🔍 快速搜索: {province}")
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/quick-search?q={province}")
            if response.status_code == 200:
                results = response.json()
                region_results = [r for r in results if r['type'] == 'region']
                if region_results:
                    result = region_results[0]
                    print(f"   ✅ 省份: {result['name']}")
                    print(f"   📍 坐标: {result['lat']:.4f}, {result['lon']:.4f}")
                    if 'capital_name' in result:
                        print(f"   🏛️ 省会: {result['capital_name']}")
                    else:
                        print(f"   ⚠️ 未找到省会信息")
                else:
                    print(f"   ❌ 未找到区域结果")
            else:
                print(f"   ❌ HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    test_capital_cities()
