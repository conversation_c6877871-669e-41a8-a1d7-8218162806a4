#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矢量地图查看器
支持矢量数据显示和定位功能
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from flask import Flask, render_template, request, jsonify
import math

app = Flask(__name__)

class VectorMapViewer:
    def __init__(self, vector_data_dir="static/vector_data"):
        self.vector_data_dir = Path(vector_data_dir)
        self.vector_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 地图配置
        self.map_config = {
            'center_lat': 39.9042,
            'center_lng': 116.4074,
            'zoom': 12,
            'min_zoom': 8,
            'max_zoom': 18
        }
        
        # 著名地标数据库
        self.famous_places = {
            '天安门': {'lat': 39.9042, 'lng': 116.4074, 'type': 'landmark', 'city': '北京'},
            '故宫': {'lat': 39.9163, 'lng': 116.3972, 'type': 'landmark', 'city': '北京'},
            '天坛': {'lat': 39.8823, 'lng': 116.4066, 'type': 'landmark', 'city': '北京'},
            '颐和园': {'lat': 39.9999, 'lng': 116.2755, 'type': 'landmark', 'city': '北京'},
            '长城': {'lat': 40.4319, 'lng': 116.5704, 'type': 'landmark', 'city': '北京'},
            '鸟巢': {'lat': 39.9934, 'lng': 116.3883, 'type': 'landmark', 'city': '北京'},
            '水立方': {'lat': 39.9948, 'lng': 116.3903, 'type': 'landmark', 'city': '北京'},
            '王府井': {'lat': 39.9097, 'lng': 116.4134, 'type': 'commercial', 'city': '北京'},
            '西单': {'lat': 39.9139, 'lng': 116.3669, 'type': 'commercial', 'city': '北京'},
            '三里屯': {'lat': 39.9378, 'lng': 116.4434, 'type': 'commercial', 'city': '北京'},
            '外滩': {'lat': 31.2397, 'lng': 121.4999, 'type': 'landmark', 'city': '上海'},
            '东方明珠': {'lat': 31.2397, 'lng': 121.4999, 'type': 'landmark', 'city': '上海'},
            '豫园': {'lat': 31.2277, 'lng': 121.4917, 'type': 'landmark', 'city': '上海'},
            '南京路': {'lat': 31.2359, 'lng': 121.4737, 'type': 'commercial', 'city': '上海'},
            '西湖': {'lat': 30.2741, 'lng': 120.1551, 'type': 'landmark', 'city': '杭州'},
            '雷峰塔': {'lat': 30.2319, 'lng': 120.1303, 'type': 'landmark', 'city': '杭州'},
            '断桥': {'lat': 30.2594, 'lng': 120.1551, 'type': 'landmark', 'city': '杭州'},
            '中山陵': {'lat': 32.0581, 'lng': 118.8486, 'type': 'landmark', 'city': '南京'},
            '夫子庙': {'lat': 32.0208, 'lng': 118.7867, 'type': 'landmark', 'city': '南京'},
            '大雁塔': {'lat': 34.2186, 'lng': 108.9647, 'type': 'landmark', 'city': '西安'},
            '兵马俑': {'lat': 34.3847, 'lng': 109.2731, 'type': 'landmark', 'city': '西安'},
            '钟楼': {'lat': 34.2586, 'lng': 108.9425, 'type': 'landmark', 'city': '西安'},
            '宽窄巷子': {'lat': 30.6708, 'lng': 104.0631, 'type': 'landmark', 'city': '成都'},
            '武侯祠': {'lat': 30.6458, 'lng': 104.0481, 'type': 'landmark', 'city': '成都'},
            '锦里': {'lat': 30.6458, 'lng': 104.0481, 'type': 'commercial', 'city': '成都'},
            '黄鹤楼': {'lat': 30.5458, 'lng': 114.2981, 'type': 'landmark', 'city': '武汉'},
            '户部巷': {'lat': 30.5458, 'lng': 114.2981, 'type': 'commercial', 'city': '武汉'},
            '解放碑': {'lat': 29.5581, 'lng': 106.5747, 'type': 'landmark', 'city': '重庆'},
            '洪崖洞': {'lat': 29.5581, 'lng': 106.5747, 'type': 'landmark', 'city': '重庆'},
            '小蛮腰': {'lat': 23.1097, 'lng': 113.3245, 'type': 'landmark', 'city': '广州'},
            '珠江': {'lat': 23.1097, 'lng': 113.3245, 'type': 'landmark', 'city': '广州'},
            '深圳湾': {'lat': 22.4958, 'lng': 113.9347, 'type': 'landmark', 'city': '深圳'},
            '世界之窗': {'lat': 22.5408, 'lng': 113.9747, 'type': 'landmark', 'city': '深圳'}
        }
    
    def parse_osm_data(self, osm_file):
        """解析OSM数据文件"""
        try:
            tree = ET.parse(osm_file)
            root = tree.getroot()
            
            # 解析节点
            nodes = {}
            for node in root.findall('node'):
                node_id = node.get('id')
                lat = float(node.get('lat'))
                lon = float(node.get('lon'))
                nodes[node_id] = {'lat': lat, 'lon': lon}
            
            # 解析道路
            roads = []
            for way in root.findall('way'):
                way_id = way.get('id')
                tags = {}
                for tag in way.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                # 检查是否是道路
                if 'highway' in tags:
                    nd_refs = [nd.get('ref') for nd in way.findall('nd')]
                    road_coords = []
                    
                    for ref in nd_refs:
                        if ref in nodes:
                            coord = nodes[ref]
                            road_coords.append([coord['lon'], coord['lat']])
                    
                    if road_coords:
                        roads.append({
                            'id': way_id,
                            'type': tags.get('highway', 'unknown'),
                            'name': tags.get('name', ''),
                            'coordinates': road_coords
                        })
            
            # 解析建筑物
            buildings = []
            for way in root.findall('way'):
                way_id = way.get('id')
                tags = {}
                for tag in way.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                # 检查是否是建筑物
                if 'building' in tags or 'amenity' in tags or 'shop' in tags or 'office' in tags or 'leisure' in tags:
                    nd_refs = [nd.get('ref') for nd in way.findall('nd')]
                    building_coords = []
                    
                    for ref in nd_refs:
                        if ref in nodes:
                            coord = nodes[ref]
                            building_coords.append([coord['lon'], coord['lat']])
                    
                    if building_coords:
                        buildings.append({
                            'id': way_id,
                            'type': tags.get('building', tags.get('amenity', tags.get('shop', tags.get('office', tags.get('leisure', 'unknown'))))),
                            'name': tags.get('name', ''),
                            'coordinates': building_coords
                        })
            
            # 解析POI点
            pois = []
            for node in root.findall('node'):
                node_id = node.get('id')
                lat = float(node.get('lat'))
                lon = float(node.get('lon'))
                tags = {}
                for tag in node.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                # 检查是否是POI
                if 'amenity' in tags or 'shop' in tags or 'tourism' in tags or 'leisure' in tags or 'office' in tags:
                    pois.append({
                        'id': node_id,
                        'type': tags.get('amenity', tags.get('shop', tags.get('tourism', tags.get('leisure', tags.get('office', 'unknown'))))),
                        'name': tags.get('name', ''),
                        'lat': lat,
                        'lon': lon
                    })
            
            # 解析交通设施
            transport = []
            for way in root.findall('way'):
                way_id = way.get('id')
                tags = {}
                for tag in way.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                # 检查是否是交通设施
                if 'railway' in tags or 'aeroway' in tags:
                    nd_refs = [nd.get('ref') for nd in way.findall('nd')]
                    transport_coords = []
                    
                    for ref in nd_refs:
                        if ref in nodes:
                            coord = nodes[ref]
                            transport_coords.append([coord['lon'], coord['lat']])
                    
                    if transport_coords:
                        transport.append({
                            'id': way_id,
                            'type': tags.get('railway', tags.get('aeroway', 'unknown')),
                            'name': tags.get('name', ''),
                            'coordinates': transport_coords
                        })
            
            # 解析交通节点
            for node in root.findall('node'):
                node_id = node.get('id')
                lat = float(node.get('lat'))
                lon = float(node.get('lon'))
                tags = {}
                for tag in node.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                # 检查是否是交通节点
                if 'public_transport' in tags or 'railway' in tags or 'highway' in tags or 'aeroway' in tags:
                    if tags.get('highway') in ['bus_stop', 'tram_stop'] or tags.get('railway') in ['station', 'halt', 'tram_stop', 'subway_entrance', 'platform'] or tags.get('aeroway') in ['aerodrome', 'helipad', 'heliport'] or tags.get('public_transport') in ['station', 'stop_position', 'platform', 'stop_area']:
                        transport.append({
                            'id': node_id,
                            'type': tags.get('public_transport', tags.get('railway', tags.get('highway', tags.get('aeroway', 'unknown')))),
                            'name': tags.get('name', ''),
                            'lat': lat,
                            'lon': lon
                        })
            
            return {
                'roads': roads,
                'buildings': buildings,
                'pois': pois,
                'transport': transport,
                'nodes': nodes
            }
            
        except Exception as e:
            print(f"解析OSM数据失败: {e}")
            return {'roads': [], 'buildings': [], 'pois': [], 'transport': [], 'nodes': {}}
    
    def get_available_regions(self):
        """获取可用的区域数据"""
        regions = []
        
        if self.vector_data_dir.exists():
            for region_dir in self.vector_data_dir.iterdir():
                if region_dir.is_dir():
                    region_name = region_dir.name
                    files = list(region_dir.glob("*.xml"))
                    if files:
                        regions.append({
                            'name': region_name,
                            'files': [f.name for f in files]
                        })
        
        return regions
    
    def search_poi(self, query, region_name=None):
        """搜索兴趣点"""
        results = []
        
        # 首先搜索著名地标数据库
        query_lower = query.lower()
        for place_name, place_info in self.famous_places.items():
            if query_lower in place_name.lower() or place_name.lower() in query_lower:
                results.append({
                    'name': place_name,
                    'type': f"著名{place_info['type']}",
                    'lat': place_info['lat'],
                    'lon': place_info['lng'],
                    'region': place_info['city'],
                    'priority': 1  # 高优先级
                })
        
        # 如果找到著名地标，直接返回
        if results:
            return results[:10]  # 返回前10个结果
        
        # 确定搜索范围
        if region_name:
            search_dirs = [self.vector_data_dir / region_name]
        else:
            search_dirs = [d for d in self.vector_data_dir.iterdir() if d.is_dir()]
        
        for search_dir in search_dirs:
            if not search_dir.exists():
                continue
            
            # 搜索POI文件
            poi_files = list(search_dir.glob("*_poi.xml"))
            for poi_file in poi_files:
                data = self.parse_osm_data(poi_file)
                
                for poi in data['pois']:
                    if query_lower in poi['name'].lower():
                        results.append({
                            'name': poi['name'],
                            'type': poi['type'],
                            'lat': poi['lat'],
                            'lon': poi['lon'],
                            'region': search_dir.name,
                            'priority': 2  # 中等优先级
                        })
            
            # 搜索建筑物文件
            building_files = list(search_dir.glob("*_buildings.xml"))
            for building_file in building_files:
                data = self.parse_osm_data(building_file)
                
                for building in data['buildings']:
                    if query_lower in building['name'].lower():
                        # 计算建筑物中心点
                        coords = building['coordinates']
                        if coords:
                            center_lon = sum(c[0] for c in coords) / len(coords)
                            center_lat = sum(c[1] for c in coords) / len(coords)
                            
                            results.append({
                                'name': building['name'],
                                'type': building['type'],
                                'lat': center_lat,
                                'lon': center_lon,
                                'region': search_dir.name,
                                'priority': 3  # 低优先级
                            })
        
        # 按优先级排序
        results.sort(key=lambda x: x.get('priority', 4))
        return results[:20]  # 返回前20个结果
    
    def get_nearby_roads(self, lat, lon, radius=0.01):
        """获取附近的道路"""
        nearby_roads = []
        
        for region_dir in self.vector_data_dir.iterdir():
            if not region_dir.is_dir():
                continue
                
            road_files = list(region_dir.glob("*_roads.xml"))
            for road_file in road_files:
                data = self.parse_osm_data(road_file)
                
                for road in data['roads']:
                    for coord in road['coordinates']:
                        road_lon, road_lat = coord
                        
                        # 计算距离
                        distance = math.sqrt((lat - road_lat)**2 + (lon - road_lon)**2)
                        
                        if distance <= radius:
                            nearby_roads.append({
                                'name': road['name'],
                                'type': road['type'],
                                'distance': distance,
                                'coordinates': road['coordinates']
                            })
                            break
        
        return nearby_roads

# 创建查看器实例
viewer = VectorMapViewer()

@app.route('/')
def index():
    """主页面"""
    regions = viewer.get_available_regions()
    return render_template('vector_map.html', regions=regions, map_config=viewer.map_config)

@app.route('/api/regions')
def api_regions():
    """获取可用区域API"""
    regions = viewer.get_available_regions()
    return jsonify(regions)

@app.route('/api/vector-data/<region_name>')
def api_vector_data(region_name):
    """获取矢量数据API"""
    region_dir = viewer.vector_data_dir / region_name
    
    if not region_dir.exists():
        return jsonify({'error': '区域不存在'}), 404
    
    # 加载道路数据
    roads_data = {'roads': [], 'buildings': [], 'nodes': {}}
    road_files = list(region_dir.glob("*_roads.xml"))
    if road_files:
        roads_data = viewer.parse_osm_data(road_files[0])
    
    # 加载建筑物数据
    buildings_data = {'buildings': []}
    building_files = list(region_dir.glob("*_buildings.xml"))
    if building_files:
        buildings_data = viewer.parse_osm_data(building_files[0])
    
    # 合并数据
    combined_data = {
        'roads': roads_data['roads'],
        'buildings': buildings_data['buildings']
    }
    
    return jsonify(combined_data)

@app.route('/api/search')
def api_search():
    """搜索API"""
    query = request.args.get('q', '')
    region = request.args.get('region', '')
    
    if not query:
        return jsonify([])
    
    results = viewer.search_poi(query, region)
    return jsonify(results)

@app.route('/api/nearby-roads')
def api_nearby_roads():
    """获取附近道路API"""
    lat = float(request.args.get('lat', 0))
    lon = float(request.args.get('lon', 0))
    radius = float(request.args.get('radius', 0.01))
    
    roads = viewer.get_nearby_roads(lat, lon, radius)
    return jsonify(roads)

@app.route('/api/famous-places')
def api_famous_places():
    """获取著名地标列表API"""
    city = request.args.get('city', '')
    
    if city:
        places = {name: info for name, info in viewer.famous_places.items() if info['city'] == city}
    else:
        places = viewer.famous_places
    
    return jsonify(places)

@app.route('/api/quick-locate/<place_name>')
def api_quick_locate(place_name):
    """快速定位著名地标API"""
    if place_name in viewer.famous_places:
        place_info = viewer.famous_places[place_name]
        return jsonify({
            'success': True,
            'name': place_name,
            'lat': place_info['lat'],
            'lon': place_info['lng'],
            'type': place_info['type'],
            'city': place_info['city']
        })
    else:
        return jsonify({'success': False, 'error': '地标不存在'}), 404

if __name__ == '__main__':
    print("🗺️  矢量地图查看器启动中...")
    print("   访问地址: http://localhost:5000")
    print("   功能: 矢量数据显示、定位、搜索")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
