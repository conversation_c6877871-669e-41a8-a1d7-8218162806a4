#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瓦片修复效果
验证不同缩放级别的瓦片生成是否正常
"""

import os
import sys
from pathlib import Path
from enhanced_osm_processor import RealTileGenerator

def test_tile_generation():
    """测试瓦片生成功能"""
    print("🧪 测试瓦片修复效果")
    print("=" * 50)
    
    # 创建瓦片生成器实例
    generator = RealTileGenerator(
        province="北京",
        min_zoom=10,
        max_zoom=12
    )
    
    print(f"📍 测试区域: {generator.province}")
    print(f"🔍 缩放级别: {generator.min_zoom}-{generator.max_zoom}")
    print(f"🗂️  OSM文件: {generator.osm_file}")
    print(f"📁 瓦片目录: {generator.tiles_dir}")
    
    # 检查OSM文件是否存在
    if not generator.osm_file.exists():
        print(f"❌ OSM文件不存在: {generator.osm_file}")
        return False
    
    print(f"✅ OSM文件存在，大小: {generator.osm_file.stat().st_size / 1024 / 1024:.1f} MB")
    
    # 测试特定瓦片生成
    test_tiles = [
        (10, 840, 385),  # 第10级测试瓦片
        (11, 1671, 1681),  # 第11级测试瓦片  
        (12, 3363, 1525),  # 第12级测试瓦片
    ]
    
    print(f"\n🔬 测试特定瓦片生成...")
    
    for zoom, x, y in test_tiles:
        print(f"\n测试瓦片 {zoom}/{x}/{y}:")
        
        # 生成瓦片
        try:
            img, has_data = generator.generate_test_tile(x, y, zoom)
            
            # 检查瓦片质量
            if has_data:
                print(f"  ✅ 瓦片包含数据")
            else:
                print(f"  ⚠️  瓦片无数据（可能是空白区域）")
            
            # 保存测试瓦片
            test_dir = generator.tiles_dir / "test"
            test_dir.mkdir(parents=True, exist_ok=True)
            test_file = test_dir / f"test_{zoom}_{x}_{y}.png"
            img.save(test_file, 'PNG')
            
            file_size = test_file.stat().st_size
            print(f"  📊 文件大小: {file_size} 字节")
            
            if file_size < 1000:
                print(f"  ❌ 文件过小，可能是空白瓦片")
            elif file_size > 50000:
                print(f"  ⚠️  文件较大，可能包含过多细节")
            else:
                print(f"  ✅ 文件大小正常")
                
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
    
    print(f"\n🎯 测试完成！")
    print(f"📁 测试瓦片保存在: {generator.tiles_dir / 'test'}")
    
    return True

def main():
    """主函数"""
    try:
        success = test_tile_generation()
        if success:
            print(f"\n✅ 瓦片修复测试完成")
            print(f"💡 建议运行完整瓦片生成:")
            print(f"   python enhanced_osm_processor.py 北京 --min-zoom 10 --max-zoom 12")
        else:
            print(f"\n❌ 瓦片修复测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False
    
    return success

if __name__ == "__main__":
    main()
