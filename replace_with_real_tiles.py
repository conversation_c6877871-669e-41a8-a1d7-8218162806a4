#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替换现有瓦片为真实OSM瓦片
专门针对北京地区的核心区域进行高质量瓦片替换
"""

import os
import sys
import math
import json
import time
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class TileReplacer:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        
        # 北京核心区域 - 缩小范围确保高质量
        self.center_lat = 39.9042
        self.center_lon = 116.4074
        self.radius_km = 25  # 缩小到25公里确保质量
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {"replaced": 0, "failed": 0, "total": 0}
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算距离"""
        R = 6371
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def is_low_quality_tile(self, tile_path):
        """检查是否为低质量瓦片"""
        if not tile_path.exists():
            return True
        
        # 文件大小小于5KB的认为是低质量瓦片
        return tile_path.stat().st_size < 5000
    
    def download_real_tile(self, z, x, y):
        """下载真实瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_file = tile_dir / f"{y}.png"
        
        # 检查是否需要替换
        if not self.is_low_quality_tile(tile_file):
            return True
        
        # 检查是否在核心区域内
        tile_lat, tile_lon = self.num2deg(x, y, z)
        distance = self.calculate_distance(self.center_lat, self.center_lon, tile_lat, tile_lon)
        if distance > self.radius_km:
            return False
        
        # 多个瓦片源
        sources = [
            "https://tile.openstreetmap.org",
            "https://a.tile.openstreetmap.org",
            "https://b.tile.openstreetmap.org",
            "https://c.tile.openstreetmap.org"
        ]
        
        for source in sources:
            try:
                url = f"{source}/{z}/{x}/{y}.png"
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200 and len(response.content) > 2000:
                    tile_dir.mkdir(parents=True, exist_ok=True)
                    with open(tile_file, 'wb') as f:
                        f.write(response.content)
                    return True
                
                time.sleep(0.3)  # 请求间隔
                
            except Exception:
                continue
        
        return False
    
    def replace_tiles_for_zoom(self, zoom):
        """替换指定缩放级别的瓦片"""
        print(f"\n🔄 替换缩放级别 {zoom} 的低质量瓦片...")
        
        # 获取现有瓦片
        zoom_dir = self.tiles_dir / str(zoom)
        if not zoom_dir.exists():
            print(f"   ⚠️  级别 {zoom} 目录不存在")
            return 0
        
        # 收集需要替换的瓦片
        tiles_to_replace = []
        for x_dir in zoom_dir.iterdir():
            if not x_dir.is_dir() or not x_dir.name.isdigit():
                continue
            
            x = int(x_dir.name)
            for tile_file in x_dir.glob("*.png"):
                y = int(tile_file.stem)
                
                # 检查是否为低质量瓦片且在范围内
                if self.is_low_quality_tile(tile_file):
                    tile_lat, tile_lon = self.num2deg(x, y, zoom)
                    distance = self.calculate_distance(self.center_lat, self.center_lon, tile_lat, tile_lon)
                    if distance <= self.radius_km:
                        tiles_to_replace.append((zoom, x, y))
        
        if not tiles_to_replace:
            print(f"   ✅ 级别 {zoom} 无需替换")
            return 0
        
        print(f"   需要替换: {len(tiles_to_replace)} 个瓦片")
        
        # 多线程替换
        success_count = 0
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_tile = {
                executor.submit(self.download_real_tile, z, x, y): (z, x, y) 
                for z, x, y in tiles_to_replace
            }
            
            for i, future in enumerate(as_completed(future_to_tile)):
                z, x, y = future_to_tile[future]
                try:
                    if future.result():
                        success_count += 1
                        self.stats["replaced"] += 1
                    else:
                        self.stats["failed"] += 1
                except Exception:
                    self.stats["failed"] += 1
                
                self.stats["total"] += 1
                
                # 显示进度
                if (i + 1) % 10 == 0 or (i + 1) == len(tiles_to_replace):
                    progress = (i + 1) / len(tiles_to_replace) * 100
                    print(f"   进度: {i+1}/{len(tiles_to_replace)} ({progress:.1f}%) - 成功: {success_count}")
        
        print(f"   ✅ 级别 {zoom} 完成: {success_count}/{len(tiles_to_replace)} 个瓦片被替换")
        return success_count
    
    def replace_low_quality_tiles(self):
        """替换所有低质量瓦片"""
        print("🔄 瓦片质量提升工具")
        print("=" * 50)
        print(f"📍 目标区域: 北京市中心 {self.radius_km} 公里")
        print(f"🎯 任务: 替换低质量瓦片为真实OSM瓦片")
        print(f"📁 瓦片目录: {self.tiles_dir}")
        
        start_time = time.time()
        total_replaced = 0
        
        # 重点替换关键级别
        key_levels = [10, 11, 12, 13, 14, 15, 16, 17, 18]
        
        for zoom in key_levels:
            replaced = self.replace_tiles_for_zoom(zoom)
            total_replaced += replaced
            
            # 每个级别后稍作休息
            if zoom < max(key_levels):
                time.sleep(1)
        
        elapsed_time = time.time() - start_time
        
        print(f"\n" + "=" * 50)
        print(f"🎉 瓦片替换完成!")
        print(f"📊 统计信息:")
        print(f"   成功替换: {self.stats['replaced']:,} 个瓦片")
        print(f"   替换失败: {self.stats['failed']:,} 个瓦片")
        print(f"   总计处理: {self.stats['total']:,} 个瓦片")
        if self.stats['total'] > 0:
            print(f"   成功率: {self.stats['replaced']/self.stats['total']*100:.1f}%")
        print(f"   用时: {elapsed_time:.1f} 秒")
        
        return total_replaced > 0

def main():
    replacer = TileReplacer()
    
    try:
        print("⚠️  注意: 这将替换低质量瓦片为真实OSM瓦片")
        print("   - 只替换北京核心25公里范围")
        print("   - 只替换小于5KB的低质量瓦片")
        print("   - 保留已有的高质量瓦片")
        
        confirm = input("\n确认开始替换? (y/N): ").lower()
        if confirm != 'y':
            print("已取消替换")
            return
        
        success = replacer.replace_low_quality_tiles()
        
        if success:
            print(f"\n🚀 下一步:")
            print(f"   1. 地图服务已在运行，直接刷新页面")
            print(f"   2. 访问: http://localhost:5000")
            print(f"   3. 现在应该看到更高质量的地图!")
        else:
            print(f"\n❌ 替换失败")
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断")
    except Exception as e:
        print(f"❌ 替换过程出错: {e}")

if __name__ == "__main__":
    main()