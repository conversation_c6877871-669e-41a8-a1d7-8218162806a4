<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS机井监控地图系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        
        .header .subtitle {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .control-panel {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .well-list {
            padding: 20px;
        }
        
        .well-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .well-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .well-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .well-name {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }
        
        .well-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .well-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .gas-level {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .gas-bar {
            flex: 1;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .gas-fill {
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .gas-low { background: #28a745; }
        .gas-medium { background: #ffc107; }
        .gas-high { background: #dc3545; }
        
        .coordinates {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
        }
        
        .statistics {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-value {
            font-weight: 600;
            color: #333;
        }
        
        .gps-input {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .gps-input input {
            flex: 1;
        }
        
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .search-result {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .search-result:hover {
            background: #f8f9fa;
        }
        
        .search-result:last-child {
            border-bottom: none;
        }
        
        .result-name {
            font-weight: 500;
            color: #333;
        }
        
        .result-type {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .high-precision-marker {
            background: #ff4444;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 10px rgba(255,68,68,0.8);
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 GPS机井监控地图系统</h1>
        <div class="subtitle">野外机井作业实时监控 | GPS定位 | 气体检测 | 送风机控制</div>
    </div>
    
    <div class="container">
        <div class="sidebar">
            <!-- GPS定位控制面板 -->
            <div class="control-panel">
                <h3>🎯 GPS定位</h3>
                <div class="form-group">
                    <label>纬度 (Latitude)</label>
                    <input type="number" id="gps-lat" step="0.000001" placeholder="例如: 39.9042">
                </div>
                <div class="form-group">
                    <label>经度 (Longitude)</label>
                    <input type="number" id="gps-lon" step="0.000001" placeholder="例如: 116.4074">
                </div>
                <div class="form-group">
                    <label>搜索半径 (米)</label>
                    <input type="number" id="search-radius" value="1000" min="100" max="10000">
                </div>
                <div class="form-group">
                    <label>定位精度级别</label>
                    <select id="precision-level">
                        <option value="15">15级 - 城市级别</option>
                        <option value="16">16级 - 区域级别</option>
                        <option value="17">17级 - 街道级别</option>
                        <option value="18" selected>18级 - 高精度 (推荐)</option>
                        <option value="19">19级 - 超高精度 (矢量数据)</option>
                        <option value="20">20级 - 最高精度 (矢量数据)</option>
                    </select>
                    <small style="color: #666; font-size: 12px;">
                        19级使用高精度矢量数据，20级使用超高精度矢量数据(±20米)
                    </small>
                </div>
                <button class="btn" onclick="locateByGPS()">📍 定位到GPS坐标</button>
                <button class="btn btn-secondary" onclick="getCurrentLocation()">🌍 获取当前位置</button>
                <button class="btn btn-warning" onclick="setMaxPrecision()">🎯 最高精度定位</button>
            </div>
            
            <!-- 搜索面板 -->
            <div class="control-panel">
                <h3>🔍 搜索机井</h3>
                <div class="form-group">
                    <input type="text" id="search-input" placeholder="输入机井名称或ID" onkeyup="searchWells()">
                </div>
                <div id="search-results" class="search-results" style="display: none;"></div>
            </div>
            
            <!-- 机井管理 -->
            <div class="control-panel">
                <h3>⚙️ 机井管理</h3>
                <div class="form-group">
                    <label>机井ID</label>
                    <input type="text" id="well-id" placeholder="例如: WELL001">
                </div>
                <div class="form-group">
                    <label>机井名称</label>
                    <input type="text" id="well-name" placeholder="例如: 机井001">
                </div>
                <div class="gps-input">
                    <input type="number" id="well-lat" step="0.000001" placeholder="纬度">
                    <input type="number" id="well-lon" step="0.000001" placeholder="经度">
                </div>
                <button class="btn btn-success" onclick="addWell()">➕ 添加机井</button>
                <button class="btn btn-warning" onclick="updateWellGPS()">🔄 更新GPS</button>
            </div>
            
            <!-- 统计信息 -->
            <div class="statistics">
                <h3>📊 统计信息</h3>
                <div id="statistics-content">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
    </div>

    <script>
        // 地图初始化
        let map;
        let wellMarkers = {};
        let currentLocationMarker = null;
        let mapConfig = null;
        
        async function initMap() {
            // 获取地图配置
            try {
                const response = await fetch('/api/map-config');
                mapConfig = await response.json();
            } catch (error) {
                console.error('获取地图配置失败:', error);
                // 使用默认配置
                mapConfig = {
                    offline_mode: false,  // 使用在线地图作为底图
                    vector_data_available: true,  // 矢量数据可用
                    vector_data_path: '/static/vector_data',
                    min_zoom: 3,  // 最小缩放级别
                    max_zoom: 18,  // 在线地图最大可用精度
                    vector_max_zoom: 20,  // 矢量数据支持的最高精度
                    initial_zoom: 10,  // 初始缩放级别 - 中国国家地图
                    gps_zoom: 18,  // GPS定位时的缩放级别
                    center: [35.0, 105.0],  // 中国中心坐标 [纬度, 经度]
                    bounds: { north: 54.0, south: 18.0, east: 135.0, west: 73.0 }  // 中国边界
                };
            }
            
            // 初始化地图
            map = L.map('map', {
                center: mapConfig.center,
                zoom: mapConfig.initial_zoom || 10, // 使用配置的初始缩放级别
                minZoom: mapConfig.min_zoom,
                maxZoom: mapConfig.max_zoom,
                maxBounds: [
                    [mapConfig.bounds.south, mapConfig.bounds.west],
                    [mapConfig.bounds.north, mapConfig.bounds.east]
                ]
            });
            
            // 添加地图图层
            const baseLayers = {};
            
            if (mapConfig.offline_mode) {
                // 离线模式 - 使用本地瓦片
                baseLayers["离线地图"] = L.tileLayer(mapConfig.tile_url_template, {
                    attribution: '© 本地离线地图',
                    minZoom: mapConfig.min_zoom,
                    maxZoom: mapConfig.max_zoom,
                    bounds: [
                        [mapConfig.bounds.south, mapConfig.bounds.west],
                        [mapConfig.bounds.north, mapConfig.bounds.east]
                    ]
                });
            } else {
                // 在线模式 - 使用在线地图
                baseLayers["OpenStreetMap"] = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    minZoom: mapConfig.min_zoom,
                    maxZoom: mapConfig.max_zoom
                });
                
                baseLayers["高德地图"] = L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                    attribution: '© 高德地图',
                    subdomains: ['1', '2', '3', '4'],
                    minZoom: mapConfig.min_zoom,
                    maxZoom: mapConfig.max_zoom
                });
                
                baseLayers["百度地图"] = L.tileLayer('https://maponline{s}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20200101', {
                    attribution: '© 百度地图',
                    subdomains: ['0', '1', '2', '3'],
                    minZoom: mapConfig.min_zoom,
                    maxZoom: mapConfig.max_zoom
                });
            }
            
            // 添加默认图层
            const defaultLayer = Object.keys(baseLayers)[0];
            baseLayers[defaultLayer].addTo(map);
            
            // 添加图层控制
            if (Object.keys(baseLayers).length > 1) {
                L.control.layers(baseLayers).addTo(map);
            }
            
            // 矢量数据将在用户GPS定位后按需加载
            // 初始显示中国国家地图，不加载特定区域数据
            
            // 加载机井数据
            loadWells();
            
            // 加载统计信息
            loadStatistics();
        }
        
        // 加载矢量数据
        async function loadVectorData(region) {
            try {
                const response = await fetch(`/api/vector-data/${region}`);
                if (response.ok) {
                    const data = await response.json();
                    console.log(`加载 ${region} 矢量数据成功`);
                    
                    // 这里可以解析和显示矢量数据
                    // 由于矢量数据是XML格式，需要解析后转换为Leaflet图层
                    if (data.roads) {
                        console.log('道路数据:', data.roads.length, '字符');
                    }
                    if (data.buildings) {
                        console.log('建筑数据:', data.buildings.length, '字符');
                    }
                    if (data.pois) {
                        console.log('POI数据:', data.pois.length, '字符');
                    }
                } else {
                    console.error(`加载 ${region} 矢量数据失败:`, response.status);
                }
            } catch (error) {
                console.error('矢量数据加载错误:', error);
            }
        }
        
        // 加载高精度矢量数据
        async function loadHighPrecisionData(region) {
            try {
                const response = await fetch(`/api/high-precision-data/${region}`);
                if (response.ok) {
                    const data = await response.json();
                    console.log(`高精度数据加载成功: ${region}`, data);
                    
                    // 渲染高精度数据
                    renderHighPrecisionData(data);
                } else {
                    console.error(`高精度数据加载失败: ${response.status}`);
                }
            } catch (error) {
                console.error('高精度数据加载错误:', error);
            }
        }
        
        // 加载超高精度矢量数据
        async function loadUltraPrecisionData(region) {
            try {
                const response = await fetch(`/api/ultra-precision-data/${region}`);
                if (response.ok) {
                    const data = await response.json();
                    console.log(`超高精度数据加载成功: ${region}`, data);
                    
                    // 渲染超高精度数据
                    renderUltraPrecisionData(data);
                } else {
                    console.error(`超高精度数据加载失败: ${response.status}`);
                }
            } catch (error) {
                console.error('超高精度数据加载错误:', error);
            }
        }
        
        // 渲染高精度矢量数据
        function renderHighPrecisionData(data) {
            if (!data || !map) return;
            
            // 清除之前的矢量图层
            if (window.vectorLayers) {
                window.vectorLayers.forEach(layer => {
                    map.removeLayer(layer);
                });
                window.vectorLayers = [];
            } else {
                window.vectorLayers = [];
            }
            
            // 渲染道路数据
            if (data.roads && data.roads.features) {
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#666';
                        let weight = 2;
                        
                        switch(highway) {
                            case 'motorway':
                            case 'trunk':
                                color = '#ff0000';
                                weight = 4;
                                break;
                            case 'primary':
                                color = '#ff8800';
                                weight = 3;
                                break;
                            case 'secondary':
                                color = '#ffff00';
                                weight = 3;
                                break;
                            case 'tertiary':
                                color = '#00ff00';
                                weight = 2;
                                break;
                            case 'residential':
                                color = '#ffffff';
                                weight = 1;
                                break;
                            default:
                                color = '#cccccc';
                                weight = 1;
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 0.8
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway}<br>
                                车道数: ${props.lanes || '未知'}<br>
                                限速: ${props.maxspeed || '未知'}<br>
                                单向: ${props.oneway === 'yes' ? '是' : '否'}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(roadsLayer);
            }
            
            // 渲染建筑数据
            if (data.buildings && data.buildings.features) {
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: function(feature) {
                        return {
                            color: '#8B4513',
                            weight: 1,
                            fillColor: '#DEB887',
                            fillOpacity: 0.6
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building}<br>
                                高度: ${props.height || '未知'}<br>
                                层数: ${props.levels || '未知'}<br>
                                用途: ${props.use || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(buildingsLayer);
            }
            
            // 渲染POI数据
            if (data.pois && data.pois.features) {
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 4,
                            fillColor: '#ff0000',
                            color: '#ffffff',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.type}<br>
                                分类: ${props.category}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(poisLayer);
            }
            
            console.log(`高精度矢量数据渲染完成: ${data.metadata.total_features}个要素`);
        }
        
        // 渲染超高精度矢量数据
        function renderUltraPrecisionData(data) {
            if (!data || !map) return;
            
            // 清除之前的矢量图层
            if (window.vectorLayers) {
                window.vectorLayers.forEach(layer => {
                    map.removeLayer(layer);
                });
                window.vectorLayers = [];
            } else {
                window.vectorLayers = [];
            }
            
            // 渲染道路数据
            if (data.roads && data.roads.features) {
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#666';
                        let weight = 2;
                        
                        switch(highway) {
                            case 'primary':
                                color = '#ff0000';
                                weight = 12; // 增加线宽
                                break;
                            case 'secondary':
                                color = '#ff8800';
                                weight = 8; // 增加线宽
                                break;
                            case 'residential':
                                color = '#00ff00';
                                weight = 6; // 增加线宽
                                break;
                            default:
                                color = '#0000ff';
                                weight = 6; // 增加线宽
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 1.0 // 增加不透明度
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway}<br>
                                车道数: ${props.lanes || '未知'}<br>
                                限速: ${props.maxspeed || '未知'}<br>
                                单向: ${props.oneway === 'yes' ? '是' : '否'}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(roadsLayer);
            }
            
            // 渲染建筑数据
            if (data.buildings && data.buildings.features) {
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: function(feature) {
                        return {
                            color: '#ff0000',
                            weight: 6, // 增加边框宽度
                            fillColor: '#ffff00',
                            fillOpacity: 1.0 // 增加填充不透明度
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building}<br>
                                高度: ${props.height || '未知'}米<br>
                                层数: ${props.levels || '未知'}<br>
                                用途: ${props.use || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(buildingsLayer);
            }
            
            // 渲染POI数据
            if (data.pois && data.pois.features) {
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 15, // 增加半径
                            fillColor: '#00ff00',
                            color: '#000000',
                            weight: 4, // 增加边框宽度
                            opacity: 1,
                            fillOpacity: 1.0 // 增加填充不透明度
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.type}<br>
                                分类: ${props.category}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(poisLayer);
            }
            
            // 渲染机井监控点
            if (data.wells && data.wells.features) {
                const wellsLayer = L.geoJSON(data.wells, {
                    pointToLayer: function(feature, latlng) {
                        const status = feature.properties.status;
                        let color = '#00ff00';
                        if (status === 'warning') color = '#ffaa00';
                        if (status === 'maintenance') color = '#ff0000';
                        
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: color,
                            color: '#ffffff',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 11px;">
                                <strong>${props.name || '未命名机井'}</strong><br>
                                状态: ${props.status}<br>
                                气体浓度: ${props.gas_level}%<br>
                                送风机: ${props.fan_status === 'on' ? '开启' : '关闭'}<br>
                                更新时间: ${props.last_update}
                            </div>
                        `);
                    }
                }).addTo(map);
                window.vectorLayers.push(wellsLayer);
            }
            
                console.log(`🎯 超高精度矢量数据渲染完成: ${data.metadata.total_features}个要素`);
                console.log(`🎯 道路要素: ${data.roads ? data.roads.features.length : 0}个`);
                console.log(`🎯 建筑要素: ${data.buildings ? data.buildings.features.length : 0}个`);
                console.log(`🎯 POI要素: ${data.pois ? data.pois.features.length : 0}个`);
                console.log(`🎯 机井要素: ${data.wells ? data.wells.features.length : 0}个`);
                console.log(`精度: ±${data.metadata.accuracy_meters}米`);
                console.log(`地图缩放级别: ${map.getZoom()}`);
                console.log(`地图中心: ${map.getCenter()}`);
                console.log(`矢量图层数量: ${window.vectorLayers.length}`);
        }
        
        // 加载机井数据
        async function loadWells() {
            try {
                const response = await fetch('/api/wells');
                const wells = await response.json();
                
                // 清除现有标记
                Object.values(wellMarkers).forEach(marker => map.removeLayer(marker));
                wellMarkers = {};
                
                // 添加机井标记
                Object.values(wells).forEach(well => {
                    addWellMarker(well);
                });
                
                updateWellList(wells);
            } catch (error) {
                console.error('加载机井数据失败:', error);
            }
        }
        
        // 添加机井标记
        function addWellMarker(well) {
            const status = well.status || 'active';
            const gasLevel = well.gas_level || 0;
            
            // 根据状态选择图标颜色
            let iconColor = '#28a745'; // 绿色 - 正常
            if (status === 'warning' || gasLevel > 50) {
                iconColor = '#ffc107'; // 黄色 - 警告
            } else if (status === 'error' || gasLevel > 80) {
                iconColor = '#dc3545'; // 红色 - 危险
            }
            
            const icon = L.divIcon({
                className: 'well-marker',
                html: `<div style="
                    width: 20px;
                    height: 20px;
                    background: ${iconColor};
                    border: 2px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                "></div>`,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });
            
            const marker = L.marker([well.lat, well.lon], { icon })
                .addTo(map)
                .bindPopup(`
                    <div style="min-width: 200px;">
                        <h4 style="margin: 0 0 10px 0; color: #333;">${well.name}</h4>
                        <p style="margin: 5px 0;"><strong>ID:</strong> ${well.id}</p>
                        <p style="margin: 5px 0;"><strong>状态:</strong> 
                            <span style="color: ${iconColor};">${getStatusText(status)}</span>
                        </p>
                        <p style="margin: 5px 0;"><strong>气体浓度:</strong> ${gasLevel}%</p>
                        <p style="margin: 5px 0;"><strong>送风机:</strong> ${well.fan_status === 'on' ? '开启' : '关闭'}</p>
                        <p style="margin: 5px 0;"><strong>坐标:</strong> ${well.lat.toFixed(6)}, ${well.lon.toFixed(6)}</p>
                        <p style="margin: 5px 0;"><strong>更新时间:</strong> ${new Date(well.last_update).toLocaleString()}</p>
                        ${well.description ? `<p style="margin: 5px 0;"><strong>描述:</strong> ${well.description}</p>` : ''}
                        <button onclick="flyToWell('${well.id}')" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 5px 10px;
                            border-radius: 3px;
                            cursor: pointer;
                            margin-top: 10px;
                        ">定位到此机井</button>
                    </div>
                `);
            
            wellMarkers[well.id] = marker;
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '正常',
                'warning': '警告',
                'error': '故障'
            };
            return statusMap[status] || status;
        }
        
        // 更新机井列表
        function updateWellList(wells) {
            const wellList = document.querySelector('.well-list');
            if (!wellList) return;
            
            wellList.innerHTML = '';
            
            Object.values(wells).forEach(well => {
                const wellItem = document.createElement('div');
                wellItem.className = 'well-item';
                
                const gasLevel = well.gas_level || 0;
                const gasClass = gasLevel > 80 ? 'gas-high' : gasLevel > 50 ? 'gas-medium' : 'gas-low';
                
                wellItem.innerHTML = `
                    <div class="well-header">
                        <div class="well-name">${well.name}</div>
                        <div class="well-status status-${well.status}">${getStatusText(well.status)}</div>
                    </div>
                    <div class="well-info">
                        <div class="info-item">
                            <div class="info-label">机井ID</div>
                            <div class="info-value">${well.id}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">送风机</div>
                            <div class="info-value">${well.fan_status === 'on' ? '开启' : '关闭'}</div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">气体浓度</div>
                        <div class="gas-level">
                            <div class="gas-bar">
                                <div class="gas-fill ${gasClass}" style="width: ${gasLevel}%"></div>
                            </div>
                            <span>${gasLevel}%</span>
                        </div>
                    </div>
                    <div class="coordinates">
                        ${well.lat.toFixed(6)}, ${well.lon.toFixed(6)}
                    </div>
                `;
                
                wellItem.addEventListener('click', () => flyToWell(well.id));
                wellList.appendChild(wellItem);
            });
        }
        
        // 定位到机井
        function flyToWell(wellId) {
            const marker = wellMarkers[wellId];
            if (marker) {
                map.flyTo(marker.getLatLng(), 15);
                marker.openPopup();
            }
        }
        
        // 设置最高精度定位
        function setMaxPrecision() {
            document.getElementById('precision-level').value = '20';
            const lat = parseFloat(document.getElementById('gps-lat').value);
            const lon = parseFloat(document.getElementById('gps-lon').value);
            
            if (!isNaN(lat) && !isNaN(lon)) {
                // 智能精度处理 - 使用18级在线地图 + 矢量数据
                const actualZoom = mapConfig.max_zoom || 18;
                map.flyTo([lat, lon], actualZoom);
                
                // 根据GPS坐标确定区域并加载超高精度矢量数据
                const region = getRegionByCoordinates(lat, lon);
                if (region && mapConfig.vector_data_available) {
                    loadUltraPrecisionData(region);
                }
                
                // 添加高精度标记
                if (currentLocationMarker) {
                    map.removeLayer(currentLocationMarker);
                }
                
                currentLocationMarker = L.marker([lat, lon], {
                    icon: L.divIcon({
                        className: 'high-precision-marker',
                        html: `<div style="
                            background: #ff4444;
                            width: 20px;
                            height: 20px;
                            border-radius: 50%;
                            border: 3px solid white;
                            box-shadow: 0 0 10px rgba(255,68,68,0.8);
                            animation: pulse 1s infinite;
                        "></div>`,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                currentLocationMarker.bindPopup(`
                    <div style="text-align: center;">
                        <h4>🎯 最高精度定位</h4>
                        <p><strong>坐标:</strong> ${lat.toFixed(6)}, ${lon.toFixed(6)}</p>
                        <p><strong>地图精度:</strong> ${actualZoom}级 (在线地图最高)</p>
                        <p><strong>矢量精度:</strong> 20级 (矢量数据)</p>
                        <p><strong>综合精度:</strong> ±1-5米</p>
                    </div>
                `).openPopup();
            } else {
                alert('请先输入GPS坐标');
            }
        }
        
        // 根据坐标确定区域
        function getRegionByCoordinates(lat, lon) {
            // 具体城市坐标范围映射（优先匹配）
            const cities = {
                '恩施': { north: 30.5, south: 30.0, east: 109.8, west: 109.0 },
                '武汉': { north: 30.8, south: 30.4, east: 114.6, west: 114.0 },
                '北京': { north: 40.2, south: 39.4, east: 117.4, west: 115.7 },
                '上海': { north: 31.9, south: 30.7, east: 122.1, west: 120.9 },
                '重庆': { north: 32.2, south: 28.2, east: 110.2, west: 105.3 }
            };
            
            // 先检查具体城市
            for (const [city, bounds] of Object.entries(cities)) {
                if (lat >= bounds.south && lat <= bounds.north && 
                    lon >= bounds.west && lon <= bounds.east) {
                    return city;
                }
            }
            
            // 中国各省市坐标范围映射
            const regions = {
                '北京': { north: 40.2, south: 39.4, east: 117.4, west: 115.7 },
                '上海': { north: 31.9, south: 30.7, east: 122.1, west: 120.9 },
                '天津': { north: 40.2, south: 38.6, east: 118.1, west: 116.7 },
                '重庆': { north: 32.2, south: 28.2, east: 110.2, west: 105.3 },
                '河北': { north: 42.6, south: 36.1, east: 119.8, west: 113.5 },
                '山西': { north: 40.7, south: 34.6, east: 114.6, west: 110.2 },
                '内蒙古': { north: 53.3, south: 37.2, east: 126.0, west: 97.2 },
                '辽宁': { north: 43.3, south: 38.7, east: 125.5, west: 118.5 },
                '吉林': { north: 46.3, south: 40.9, east: 131.2, west: 121.6 },
                '黑龙江': { north: 53.6, south: 43.4, east: 135.1, west: 121.1 },
                '江苏': { north: 35.1, south: 30.7, east: 121.9, west: 116.2 },
                '浙江': { north: 31.4, south: 27.0, east: 123.2, west: 118.0 },
                '安徽': { north: 35.1, south: 29.4, east: 119.3, west: 114.9 },
                '福建': { north: 28.3, south: 23.5, east: 120.4, west: 115.8 },
                '江西': { north: 30.0, south: 24.5, east: 118.5, west: 113.6 },
                '山东': { north: 38.4, south: 34.4, east: 122.7, west: 114.8 },
                '河南': { north: 36.4, south: 31.2, east: 116.7, west: 110.4 },
                '湖北': { north: 33.3, south: 29.0, east: 116.1, west: 108.3 },
                '湖南': { north: 30.1, south: 24.6, east: 114.3, west: 108.8 },
                '广东': { north: 25.3, south: 20.2, east: 117.2, west: 109.7 },
                '广西': { north: 26.2, south: 20.9, east: 112.0, west: 104.3 },
                '海南': { north: 20.1, south: 18.2, east: 111.1, west: 108.6 },
                '四川': { north: 34.3, south: 26.0, east: 108.5, west: 97.3 },
                '贵州': { north: 29.2, south: 24.6, east: 109.6, west: 103.6 },
                '云南': { north: 29.2, south: 21.1, east: 106.2, west: 97.5 },
                '西藏': { north: 36.5, south: 26.9, east: 99.1, west: 78.4 },
                '陕西': { north: 39.6, south: 31.4, east: 111.3, west: 105.5 },
                '甘肃': { north: 42.8, south: 32.1, east: 108.7, west: 92.1 },
                '青海': { north: 39.2, south: 31.6, east: 103.1, west: 89.4 },
                '宁夏': { north: 39.4, south: 35.2, east: 107.6, west: 104.2 },
                '新疆': { north: 48.2, south: 34.3, east: 96.4, west: 73.4 }
            };
            
            for (const [region, bounds] of Object.entries(regions)) {
                if (lat >= bounds.south && lat <= bounds.north && 
                    lon >= bounds.west && lon <= bounds.east) {
                    return region;
                }
            }
            return null; // 不在任何已知区域内
        }
        
        // 根据GPS定位
        async function locateByGPS() {
            const lat = parseFloat(document.getElementById('gps-lat').value);
            const lon = parseFloat(document.getElementById('gps-lon').value);
            const radius = parseInt(document.getElementById('search-radius').value);
            const precisionLevel = parseInt(document.getElementById('precision-level').value);
            
            if (isNaN(lat) || isNaN(lon)) {
                alert('请输入有效的GPS坐标');
                return;
            }
            
            try {
                const response = await fetch('/api/locate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ lat, lon, radius })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 智能精度处理 - 如果选择超过18级，使用18级并加载矢量数据
                    let actualZoom = precisionLevel;
                    if (precisionLevel > mapConfig.max_zoom) {
                        actualZoom = mapConfig.max_zoom;
                        console.log(`精度级别${precisionLevel}超过在线地图支持，使用${actualZoom}级并加载矢量数据`);
                    }
                    
                    // 定位到GPS坐标
                    map.flyTo([lat, lon], actualZoom);
                    
                    // 根据GPS坐标确定区域并加载矢量数据
                    const region = getRegionByCoordinates(lat, lon);
                    if (region && mapConfig.vector_data_available) {
                        // 根据精度级别选择数据源
                        if (precisionLevel >= 20) {
                            loadUltraPrecisionData(region);
                        } else if (precisionLevel > 18) {
                            loadHighPrecisionData(region);
                        } else {
                            loadVectorData(region);
                        }
                    }
                    
                    // 添加当前位置标记
                    if (currentLocationMarker) {
                        map.removeLayer(currentLocationMarker);
                    }
                    
                    currentLocationMarker = L.marker([lat, lon], {
                        icon: L.divIcon({
                            className: 'current-location-marker',
                            html: `<div style="
                                width: 15px;
                                height: 15px;
                                background: #007bff;
                                border: 3px solid white;
                                border-radius: 50%;
                                box-shadow: 0 2px 10px rgba(0,123,255,0.5);
                                animation: pulse 2s infinite;
                            "></div>`,
                            iconSize: [15, 15],
                            iconAnchor: [7, 7]
                        })
                    }).addTo(map).bindPopup(`
                        <div>
                            <h4>📍 GPS定位点</h4>
                            <p><strong>坐标:</strong> ${lat.toFixed(6)}, ${lon.toFixed(6)}</p>
                            <p><strong>附近机井:</strong> ${result.count}个</p>
                        </div>
                    `).openPopup();
                    
                    // 显示附近机井
                    if (result.nearby_wells.length > 0) {
                        let message = `找到 ${result.count} 个附近机井:\n`;
                        result.nearby_wells.forEach(well => {
                            message += `- ${well.well.name} (距离: ${well.distance.toFixed(0)}米)\n`;
                        });
                        alert(message);
                    } else {
                        alert('在指定范围内未找到机井');
                    }
                } else {
                    alert('定位失败: ' + result.error);
                }
            } catch (error) {
                console.error('定位失败:', error);
                alert('定位失败，请检查网络连接');
            }
        }
        
        // 获取当前位置
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lon = position.coords.longitude;
                        
                        document.getElementById('gps-lat').value = lat;
                        document.getElementById('gps-lon').value = lon;
                        
                        // 自动定位
                        locateByGPS();
                    },
                    (error) => {
                        alert('获取当前位置失败: ' + error.message);
                    }
                );
            } else {
                alert('浏览器不支持地理位置功能');
            }
        }
        
        // 搜索机井
        async function searchWells() {
            const query = document.getElementById('search-input').value.trim();
            const resultsDiv = document.getElementById('search-results');
            
            if (query.length < 2) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            try {
                const response = await fetch('/api/search-location', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query })
                });
                
                const result = await response.json();
                
                if (result.success && result.results.length > 0) {
                    resultsDiv.innerHTML = '';
                    result.results.forEach(item => {
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'search-result';
                        resultDiv.innerHTML = `
                            <div class="result-name">${item.name}</div>
                            <div class="result-type">${item.type} - ${item.status}</div>
                        `;
                        resultDiv.addEventListener('click', () => {
                            map.flyTo([item.lat, item.lon], 15);
                            resultsDiv.style.display = 'none';
                        });
                        resultsDiv.appendChild(resultDiv);
                    });
                    resultsDiv.style.display = 'block';
                } else {
                    resultsDiv.style.display = 'none';
                }
            } catch (error) {
                console.error('搜索失败:', error);
            }
        }
        
        // 添加机井
        async function addWell() {
            const id = document.getElementById('well-id').value.trim();
            const name = document.getElementById('well-name').value.trim();
            const lat = parseFloat(document.getElementById('well-lat').value);
            const lon = parseFloat(document.getElementById('well-lon').value);
            
            if (!id || !name || isNaN(lat) || isNaN(lon)) {
                alert('请填写完整的机井信息');
                return;
            }
            
            try {
                const response = await fetch('/api/wells', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id, name, lat, lon,
                        status: 'active',
                        gas_level: 0,
                        fan_status: 'off',
                        description: '新添加的机井'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('机井添加成功');
                    loadWells(); // 重新加载数据
                    // 清空表单
                    document.getElementById('well-id').value = '';
                    document.getElementById('well-name').value = '';
                    document.getElementById('well-lat').value = '';
                    document.getElementById('well-lon').value = '';
                } else {
                    alert('添加失败: ' + result.error);
                }
            } catch (error) {
                console.error('添加机井失败:', error);
                alert('添加失败，请检查网络连接');
            }
        }
        
        // 更新机井GPS
        async function updateWellGPS() {
            const id = document.getElementById('well-id').value.trim();
            const lat = parseFloat(document.getElementById('well-lat').value);
            const lon = parseFloat(document.getElementById('well-lon').value);
            
            if (!id || isNaN(lat) || isNaN(lon)) {
                alert('请填写机井ID和GPS坐标');
                return;
            }
            
            try {
                const response = await fetch(`/api/wells/${id}/gps`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ lat, lon })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('GPS坐标更新成功');
                    loadWells(); // 重新加载数据
                } else {
                    alert('更新失败: ' + result.error);
                }
            } catch (error) {
                console.error('更新GPS失败:', error);
                alert('更新失败，请检查网络连接');
            }
        }
        
        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/statistics');
                const stats = await response.json();
                
                document.getElementById('statistics-content').innerHTML = `
                    <div class="stat-item">
                        <span class="stat-label">总机井数</span>
                        <span class="stat-value">${stats.total_wells}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">正常机井</span>
                        <span class="stat-value">${stats.active_wells}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">警告机井</span>
                        <span class="stat-value">${stats.warning_wells}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最后更新</span>
                        <span class="stat-value">${new Date(stats.last_update).toLocaleTimeString()}</span>
                    </div>
                `;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            await initMap();
            
            // 定期更新数据
            setInterval(() => {
                loadWells();
                loadStatistics();
            }, 30000); // 每30秒更新一次
        });
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.2); opacity: 0.7; }
                100% { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
