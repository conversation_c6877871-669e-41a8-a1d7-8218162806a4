#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的瓦片生成工具
直接从在线源下载中国地区的地图瓦片
"""

import os
import sys
import requests
import math
import time
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class SimpleTileGenerator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.tiles_dir = self.base_dir / "static" / "tiles"
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 中国主要城市和监控点
        self.locations = {
            "beijing": {"lat": 39.9042, "lng": 116.4074, "name": "北京", "priority": 1},
            "tianjin": {"lat": 39.3434, "lng": 117.3616, "name": "天津", "priority": 2},
            "shijiazhuang": {"lat": 38.0428, "lng": 114.5149, "name": "石家庄", "priority": 2},
            "taiyuan": {"lat": 37.8706, "lng": 112.5489, "name": "太原", "priority": 3},
            "hohhot": {"lat": 40.8414, "lng": 111.7519, "name": "呼和浩特", "priority": 3},
            "shenyang": {"lat": 41.8057, "lng": 123.4315, "name": "沈阳", "priority": 2},
            "changchun": {"lat": 43.8171, "lng": 125.3235, "name": "长春", "priority": 3},
            "harbin": {"lat": 45.8038, "lng": 126.5349, "name": "哈尔滨", "priority": 3},
            "shanghai": {"lat": 31.2304, "lng": 121.4737, "name": "上海", "priority": 1},
            "nanjing": {"lat": 32.0603, "lng": 118.7969, "name": "南京", "priority": 2},
            "hangzhou": {"lat": 30.2741, "lng": 120.1551, "name": "杭州", "priority": 2},
            "hefei": {"lat": 31.8206, "lng": 117.2272, "name": "合肥", "priority": 3},
            "fuzhou": {"lat": 26.0745, "lng": 119.2965, "name": "福州", "priority": 3},
            "nanchang": {"lat": 28.6820, "lng": 115.8581, "name": "南昌", "priority": 3},
            "jinan": {"lat": 36.6512, "lng": 117.1201, "name": "济南", "priority": 3},
            "zhengzhou": {"lat": 34.7466, "lng": 113.6253, "name": "郑州", "priority": 3},
            "wuhan": {"lat": 30.5928, "lng": 114.3055, "name": "武汉", "priority": 2},
            "changsha": {"lat": 28.2282, "lng": 112.9388, "name": "长沙", "priority": 3},
            "guangzhou": {"lat": 23.1291, "lng": 113.2644, "name": "广州", "priority": 1},
            "nanning": {"lat": 22.8170, "lng": 108.3669, "name": "南宁", "priority": 3},
            "haikou": {"lat": 20.0444, "lng": 110.1989, "name": "海口", "priority": 3},
            "chongqing": {"lat": 29.5647, "lng": 106.5507, "name": "重庆", "priority": 2},
            "chengdu": {"lat": 30.5728, "lng": 104.0668, "name": "成都", "priority": 2},
            "guiyang": {"lat": 26.6470, "lng": 106.6302, "name": "贵阳", "priority": 3},
            "kunming": {"lat": 25.0389, "lng": 102.7183, "name": "昆明", "priority": 3},
            "lhasa": {"lat": 29.6520, "lng": 91.1721, "name": "拉萨", "priority": 3},
            "xian": {"lat": 34.3416, "lng": 108.9398, "name": "西安", "priority": 2},
            "lanzhou": {"lat": 36.0611, "lng": 103.8343, "name": "兰州", "priority": 3},
            "xining": {"lat": 36.6171, "lng": 101.7782, "name": "西宁", "priority": 3},
            "yinchuan": {"lat": 38.4872, "lng": 106.2309, "name": "银川", "priority": 3},
            "urumqi": {"lat": 43.8256, "lng": 87.6168, "name": "乌鲁木齐", "priority": 3}
        }
        
        # 瓦片服务器列表
        self.tile_servers = [
            {
                "name": "OpenStreetMap",
                "url": "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                "max_zoom": 19,
                "attribution": "© OpenStreetMap contributors"
            },
            {
                "name": "OpenStreetMap DE",
                "url": "https://tile.openstreetmap.de/{z}/{x}/{y}.png", 
                "max_zoom": 18,
                "attribution": "© OpenStreetMap contributors"
            }
        ]
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """将经纬度转换为瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """将瓦片坐标转换为经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def download_tile(self, server, z, x, y, max_retries=3):
        """下载单个瓦片"""
        tile_dir = self.tiles_dir / str(z) / str(x)
        tile_dir.mkdir(parents=True, exist_ok=True)
        tile_file = tile_dir / f"{y}.png"
        
        # 如果文件已存在且大小合理，跳过
        if tile_file.exists() and tile_file.stat().st_size > 100:
            return True, f"已存在: {z}/{x}/{y}"
        
        url = server["url"].format(z=z, x=x, y=y)
        
        for attempt in range(max_retries):
            try:
                headers = {
                    'User-Agent': 'WellMonitoringSystem/1.0 (Contact: <EMAIL>)'
                }
                
                response = requests.get(url, timeout=15, headers=headers)
                
                if response.status_code == 200:
                    with open(tile_file, 'wb') as f:
                        f.write(response.content)
                    return True, f"下载成功: {z}/{x}/{y}"
                elif response.status_code == 404:
                    return False, f"瓦片不存在: {z}/{x}/{y}"
                else:
                    if attempt == max_retries - 1:
                        return False, f"HTTP {response.status_code}: {z}/{x}/{y}"
                    time.sleep(1)  # 重试前等待
                    
            except Exception as e:
                if attempt == max_retries - 1:
                    return False, f"下载失败 {z}/{x}/{y}: {e}"
                time.sleep(1)
        
        return False, f"重试失败: {z}/{x}/{y}"
    
    def generate_tiles_for_location(self, location_info, zoom_levels, radius=3):
        """为指定位置生成瓦片"""
        lat, lng = location_info["lat"], location_info["lng"]
        name = location_info["name"]
        
        print(f"\n📍 处理 {name} ({lat:.4f}, {lng:.4f})")
        
        total_tiles = 0
        successful_tiles = 0
        
        for zoom in zoom_levels:
            print(f"  🔍 缩放级别 {zoom}")
            
            # 计算中心瓦片坐标
            center_x, center_y = self.deg2num(lat, lng, zoom)
            
            # 计算需要下载的瓦片范围
            tiles_to_download = []
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    tile_x = center_x + dx
                    tile_y = center_y + dy
                    
                    # 检查瓦片坐标是否有效
                    max_coord = 2 ** zoom
                    if 0 <= tile_x < max_coord and 0 <= tile_y < max_coord:
                        tiles_to_download.append((tile_x, tile_y))
            
            print(f"    需要下载 {len(tiles_to_download)} 个瓦片")
            
            # 使用线程池下载瓦片
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                
                for tile_x, tile_y in tiles_to_download:
                    # 选择服务器 (轮询)
                    server = self.tile_servers[total_tiles % len(self.tile_servers)]
                    
                    future = executor.submit(
                        self.download_tile, server, zoom, tile_x, tile_y
                    )
                    futures.append(future)
                    total_tiles += 1
                
                # 收集结果
                for future in as_completed(futures):
                    success, message = future.result()
                    if success:
                        successful_tiles += 1
                    
                    # 每10个瓦片显示一次进度
                    if total_tiles % 10 == 0:
                        progress = (successful_tiles / total_tiles) * 100
                        print(f"    进度: {successful_tiles}/{total_tiles} ({progress:.1f}%)")
                    
                    # 添加延迟避免过于频繁的请求
                    time.sleep(0.05)
        
        return total_tiles, successful_tiles
    
    def generate_china_tiles(self, priority_filter=None, zoom_levels=None):
        """生成中国地区瓦片"""
        zoom_levels = zoom_levels or [8, 10, 12, 15]
        
        print("🗺️  开始生成中国地区地图瓦片")
        print("=" * 50)
        print(f"📊 缩放级别: {zoom_levels}")
        print(f"📊 城市数量: {len(self.locations)}")
        
        if priority_filter:
            filtered_locations = {
                k: v for k, v in self.locations.items() 
                if v["priority"] <= priority_filter
            }
            print(f"📊 优先级过滤: <= {priority_filter} ({len(filtered_locations)} 个城市)")
        else:
            filtered_locations = self.locations
        
        total_tiles_all = 0
        successful_tiles_all = 0
        
        start_time = time.time()
        
        for location_id, location_info in filtered_locations.items():
            try:
                total, successful = self.generate_tiles_for_location(
                    location_info, zoom_levels
                )
                total_tiles_all += total
                successful_tiles_all += successful
                
                # 显示当前进度
                elapsed = time.time() - start_time
                print(f"  ✅ 完成 {location_info['name']}: {successful}/{total} 瓦片")
                print(f"  ⏱️  已用时间: {elapsed/60:.1f} 分钟")
                
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断，已处理 {successful_tiles_all}/{total_tiles_all} 瓦片")
                break
            except Exception as e:
                print(f"  ❌ 处理 {location_info['name']} 时出错: {e}")
                continue
        
        # 显示最终统计
        elapsed_total = time.time() - start_time
        success_rate = (successful_tiles_all / total_tiles_all * 100) if total_tiles_all > 0 else 0
        
        print(f"\n🎉 瓦片生成完成!")
        print(f"📊 总瓦片数: {total_tiles_all}")
        print(f"📊 成功下载: {successful_tiles_all}")
        print(f"📊 成功率: {success_rate:.1f}%")
        print(f"📊 总用时: {elapsed_total/60:.1f} 分钟")
        
        return successful_tiles_all > 0
    
    def get_tile_stats(self):
        """获取瓦片统计信息"""
        if not self.tiles_dir.exists():
            return {"tiles": 0, "size_mb": 0, "zoom_levels": []}
        
        total_tiles = 0
        total_size = 0
        zoom_levels = set()
        
        for tile_file in self.tiles_dir.rglob("*.png"):
            total_tiles += 1
            total_size += tile_file.stat().st_size
            
            # 提取缩放级别
            parts = tile_file.parts
            if len(parts) >= 3:
                try:
                    zoom = int(parts[-3])
                    zoom_levels.add(zoom)
                except ValueError:
                    pass
        
        return {
            "tiles": total_tiles,
            "size_mb": round(total_size / 1024 / 1024, 2),
            "zoom_levels": sorted(list(zoom_levels))
        }

def main():
    generator = SimpleTileGenerator()
    
    print("🗺️  简化瓦片生成工具")
    print("=" * 50)
    
    # 显示当前状态
    stats = generator.get_tile_stats()
    print(f"📊 当前瓦片: {stats['tiles']} 个")
    print(f"📊 占用空间: {stats['size_mb']} MB")
    print(f"📊 缩放级别: {stats['zoom_levels']}")
    
    if stats['tiles'] > 0:
        print(f"\n✅ 检测到已有瓦片数据")
        choice = input("是否继续添加更多瓦片? (Y/n): ").lower()
        if choice == 'n':
            print("👋 保持现有数据")
            return True
    
    print(f"\n📋 生成选项:")
    print(f"1. 快速生成 (优先级1城市: 北京、上海、广州)")
    print(f"2. 标准生成 (优先级1-2城市)")
    print(f"3. 完整生成 (所有城市)")
    print(f"4. 自定义生成")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == '1':
        success = generator.generate_china_tiles(priority_filter=1, zoom_levels=[10, 12, 15])
    elif choice == '2':
        success = generator.generate_china_tiles(priority_filter=2, zoom_levels=[8, 10, 12, 15])
    elif choice == '3':
        success = generator.generate_china_tiles(zoom_levels=[8, 10, 12, 15, 16])
    elif choice == '4':
        zoom_input = input("输入缩放级别 (如: 8,10,12,15): ").strip()
        try:
            zoom_levels = [int(z.strip()) for z in zoom_input.split(',')]
            priority = input("输入优先级过滤 (1-3, 回车跳过): ").strip()
            priority_filter = int(priority) if priority else None
            success = generator.generate_china_tiles(priority_filter, zoom_levels)
        except ValueError:
            print("❌ 输入格式错误")
            return False
    else:
        print("❌ 无效选择")
        return False
    
    if success:
        # 显示最终统计
        final_stats = generator.get_tile_stats()
        print(f"\n📊 最终统计:")
        print(f"   瓦片数量: {final_stats['tiles']}")
        print(f"   占用空间: {final_stats['size_mb']} MB")
        print(f"   缩放级别: {final_stats['zoom_levels']}")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 测试地图: python start_system.py map")
        print(f"   2. 启动系统: python start_system.py test")
        print(f"   3. 访问界面: http://localhost:5000")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
        sys.exit(0)