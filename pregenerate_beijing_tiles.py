#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预生成北京地区的20米精度瓦片
确保地图能够正常显示
"""

import os
import sys
import math
import time
import osmium
from pathlib import Path
from PIL import Image, ImageDraw
import sqlite3
from concurrent.futures import ThreadPoolExecutor

class BeijingOSMProcessor(osmium.SimpleHandler):
    """北京地区OSM处理器"""
    
    def __init__(self, bbox, zoom_level):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox
        self.zoom = zoom_level
        self.nodes = {}
        self.roads = []
        self.buildings = []
        self.water_features = []
        self.node_count = 0
        self.way_count = 0
        
    def node(self, n):
        """处理节点"""
        self.node_count += 1
        
        # 限制处理数量以提高速度
        if self.node_count > 20000:
            return
            
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 8),
                'lat': round(n.location.lat, 8),
                'tags': dict(n.tags)
            }
    
    def way(self, w):
        """处理路径"""
        self.way_count += 1
        
        if self.way_count > 5000:
            return
            
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if len(relevant_nodes) >= 2:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)

class BeijingTileGenerator:
    """北京瓦片生成器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.tiles_dir = self.base_dir / "static" / "fast_precision_tiles"
        self.db_path = self.base_dir / "fast_precision_data.db"
        
        # 创建目录
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        
        # 北京地区边界 [min_lon, min_lat, max_lon, max_lat]
        self.beijing_bbox = [115.7, 39.4, 117.4, 41.6]
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def generate_tile(self, z, x, y):
        """生成单个瓦片"""
        try:
            print(f"生成瓦片 {z}/{x}/{y}")
            start_time = time.time()
            
            bbox = self.get_tile_bbox(x, y, z)
            
            # 检查是否在北京范围内
            if not self.bbox_intersects(bbox, self.beijing_bbox):
                return self.create_empty_tile_file(z, x, y)
            
            # 处理OSM数据
            processor = BeijingOSMProcessor(bbox, z)
            processor.apply_file(str(self.osm_file))
            
            # 创建图像
            img = Image.new('RGB', (256, 256), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 256)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 256)
                return (max(0, min(255, px)), max(0, min(255, py)))
            
            # 绘制水体
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4')
            
            # 绘制建筑物
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#dddddd', outline='#999999')
            
            # 绘制道路
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 道路样式
                if highway_type in ['motorway', 'trunk']:
                    color = '#ff6600'
                    width = max(2, 6 - (18 - z))
                elif highway_type in ['primary', 'secondary']:
                    color = '#ffcc00'
                    width = max(1, 4 - (18 - z))
                elif highway_type in ['tertiary', 'residential']:
                    color = '#ffffff'
                    width = max(1, 2)
                else:
                    color = '#cccccc'
                    width = 1
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=color, width=width)
            
            # 添加信息
            elapsed = time.time() - start_time
            info_text = f"Z{z} {len(processor.roads)}R {len(processor.buildings)}B"
            draw.text((5, 5), info_text, fill='#333333')
            
            # 保存瓦片
            tile_path = self.tiles_dir / str(z) / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            img.save(tile_file, 'PNG', optimize=True)
            
            print(f"瓦片 {z}/{x}/{y} 完成，用时 {elapsed:.1f}秒，{len(processor.roads)}条道路，{len(processor.buildings)}个建筑")
            return True
            
        except Exception as e:
            print(f"生成瓦片失败 {z}/{x}/{y}: {e}")
            return self.create_empty_tile_file(z, x, y)
    
    def create_empty_tile_file(self, z, x, y):
        """创建空瓦片文件"""
        try:
            img = Image.new('RGB', (256, 256), color='#f0f0f0')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), f"Z{z}/{x}/{y}", fill='#999999')
            draw.text((10, 30), "No Data", fill='#999999')
            
            tile_path = self.tiles_dir / str(z) / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            img.save(tile_file, 'PNG')
            return True
        except Exception as e:
            print(f"创建空瓦片失败 {z}/{x}/{y}: {e}")
            return False
    
    def bbox_intersects(self, bbox1, bbox2):
        """检查两个边界框是否相交"""
        return not (bbox1[2] < bbox2[0] or bbox1[0] > bbox2[2] or 
                   bbox1[3] < bbox2[1] or bbox1[1] > bbox2[3])
    
    def generate_beijing_tiles(self):
        """生成北京地区瓦片"""
        print("🗺️ 开始生成北京地区20米精度瓦片")
        print("=" * 60)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return False
        
        print(f"✅ OSM文件: {self.osm_file.stat().st_size / (1024**3):.2f}GB")
        print(f"📁 瓦片目录: {self.tiles_dir}")
        
        # 生成不同缩放级别的瓦片
        zoom_levels = [12, 14, 16]  # 从低到高精度
        
        total_tiles = 0
        success_tiles = 0
        
        for zoom in zoom_levels:
            print(f"\n🔍 缩放级别 {zoom}")
            
            # 计算北京地区的瓦片范围
            min_x, max_y = self.deg2num(self.beijing_bbox[1], self.beijing_bbox[0], zoom)
            max_x, min_y = self.deg2num(self.beijing_bbox[3], self.beijing_bbox[2], zoom)
            
            # 限制瓦片数量
            if zoom <= 12:
                step = 1
            elif zoom <= 14:
                step = 2
            else:
                step = 4
            
            tiles_to_generate = []
            for x in range(min_x, max_x + 1, step):
                for y in range(min_y, max_y + 1, step):
                    tiles_to_generate.append((zoom, x, y))
            
            print(f"需要生成 {len(tiles_to_generate)} 个瓦片")
            total_tiles += len(tiles_to_generate)
            
            # 串行生成瓦片（避免内存问题）
            for i, (z, x, y) in enumerate(tiles_to_generate):
                if i % 10 == 0:
                    print(f"进度: {i}/{len(tiles_to_generate)} ({i/len(tiles_to_generate)*100:.1f}%)")
                
                if self.generate_tile(z, x, y):
                    success_tiles += 1
                
                # 每生成10个瓦片休息一下
                if i % 10 == 9:
                    time.sleep(1)
        
        print(f"\n🎉 瓦片生成完成!")
        print(f"📊 总瓦片: {total_tiles}")
        print(f"📊 成功: {success_tiles}")
        print(f"📊 成功率: {success_tiles/total_tiles*100:.1f}%")
        print(f"\n🚀 现在可以访问: http://127.0.0.1:5004")
        
        return success_tiles > 0

def main():
    """主函数"""
    print("🎯 北京地区20米精度瓦片预生成器")
    print("=" * 60)
    
    generator = BeijingTileGenerator()
    
    # 检查系统状态
    print("📋 系统检查:")
    print(f"   OSM文件: {'✅' if generator.osm_file.exists() else '❌'}")
    print(f"   瓦片目录: {'✅' if generator.tiles_dir.exists() else '❌'}")
    
    if not generator.osm_file.exists():
        print(f"\n❌ 请确保OSM文件存在: {generator.osm_file}")
        return False
    
    # 开始生成
    success = generator.generate_beijing_tiles()
    
    if success:
        print("\n✅ 预生成完成！现在可以在浏览器中查看地图了。")
    else:
        print("\n❌ 预生成失败，请检查错误信息。")
    
    return success

if __name__ == "__main__":
    main()
