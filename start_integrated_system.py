#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动整合矢量地图系统
"""

import os
import sys
import time
import webbrowser
import threading
from pathlib import Path
from integrated_vector_system import IntegratedVectorSystem

def main():
    """主函数"""
    print("🔧 整合矢量地图系统启动器")
    print("=" * 50)
    
    # 检查数据目录
    data_dir = Path("static/vector_data")
    if not data_dir.exists():
        print("❌ 数据目录不存在，请先下载矢量数据")
        return
    
    # 检查模板文件
    template_file = Path("templates/integrated_vector_map.html")
    if not template_file.exists():
        print("❌ 模板文件不存在")
        return
    
    # 创建系统实例
    system = IntegratedVectorSystem()
    
    print("\n📊 系统状态:")
    total_regions = len(system.regions)
    complete_regions = sum(1 for r in system.regions.values() if r["status"] == "complete")
    incomplete_regions = sum(1 for r in system.regions.values() if r["status"] == "incomplete")
    missing_regions = sum(1 for r in system.regions.values() if r["status"] == "missing")
    
    print(f"  - 总区域数: {total_regions}")
    print(f"  - 完整区域: {complete_regions}")
    print(f"  - 不完整区域: {incomplete_regions}")
    print(f"  - 缺失区域: {missing_regions}")
    print(f"  - 完成率: {round(complete_regions / total_regions * 100, 2) if total_regions > 0 else 0}%")
    print(f"  - 著名地标: {len(system.famous_places)}")
    
    if complete_regions == 0:
        print("\n❌ 没有完整的区域数据，请先下载矢量数据")
        return
    
    print(f"\n🚀 启动整合矢量地图系统...")
    print(f"🌐 系统将在 http://127.0.0.1:5000 启动")
    print(f"📱 请在浏览器中访问该地址")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 启动系统
        system.run(host='127.0.0.1', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")

if __name__ == "__main__":
    main()
