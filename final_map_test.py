#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终地图显示测试
"""

import requests
import webbrowser
import time

def test_map_display():
    """测试地图显示"""
    print("🗺️ 最终地图显示测试")
    print("=" * 60)
    
    # 测试GPS监控系统
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ GPS监控系统正常")
            print(f"📍 中心点: {config['center']}")
            print(f"🔍 缩放级别: {config['min_zoom']}-{config['max_zoom']}")
            print(f"🌐 离线模式: {config['offline_mode']}")
        else:
            print(f"❌ GPS监控系统异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GPS监控系统连接失败: {e}")
        return False
    
    # 测试瓦片服务器
    try:
        response = requests.get('http://127.0.0.1:8080/config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 瓦片服务器正常")
            print(f"📁 瓦片目录: {config['tiles_dir']}")
            print(f"📊 状态: {config['status']}")
        else:
            print(f"❌ 瓦片服务器异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 瓦片服务器连接失败: {e}")
        return False
    
    # 测试瓦片访问
    test_tile = "http://127.0.0.1:8080/tiles/18/215828/99324.png"
    try:
        response = requests.head(test_tile, timeout=5)
        if response.status_code == 200:
            print(f"✅ 瓦片访问正常: {test_tile}")
        else:
            print(f"❌ 瓦片访问失败: {test_tile} (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ 瓦片访问错误: {e}")
        return False
    
    print("\n🎉 所有测试通过！")
    print("=" * 60)
    return True

def show_access_info():
    """显示访问信息"""
    print("\n📱 系统访问地址:")
    print("=" * 60)
    print("🌐 GPS监控系统: http://127.0.0.1:5000")
    print("🗺️  瓦片服务器: http://127.0.0.1:8080")
    print("🧪 简单地图测试: file:///F:/monitor1/test_simple_map.html")
    
    print("\n🎯 使用说明:")
    print("=" * 60)
    print("1. 在浏览器中访问 http://127.0.0.1:5000")
    print("2. 地图应该显示北京地区")
    print("3. 初始缩放级别为15，可以缩放到10-18级")
    print("4. 可以通过GPS定位功能定位到具体位置")
    print("5. 支持机井管理和实时监控")
    
    print("\n🔧 如果地图仍不显示:")
    print("=" * 60)
    print("1. 清除浏览器缓存 (Ctrl+F5)")
    print("2. 检查浏览器控制台错误")
    print("3. 尝试访问简单地图测试页面")
    print("4. 确认两个服务都在运行")

if __name__ == "__main__":
    success = test_map_display()
    show_access_info()
    
    if success:
        print("\n✅ 地图显示测试完成，系统应该能正常显示地图！")
        print("🌐 正在打开GPS监控系统...")
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    else:
        print("\n❌ 地图显示测试失败，请检查错误信息！")
