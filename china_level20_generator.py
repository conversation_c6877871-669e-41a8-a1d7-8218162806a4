#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国全境20级瓦片生成器
对整个china-latest.osm.pbf文件进行处理，生成全国范围的20级超高精度瓦片
"""

import os
import sys
import math
import time
import osmium
from pathlib import Path
from PIL import Image, ImageDraw
import json
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing
import sqlite3

class ChinaLevel20OSMProcessor(osmium.SimpleHandler):
    """中国20级OSM处理器"""
    
    def __init__(self, bbox, region_name="unknown"):
        osmium.SimpleHandler.__init__(self)
        self.bbox = bbox
        self.region_name = region_name
        self.nodes = {}
        self.roads = []
        self.buildings = []
        self.water_features = []
        self.pois = []
        self.processed_nodes = 0
        self.processed_ways = 0
        
    def node(self, n):
        """处理节点 - 全国范围优化"""
        self.processed_nodes += 1
        
        # 每100万个节点显示进度
        if self.processed_nodes % 1000000 == 0:
            print(f"  [{self.region_name}] 已处理 {self.processed_nodes//1000000}M 节点...")
        
        if (self.bbox[0] <= n.location.lon <= self.bbox[2] and 
            self.bbox[1] <= n.location.lat <= self.bbox[3]):
            
            # 20级超高精度 - 10位小数
            self.nodes[n.id] = {
                'lon': round(n.location.lon, 10),
                'lat': round(n.location.lat, 10),
                'tags': dict(n.tags)
            }
            
            # POI检查
            if n.tags:
                poi_tags = ['amenity', 'shop', 'tourism', 'leisure', 'office', 'craft', 'emergency', 'healthcare']
                if any(tag in n.tags for tag in poi_tags):
                    self.pois.append({
                        'id': n.id,
                        'lon': round(n.location.lon, 10),
                        'lat': round(n.location.lat, 10),
                        'tags': dict(n.tags)
                    })
    
    def way(self, w):
        """处理路径 - 全国范围优化"""
        self.processed_ways += 1
        
        # 每10万条路径显示进度
        if self.processed_ways % 100000 == 0:
            print(f"  [{self.region_name}] 已处理 {self.processed_ways//1000}K 路径...")
        
        relevant_nodes = [n.ref for n in w.nodes if n.ref in self.nodes]
        
        if len(relevant_nodes) >= 2:
            way_data = {
                'id': w.id,
                'nodes': relevant_nodes,
                'tags': dict(w.tags)
            }
            
            if 'building' in w.tags:
                self.buildings.append(way_data)
            elif 'highway' in w.tags:
                self.roads.append(way_data)
            elif w.tags.get('natural') == 'water' or 'waterway' in w.tags:
                self.water_features.append(way_data)

class ChinaLevel20Generator:
    """中国全境20级瓦片生成器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.osm_file = self.base_dir / "china-latest.osm.pbf"
        self.tiles_dir = self.base_dir / "static" / "tiles" / "20"
        self.cache_dir = self.base_dir / "level20_cache"
        self.db_path = self.base_dir / "level20_tiles.db"
        
        # 创建目录
        self.tiles_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        
        # 中国主要区域定义 (20级重点区域)
        self.priority_regions = {
            "北京": {"bbox": [115.7, 39.4, 117.4, 41.6], "priority": 1},
            "上海": {"bbox": [120.9, 30.7, 122.1, 31.9], "priority": 1},
            "广州": {"bbox": [112.9, 22.9, 113.6, 23.6], "priority": 1},
            "深圳": {"bbox": [113.7, 22.4, 114.6, 22.9], "priority": 1},
            "杭州": {"bbox": [119.9, 30.0, 120.5, 30.5], "priority": 2},
            "南京": {"bbox": [118.4, 31.9, 119.2, 32.4], "priority": 2},
            "武汉": {"bbox": [113.8, 30.3, 114.7, 30.9], "priority": 2},
            "成都": {"bbox": [103.8, 30.4, 104.4, 31.0], "priority": 2},
            "西安": {"bbox": [108.7, 34.0, 109.3, 34.6], "priority": 2},
            "重庆": {"bbox": [106.3, 29.3, 107.0, 29.9], "priority": 2},
        }
        
    def init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tiles (
                z INTEGER,
                x INTEGER,
                y INTEGER,
                region TEXT,
                file_size INTEGER,
                features_count INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (z, x, y)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generation_progress (
                region TEXT PRIMARY KEY,
                total_tiles INTEGER,
                completed_tiles INTEGER,
                start_time TIMESTAMP,
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def deg2num(self, lat_deg, lon_deg, zoom):
        """经纬度转瓦片坐标"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def num2deg(self, x, y, zoom):
        """瓦片坐标转经纬度"""
        n = 2.0 ** zoom
        lon_deg = x / n * 360.0 - 180.0
        lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
        lat_deg = math.degrees(lat_rad)
        return (lat_deg, lon_deg)
    
    def get_tile_bbox(self, x, y, zoom):
        """获取瓦片边界"""
        lat1, lon1 = self.num2deg(x, y, zoom)
        lat2, lon2 = self.num2deg(x + 1, y + 1, zoom)
        return [min(lon1, lon2), min(lat1, lat2), max(lon1, lon2), max(lat1, lat2)]
    
    def generate_level20_tile(self, tile_info):
        """生成单个20级瓦片"""
        z, x, y, region_name = tile_info
        
        try:
            start_time = time.time()
            bbox = self.get_tile_bbox(x, y, z)
            
            # 检查瓦片是否已存在
            tile_path = self.tiles_dir / str(x)
            tile_path.mkdir(parents=True, exist_ok=True)
            tile_file = tile_path / f"{y}.png"
            
            if tile_file.exists() and tile_file.stat().st_size > 2000:
                return True, f"已存在: {z}/{x}/{y}"
            
            # 处理OSM数据
            processor = ChinaLevel20OSMProcessor(bbox, region_name)
            processor.apply_file(str(self.osm_file))
            
            # 创建超高分辨率图像
            img = Image.new('RGB', (512, 512), color='#f8f8f8')
            draw = ImageDraw.Draw(img)
            
            # 坐标转换
            def geo_to_pixel(lon, lat):
                px = int((lon - bbox[0]) / (bbox[2] - bbox[0]) * 512)
                py = int((bbox[3] - lat) / (bbox[3] - bbox[1]) * 512)
                return (max(0, min(511, px)), max(0, min(511, py)))
            
            # 绘制水体
            for water in processor.water_features:
                points = []
                for node_id in water['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    draw.polygon(points, fill='#87ceeb', outline='#4682b4', width=1)
            
            # 绘制建筑物 - 20级超详细
            for building in processor.buildings:
                points = []
                for node_id in building['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                if len(points) > 2:
                    building_type = building['tags'].get('building', 'yes')
                    
                    # 详细建筑分类
                    if building_type in ['house', 'residential', 'apartments']:
                        fill_color = '#e6e6e6'
                    elif building_type in ['commercial', 'retail', 'shop']:
                        fill_color = '#ffcccc'
                    elif building_type in ['industrial', 'warehouse']:
                        fill_color = '#ccccff'
                    elif building_type in ['school', 'university']:
                        fill_color = '#ccffcc'
                    elif building_type in ['hospital', 'clinic']:
                        fill_color = '#ffccff'
                    else:
                        fill_color = '#dddddd'
                    
                    draw.polygon(points, fill=fill_color, outline='#999999', width=1)
            
            # 绘制道路 - 20级显示所有道路
            for road in processor.roads:
                highway_type = road['tags'].get('highway', 'unknown')
                
                # 超详细道路分类
                road_styles = {
                    'motorway': {'color': '#ff6600', 'width': 10},
                    'trunk': {'color': '#ff8800', 'width': 8},
                    'primary': {'color': '#ffcc00', 'width': 6},
                    'secondary': {'color': '#ffff00', 'width': 5},
                    'tertiary': {'color': '#ffffff', 'width': 4},
                    'residential': {'color': '#ffffff', 'width': 3},
                    'unclassified': {'color': '#ffffff', 'width': 2},
                    'service': {'color': '#cccccc', 'width': 2},
                    'track': {'color': '#996633', 'width': 2},
                    'footway': {'color': '#ff9999', 'width': 1},
                    'path': {'color': '#ff9999', 'width': 1},
                    'cycleway': {'color': '#9999ff', 'width': 1},
                    'steps': {'color': '#ff6666', 'width': 1}
                }
                
                style = road_styles.get(highway_type, {'color': '#eeeeee', 'width': 1})
                
                points = []
                for node_id in road['nodes']:
                    if node_id in processor.nodes:
                        node = processor.nodes[node_id]
                        points.append(geo_to_pixel(node['lon'], node['lat']))
                
                # 绘制道路
                for i in range(len(points) - 1):
                    draw.line([points[i], points[i+1]], fill=style['color'], width=style['width'])
            
            # 绘制POI - 20级显示详细POI
            for poi in processor.pois:
                px, py = geo_to_pixel(poi['lon'], poi['lat'])
                
                # POI分类着色
                tags = poi['tags']
                if 'amenity' in tags:
                    amenity = tags['amenity']
                    if amenity in ['restaurant', 'cafe', 'fast_food']:
                        color = '#ff0000'
                        size = 4
                    elif amenity in ['hospital', 'clinic', 'pharmacy']:
                        color = '#00ff00'
                        size = 5
                    elif amenity in ['school', 'university']:
                        color = '#0000ff'
                        size = 5
                    elif amenity in ['bank', 'atm']:
                        color = '#ffff00'
                        size = 3
                    else:
                        color = '#ff00ff'
                        size = 3
                elif 'shop' in tags:
                    color = '#ff8800'
                    size = 3
                else:
                    color = '#888888'
                    size = 2
                
                draw.ellipse([px-size, py-size, px+size, py+size], 
                           fill=color, outline='#ffffff', width=1)
            
            # 添加详细信息
            elapsed = time.time() - start_time
            total_features = len(processor.roads) + len(processor.buildings) + len(processor.pois)
            info_text = f"L20 {len(processor.roads)}R {len(processor.buildings)}B {len(processor.pois)}P"
            draw.text((5, 5), info_text, fill='#333333')
            
            # 缩放到标准尺寸并保持高质量
            img = img.resize((256, 256), Image.LANCZOS)
            
            # 保存瓦片
            img.save(tile_file, 'PNG', optimize=True, quality=95)
            
            # 记录到数据库
            file_size = tile_file.stat().st_size
            self.record_tile_to_db(z, x, y, region_name, file_size, total_features)
            
            elapsed = time.time() - start_time
            return True, f"生成: {z}/{x}/{y} ({elapsed:.1f}s) {total_features}要素 {file_size}字节"
            
        except Exception as e:
            return False, f"失败: {z}/{x}/{y} - {e}"
    
    def record_tile_to_db(self, z, x, y, region, file_size, features_count):
        """记录瓦片到数据库"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            cursor.execute(
                'INSERT OR REPLACE INTO tiles (z, x, y, region, file_size, features_count) VALUES (?, ?, ?, ?, ?, ?)',
                (z, x, y, region, file_size, features_count)
            )
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"数据库记录失败: {e}")
    
    def generate_region_level20_tiles(self, region_name, region_info, max_workers=2):
        """生成指定区域的20级瓦片"""
        print(f"\n🗺️ 生成 {region_name} 20级瓦片")
        print("=" * 50)
        
        bbox = region_info["bbox"]
        priority = region_info["priority"]
        
        # 计算瓦片范围
        min_x, max_y = self.deg2num(bbox[1], bbox[0], 20)
        max_x, min_y = self.deg2num(bbox[3], bbox[2], 20)
        
        # 根据优先级调整密度
        if priority == 1:
            step = 1  # 最高密度
        else:
            step = 2  # 适中密度
        
        tasks = []
        for x in range(min_x, max_x + 1, step):
            for y in range(min_y, max_y + 1, step):
                tasks.append((20, x, y, region_name))
        
        print(f"📊 需要生成 {len(tasks)} 个瓦片")
        
        if not tasks:
            return True
        
        # 分批处理
        batch_size = 50
        total_success = 0
        
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i+batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(tasks) + batch_size - 1) // batch_size
            
            print(f"\n🔄 [{region_name}] 批次 {batch_num}/{total_batches} ({len(batch)} 个瓦片)")
            
            # 使用线程池处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                results = list(executor.map(self.generate_level20_tile, batch))
                
                batch_success = sum(1 for success, _ in results if success)
                total_success += batch_success
                
                print(f"   ✅ 批次完成: {batch_success}/{len(batch)} 成功")
                
                # 显示示例结果
                for success, message in results[:2]:
                    status = "✅" if success else "❌"
                    print(f"   {status} {message}")
            
            # 批次间休息
            time.sleep(1)
        
        success_rate = (total_success / len(tasks)) * 100 if tasks else 0
        print(f"\n🎉 {region_name} 完成: {total_success}/{len(tasks)} ({success_rate:.1f}%)")
        
        return total_success > 0
    
    def generate_china_level20_tiles(self, max_workers=2):
        """生成中国全境20级瓦片"""
        print("🇨🇳 中国全境20级超高精度瓦片生成器")
        print("=" * 60)
        
        if not self.osm_file.exists():
            print(f"❌ OSM文件不存在: {self.osm_file}")
            return False
        
        print(f"✅ OSM文件: {self.osm_file.stat().st_size / (1024**3):.2f}GB")
        print(f"📁 瓦片目录: {self.tiles_dir}")
        print(f"🔧 并行工作线程: {max_workers}")
        
        total_regions_success = 0
        
        # 按优先级处理区域
        for priority in [1, 2]:
            priority_regions = {k: v for k, v in self.priority_regions.items() if v["priority"] == priority}
            
            print(f"\n🎯 处理优先级 {priority} 区域 ({len(priority_regions)} 个)")
            
            for region_name, region_info in priority_regions.items():
                try:
                    success = self.generate_region_level20_tiles(region_name, region_info, max_workers)
                    if success:
                        total_regions_success += 1
                        
                except KeyboardInterrupt:
                    print(f"\n⚠️  用户中断，已完成 {total_regions_success} 个区域")
                    return total_regions_success > 0
                except Exception as e:
                    print(f"❌ 处理 {region_name} 时出错: {e}")
                    continue
        
        print(f"\n🎉 中国20级瓦片生成完成!")
        print(f"📊 成功区域: {total_regions_success}/{len(self.priority_regions)}")
        print(f"📁 瓦片位置: {self.tiles_dir}")
        
        return total_regions_success > 0

def main():
    """主函数"""
    print("🇨🇳 中国全境20级瓦片生成器")
    print("=" * 60)
    
    generator = ChinaLevel20Generator()
    
    try:
        # 使用适当的并行度
        cpu_count = multiprocessing.cpu_count()
        max_workers = min(3, cpu_count)  # 限制并行数避免内存问题
        
        print(f"💻 CPU核心数: {cpu_count}")
        print(f"🔧 使用线程数: {max_workers}")
        
        success = generator.generate_china_level20_tiles(max_workers=max_workers)
        
        if success:
            print("\n✅ 中国20级瓦片生成完成！")
            print("🌐 现在可以在地图系统中使用20级超高精度瓦片了")
        else:
            print("\n❌ 瓦片生成失败")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断生成")
    except Exception as e:
        print(f"\n❌ 生成过程出错: {e}")

if __name__ == "__main__":
    main()
