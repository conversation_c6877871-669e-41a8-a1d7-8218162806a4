<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超高精度数据调试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 600px; width: 100%; border: 2px solid #333; }
        .info { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        .status { font-weight: bold; color: #0066cc; }
        .error { color: #cc0000; }
        .success { color: #006600; }
        .debug { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 超高精度数据调试</h1>
    
    <div class="info">
        <div class="status">测试坐标: 30.295, 109.486 (恩施)</div>
        <div id="status">正在加载...</div>
    </div>
    
    <div id="map"></div>
    
    <div class="debug" id="debug-info">
        <h3>调试信息:</h3>
        <div id="debug-content">等待数据...</div>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([30.295, 109.486], 18);
        
        // 添加在线地图图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 状态显示
        const statusDiv = document.getElementById('status');
        const debugDiv = document.getElementById('debug-content');
        
        // 加载超高精度数据
        async function loadUltraPrecisionData() {
            try {
                statusDiv.innerHTML = '正在加载超高精度数据...';
                debugDiv.innerHTML = '开始加载数据...';
                
                const response = await fetch('/api/ultra-precision-data/恩施');
                debugDiv.innerHTML += `<br>API响应状态: ${response.status}`;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('超高精度数据:', data);
                
                debugDiv.innerHTML += `<br>数据加载成功: ${JSON.stringify(data.metadata, null, 2)}`;
                
                statusDiv.innerHTML = `<span class="success">✅ 数据加载成功: ${data.metadata.total_features}个要素</span>`;
                
                // 渲染数据
                renderUltraPrecisionData(data);
                
            } catch (error) {
                console.error('加载失败:', error);
                statusDiv.innerHTML = `<span class="error">❌ 加载失败: ${error.message}</span>`;
                debugDiv.innerHTML += `<br>错误: ${error.message}`;
            }
        }
        
        // 渲染超高精度数据
        function renderUltraPrecisionData(data) {
            if (!data || !map) {
                debugDiv.innerHTML += '<br>❌ 数据或地图为空';
                return;
            }
            
            debugDiv.innerHTML += '<br>开始渲染数据...';
            let renderedCount = 0;
            
            // 渲染道路数据
            if (data.roads && data.roads.features) {
                debugDiv.innerHTML += `<br>道路数据: ${data.roads.features.length}个要素`;
                const roadsLayer = L.geoJSON(data.roads, {
                    style: function(feature) {
                        const highway = feature.properties.highway;
                        let color = '#ff0000'; // 默认红色
                        let weight = 6; // 默认粗线
                        
                        switch(highway) {
                            case 'primary':
                                color = '#ff0000';
                                weight = 8;
                                break;
                            case 'secondary':
                                color = '#ff8800';
                                weight = 6;
                                break;
                            case 'residential':
                                color = '#00ff00';
                                weight = 4;
                                break;
                            default:
                                color = '#0000ff';
                                weight = 4;
                        }
                        
                        return {
                            color: color,
                            weight: weight,
                            opacity: 1.0
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名道路'}</strong><br>
                                类型: ${props.highway}<br>
                                车道数: ${props.lanes || '未知'}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.roads.features.length;
                debugDiv.innerHTML += `<br>✅ 道路渲染完成: ${data.roads.features.length}条`;
            } else {
                debugDiv.innerHTML += '<br>❌ 没有道路数据';
            }
            
            // 渲染建筑数据
            if (data.buildings && data.buildings.features) {
                debugDiv.innerHTML += `<br>建筑数据: ${data.buildings.features.length}个要素`;
                const buildingsLayer = L.geoJSON(data.buildings, {
                    style: function(feature) {
                        return {
                            color: '#ff0000',
                            weight: 4,
                            fillColor: '#ffff00',
                            fillOpacity: 0.9
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名建筑'}</strong><br>
                                类型: ${props.building}<br>
                                高度: ${props.height || '未知'}米
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.buildings.features.length;
                debugDiv.innerHTML += `<br>✅ 建筑渲染完成: ${data.buildings.features.length}个`;
            } else {
                debugDiv.innerHTML += '<br>❌ 没有建筑数据';
            }
            
            // 渲染POI数据
            if (data.pois && data.pois.features) {
                debugDiv.innerHTML += `<br>POI数据: ${data.pois.features.length}个要素`;
                const poisLayer = L.geoJSON(data.pois, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 10,
                            fillColor: '#00ff00',
                            color: '#000000',
                            weight: 3,
                            opacity: 1,
                            fillOpacity: 1.0
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名POI'}</strong><br>
                                类型: ${props.type}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.pois.features.length;
                debugDiv.innerHTML += `<br>✅ POI渲染完成: ${data.pois.features.length}个`;
            } else {
                debugDiv.innerHTML += '<br>❌ 没有POI数据';
            }
            
            // 渲染机井数据
            if (data.wells && data.wells.features) {
                debugDiv.innerHTML += `<br>机井数据: ${data.wells.features.length}个要素`;
                const wellsLayer = L.geoJSON(data.wells, {
                    pointToLayer: function(feature, latlng) {
                        return L.circleMarker(latlng, {
                            radius: 8,
                            fillColor: '#0066ff',
                            color: '#ffffff',
                            weight: 3,
                            opacity: 1,
                            fillOpacity: 1.0
                        });
                    },
                    onEachFeature: function(feature, layer) {
                        const props = feature.properties;
                        layer.bindPopup(`
                            <div style="font-size: 12px;">
                                <strong>${props.name || '未命名机井'}</strong><br>
                                状态: ${props.status}
                            </div>
                        `);
                    }
                }).addTo(map);
                renderedCount += data.wells.features.length;
                debugDiv.innerHTML += `<br>✅ 机井渲染完成: ${data.wells.features.length}个`;
            } else {
                debugDiv.innerHTML += '<br>❌ 没有机井数据';
            }
            
            statusDiv.innerHTML += `<br><span class="success">✅ 渲染完成: ${renderedCount}个要素</span>`;
            debugDiv.innerHTML += `<br>🎯 总渲染要素: ${renderedCount}个`;
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            loadUltraPrecisionData();
        });
    </script>
</body>
</html>
