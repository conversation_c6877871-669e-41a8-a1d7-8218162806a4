#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试地图修复效果
"""

import requests
import math

def deg2num(lat_deg, lon_deg, zoom):
    """将经纬度转换为瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def test_map_fix():
    """测试地图修复效果"""
    print("🔧 测试地图修复效果")
    print("=" * 60)
    
    # 获取地图配置
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ 地图配置获取成功")
            print(f"📍 中心点: {config['center']}")
            print(f"🗺️  边界: {config['bounds']}")
            print(f"📊 缩放级别: {config['min_zoom']}-{config['max_zoom']}")
        else:
            print(f"❌ 地图配置获取失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 地图配置获取失败: {e}")
        return
    
    # 测试中心点瓦片
    center_lat, center_lon = config['center']
    x, y = deg2num(center_lat, center_lon, 18)
    
    print(f"\n🎯 测试中心点瓦片:")
    print(f"📍 中心坐标: {center_lat}, {center_lon}")
    print(f"🗺️ 瓦片坐标: {x}, {y}")
    
    tile_url = f"http://127.0.0.1:8080/tiles/18/{x}/{y}.png"
    try:
        response = requests.head(tile_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ 中心点瓦片存在: {tile_url}")
        else:
            print(f"❌ 中心点瓦片不存在: {tile_url} (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 中心点瓦片测试失败: {e}")
    
    # 测试边界瓦片
    bounds = config['bounds']
    test_points = [
        (bounds['north'], bounds['west'], "西北角"),
        (bounds['north'], bounds['east'], "东北角"),
        (bounds['south'], bounds['west'], "西南角"),
        (bounds['south'], bounds['east'], "东南角")
    ]
    
    print(f"\n🔍 测试边界瓦片:")
    for lat, lon, name in test_points:
        x, y = deg2num(lat, lon, 18)
        tile_url = f"http://127.0.0.1:8080/tiles/18/{x}/{y}.png"
        try:
            response = requests.head(tile_url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}瓦片存在: {x}, {y}")
            else:
                print(f"❌ {name}瓦片不存在: {x}, {y} (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {name}瓦片测试失败: {e}")
    
    print(f"\n🎉 地图修复测试完成!")
    print("=" * 60)
    print("📱 请在浏览器中访问 http://127.0.0.1:5000")
    print("🗺️ 地图应该能正常显示，不再出现404错误")
    print("📍 地图中心已调整到有瓦片的区域")

if __name__ == "__main__":
    test_map_fix()
