#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度矢量数据渲染器
专门为野外作业监控设计，确保20米以内精度
"""

import xml.etree.ElementTree as ET
import json
import os
from pathlib import Path

class HighPrecisionVectorRenderer:
    def __init__(self, vector_data_path):
        self.vector_data_path = Path(vector_data_path)
        self.rendered_data = {}
        
    def parse_osm_data(self, osm_file):
        """解析OSM数据，提取高精度地理要素"""
        try:
            tree = ET.parse(osm_file)
            root = tree.getroot()
            
            data = {
                'nodes': {},
                'ways': {},
                'relations': {},
                'bounds': None
            }
            
            # 解析节点
            for node in root.findall('node'):
                node_id = node.get('id')
                lat = float(node.get('lat'))
                lon = float(node.get('lon'))
                
                tags = {}
                for tag in node.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                data['nodes'][node_id] = {
                    'lat': lat,
                    'lon': lon,
                    'tags': tags
                }
            
            # 解析路径
            for way in root.findall('way'):
                way_id = way.get('id')
                
                # 获取边界
                bounds = way.find('bounds')
                if bounds is not None:
                    bounds_data = {
                        'minlat': float(bounds.get('minlat')),
                        'minlon': float(bounds.get('minlon')),
                        'maxlat': float(bounds.get('maxlat')),
                        'maxlon': float(bounds.get('maxlon'))
                    }
                else:
                    bounds_data = None
                
                # 获取节点引用
                nd_refs = []
                for nd in way.findall('nd'):
                    nd_refs.append(nd.get('ref'))
                
                # 获取标签
                tags = {}
                for tag in way.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                data['ways'][way_id] = {
                    'nd_refs': nd_refs,
                    'tags': tags,
                    'bounds': bounds_data
                }
            
            # 解析关系
            for relation in root.findall('relation'):
                rel_id = relation.get('id')
                
                members = []
                for member in relation.findall('member'):
                    members.append({
                        'type': member.get('type'),
                        'ref': member.get('ref'),
                        'role': member.get('role')
                    })
                
                tags = {}
                for tag in relation.findall('tag'):
                    tags[tag.get('k')] = tag.get('v')
                
                data['relations'][rel_id] = {
                    'members': members,
                    'tags': tags
                }
            
            return data
            
        except Exception as e:
            print(f"解析OSM文件失败: {e}")
            return None
    
    def generate_high_precision_geojson(self, osm_data, data_type='roads'):
        """生成高精度GeoJSON数据"""
        if not osm_data:
            return None
        
        features = []
        
        if data_type == 'roads':
            # 处理道路数据
            for way_id, way_data in osm_data['ways'].items():
                if 'highway' in way_data['tags']:
                    # 构建坐标数组
                    coordinates = []
                    for nd_ref in way_data['nd_refs']:
                        if nd_ref in osm_data['nodes']:
                            node = osm_data['nodes'][nd_ref]
                            coordinates.append([node['lon'], node['lat']])
                    
                    if len(coordinates) >= 2:
                        feature = {
                            'type': 'Feature',
                            'properties': {
                                'id': way_id,
                                'highway': way_data['tags'].get('highway'),
                                'name': way_data['tags'].get('name', ''),
                                'surface': way_data['tags'].get('surface', ''),
                                'lanes': way_data['tags'].get('lanes', ''),
                                'maxspeed': way_data['tags'].get('maxspeed', ''),
                                'oneway': way_data['tags'].get('oneway', 'no')
                            },
                            'geometry': {
                                'type': 'LineString',
                                'coordinates': coordinates
                            }
                        }
                        features.append(feature)
        
        elif data_type == 'buildings':
            # 处理建筑数据
            for way_id, way_data in osm_data['ways'].items():
                if 'building' in way_data['tags']:
                    # 构建坐标数组
                    coordinates = []
                    for nd_ref in way_data['nd_refs']:
                        if nd_ref in osm_data['nodes']:
                            node = osm_data['nodes'][nd_ref]
                            coordinates.append([node['lon'], node['lat']])
                    
                    if len(coordinates) >= 3:
                        # 确保多边形闭合
                        if coordinates[0] != coordinates[-1]:
                            coordinates.append(coordinates[0])
                        
                        feature = {
                            'type': 'Feature',
                            'properties': {
                                'id': way_id,
                                'building': way_data['tags'].get('building'),
                                'name': way_data['tags'].get('name', ''),
                                'height': way_data['tags'].get('height', ''),
                                'levels': way_data['tags'].get('building:levels', ''),
                                'use': way_data['tags'].get('building:use', '')
                            },
                            'geometry': {
                                'type': 'Polygon',
                                'coordinates': [coordinates]
                            }
                        }
                        features.append(feature)
        
        elif data_type == 'pois':
            # 处理兴趣点数据
            for node_id, node_data in osm_data['nodes'].items():
                if node_data['tags']:
                    # 检查是否是POI
                    poi_types = ['amenity', 'shop', 'tourism', 'leisure', 'office', 'craft']
                    is_poi = any(tag in node_data['tags'] for tag in poi_types)
                    
                    if is_poi:
                        feature = {
                            'type': 'Feature',
                            'properties': {
                                'id': node_id,
                                'name': node_data['tags'].get('name', ''),
                                'type': self._get_poi_type(node_data['tags']),
                                'category': self._get_poi_category(node_data['tags'])
                            },
                            'geometry': {
                                'type': 'Point',
                                'coordinates': [node_data['lon'], node_data['lat']]
                            }
                        }
                        features.append(feature)
        
        return {
            'type': 'FeatureCollection',
            'features': features
        }
    
    def _get_poi_type(self, tags):
        """获取POI类型"""
        for tag_key in ['amenity', 'shop', 'tourism', 'leisure', 'office', 'craft']:
            if tag_key in tags:
                return f"{tag_key}:{tags[tag_key]}"
        return 'unknown'
    
    def _get_poi_category(self, tags):
        """获取POI分类"""
        if 'amenity' in tags:
            return '设施'
        elif 'shop' in tags:
            return '商店'
        elif 'tourism' in tags:
            return '旅游'
        elif 'leisure' in tags:
            return '休闲'
        elif 'office' in tags:
            return '办公'
        elif 'craft' in tags:
            return '工艺'
        return '其他'
    
    def process_region_data(self, region):
        """处理指定区域的高精度数据"""
        region_path = self.vector_data_path / region
        
        if not region_path.exists():
            print(f"区域数据不存在: {region}")
            return None
        
        processed_data = {
            'region': region,
            'roads': None,
            'buildings': None,
            'pois': None,
            'metadata': {
                'total_features': 0,
                'precision_level': 'high',
                'coordinate_precision': 6  # 小数点后6位，约0.1米精度
            }
        }
        
        # 处理道路数据
        roads_file = region_path / 'roads.osm'
        if roads_file.exists():
            osm_data = self.parse_osm_data(roads_file)
            if osm_data:
                processed_data['roads'] = self.generate_high_precision_geojson(osm_data, 'roads')
                if processed_data['roads']:
                    processed_data['metadata']['total_features'] += len(processed_data['roads']['features'])
        
        # 处理建筑数据
        buildings_file = region_path / 'buildings.osm'
        if buildings_file.exists():
            osm_data = self.parse_osm_data(buildings_file)
            if osm_data:
                processed_data['buildings'] = self.generate_high_precision_geojson(osm_data, 'buildings')
                if processed_data['buildings']:
                    processed_data['metadata']['total_features'] += len(processed_data['buildings']['features'])
        
        # 处理POI数据
        pois_file = region_path / 'pois.osm'
        if pois_file.exists():
            osm_data = self.parse_osm_data(pois_file)
            if osm_data:
                processed_data['pois'] = self.generate_high_precision_geojson(osm_data, 'pois')
                if processed_data['pois']:
                    processed_data['metadata']['total_features'] += len(processed_data['pois']['features'])
        
        return processed_data
    
    def save_processed_data(self, processed_data, output_path):
        """保存处理后的数据"""
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)
        
        print(f"高精度数据已保存: {output_file}")
        return output_file

def main():
    """主函数"""
    print("🎯 高精度矢量数据渲染器")
    print("=" * 60)
    
    # 初始化渲染器
    renderer = HighPrecisionVectorRenderer('static/vector_data')
    
    # 处理测试区域
    test_regions = ['北京', '重庆', '上海']
    
    for region in test_regions:
        print(f"\n🔧 处理区域: {region}")
        processed_data = renderer.process_region_data(region)
        
        if processed_data:
            # 保存处理后的数据
            output_path = f'static/processed_vector_data/{region}_high_precision.json'
            renderer.save_processed_data(processed_data, output_path)
            
            print(f"✅ {region} 处理完成")
            print(f"   - 总要素数: {processed_data['metadata']['total_features']}")
            print(f"   - 坐标精度: {processed_data['metadata']['coordinate_precision']}位小数")
            
            # 显示详细信息
            if processed_data['roads']:
                print(f"   - 道路要素: {len(processed_data['roads']['features'])}个")
            if processed_data['buildings']:
                print(f"   - 建筑要素: {len(processed_data['buildings']['features'])}个")
            if processed_data['pois']:
                print(f"   - POI要素: {len(processed_data['pois']['features'])}个")
        else:
            print(f"❌ {region} 处理失败")
    
    print("\n🎯 高精度数据处理完成！")
    print("💡 这些数据将提供20米以内的定位精度")

if __name__ == "__main__":
    main()
