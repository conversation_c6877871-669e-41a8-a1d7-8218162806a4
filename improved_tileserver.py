#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的瓦片服务器
支持更好的错误处理和日志记录
"""

from flask import Flask, send_file, jsonify, render_template_string
from pathlib import Path
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 配置路径
BASE_DIR = Path(__file__).parent
TILES_DIR = BASE_DIR / "static" / "tiles"
CONFIG_FILE = BASE_DIR / "map_config.json"

def load_config():
    """加载地图配置"""
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"配置文件加载失败: {e}")
        return None

@app.route('/')
def index():
    """主页面"""
    config = load_config()
    if not config:
        return "配置文件加载失败", 500
    
    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>{{ config.name }}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            max-width: 300px;
        }
        .info-panel h3 { margin: 0 0 10px 0; color: #333; }
        .info-panel p { margin: 5px 0; font-size: 14px; }
        .status { padding: 5px; border-radius: 3px; margin: 5px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>{{ config.name }}</h3>
        <p><strong>描述:</strong> {{ config.description }}</p>
        <p><strong>中心:</strong> {{ config.center[1] }}, {{ config.center[0] }}</p>
        <p><strong>半径:</strong> {{ config.radius_km }} 公里</p>
        <p><strong>生成时间:</strong> {{ config.generated_time }}</p>
        <div id="tile-status" class="status warning">正在检查瓦片状态...</div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 地图配置
        const config = {{ config | tojson }};
        
        // 初始化地图
        const map = L.map('map').setView([config.center[1], config.center[0]], config.zoom);
        
        // 添加离线瓦片图层
        const tileLayer = L.tileLayer('/tiles/{z}/{x}/{y}.png', {
            attribution: config.attribution,
            minZoom: config.minZoom,
            maxZoom: config.maxZoom,
            errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });
        
        tileLayer.addTo(map);
        
        // 瓦片加载状态监控
        let loadedTiles = 0;
        let failedTiles = 0;
        
        tileLayer.on('tileload', function() {
            loadedTiles++;
            updateTileStatus();
        });
        
        tileLayer.on('tileerror', function() {
            failedTiles++;
            updateTileStatus();
        });
        
        function updateTileStatus() {
            const statusDiv = document.getElementById('tile-status');
            const total = loadedTiles + failedTiles;
            
            if (total === 0) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '正在加载瓦片...';
            } else if (failedTiles === 0) {
                statusDiv.className = 'status success';
                statusDiv.textContent = `瓦片加载正常 (${loadedTiles} 个)`;
            } else if (loadedTiles > failedTiles) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = `部分瓦片缺失 (成功: ${loadedTiles}, 失败: ${failedTiles})`;
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = `瓦片加载失败 (成功: ${loadedTiles}, 失败: ${failedTiles})`;
            }
        }
        
        // 添加点击事件显示坐标
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(6);
            const lng = e.latlng.lng.toFixed(6);
            L.popup()
                .setLatLng(e.latlng)
                .setContent(`坐标: ${lat}, ${lng}`)
                .openOn(map);
        });
        
        // 添加比例尺
        L.control.scale({
            metric: true,
            imperial: false
        }).addTo(map);
    </script>
</body>
</html>
    """
    
    return render_template_string(html_template, config=config)

@app.route('/tiles/<int:z>/<int:x>/<int:y>.png')
def serve_tile(z, x, y):
    """提供地图瓦片"""
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.png"
    
    if tile_path.exists():
        logger.debug(f"提供瓦片: {z}/{x}/{y}")
        return send_file(tile_path, mimetype='image/png')
    else:
        logger.warning(f"瓦片不存在: {z}/{x}/{y}")
        # 返回404或透明图片
        return "Tile not found", 404

@app.route('/tiles/<int:z>/<int:x>/<int:y>')
def serve_tile_no_ext(z, x, y):
    """提供地图瓦片 (无扩展名)"""
    return serve_tile(z, x, y)

@app.route('/api/config')
def api_config():
    """API: 获取地图配置"""
    config = load_config()
    if config:
        return jsonify(config)
    else:
        return jsonify({"error": "配置文件加载失败"}), 500

@app.route('/api/tiles/status')
def api_tiles_status():
    """API: 获取瓦片状态"""
    if not TILES_DIR.exists():
        return jsonify({
            "status": "error",
            "message": "瓦片目录不存在",
            "tile_count": 0
        })
    
    # 统计瓦片数量
    tile_count = 0
    zoom_levels = []
    
    for zoom_dir in TILES_DIR.iterdir():
        if zoom_dir.is_dir() and zoom_dir.name.isdigit():
            zoom = int(zoom_dir.name)
            zoom_tiles = 0
            
            for x_dir in zoom_dir.iterdir():
                if x_dir.is_dir() and x_dir.name.isdigit():
                    zoom_tiles += len([f for f in x_dir.iterdir() if f.suffix == '.png'])
            
            if zoom_tiles > 0:
                zoom_levels.append({"zoom": zoom, "tiles": zoom_tiles})
                tile_count += zoom_tiles
    
    return jsonify({
        "status": "success" if tile_count > 0 else "warning",
        "message": f"找到 {tile_count} 个瓦片" if tile_count > 0 else "没有找到瓦片",
        "tile_count": tile_count,
        "zoom_levels": zoom_levels
    })

if __name__ == '__main__':
    print("🚀 启动改进的瓦片服务器...")
    print(f"📁 瓦片目录: {TILES_DIR}")
    print(f"⚙️  配置文件: {CONFIG_FILE}")
    
    # 检查瓦片目录
    if not TILES_DIR.exists():
        print("⚠️  瓦片目录不存在，请先运行 fix_offline_map.py")
    
    print("🌐 服务器地址: http://localhost:5000")
    print("📊 瓦片状态API: http://localhost:5000/api/tiles/status")
    
    app.run(host='0.0.0.0', port=5000, debug=True)