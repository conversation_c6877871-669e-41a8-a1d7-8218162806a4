#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPS数据模拟器
模拟PLC设备发送GPS和传感器数据
"""

import requests
import time
import random
import json
from datetime import datetime

class GPSDataSimulator:
    def __init__(self, base_url='http://127.0.0.1:5000'):
        self.base_url = base_url
        self.wells = [
            {
                'id': 'WELL001',
                'name': '机井001',
                'lat': 39.9042,
                'lon': 116.4074,
                'base_lat': 39.9042,
                'base_lon': 116.4074,
                'status': 'active',
                'gas_level': 25,
                'fan_status': 'on'
            },
            {
                'id': 'WELL002',
                'name': '机井002',
                'lat': 31.2304,
                'lon': 121.4737,
                'base_lat': 31.2304,
                'base_lon': 121.4737,
                'status': 'active',
                'gas_level': 45,
                'fan_status': 'on'
            },
            {
                'id': 'WELL003',
                'name': '机井003',
                'lat': 22.3193,
                'lon': 114.1694,
                'base_lat': 22.3193,
                'base_lon': 114.1694,
                'status': 'warning',
                'gas_level': 75,
                'fan_status': 'on'
            }
        ]
    
    def simulate_gps_drift(self, base_lat, base_lon, drift_range=0.001):
        """模拟GPS漂移"""
        lat_drift = random.uniform(-drift_range, drift_range)
        lon_drift = random.uniform(-drift_range, drift_range)
        return base_lat + lat_drift, base_lon + lon_drift
    
    def simulate_gas_level(self, base_level, variation=10):
        """模拟气体浓度变化"""
        change = random.uniform(-variation, variation)
        new_level = base_level + change
        return max(0, min(100, new_level))  # 限制在0-100之间
    
    def simulate_fan_status(self, current_status, gas_level):
        """模拟送风机状态"""
        if gas_level > 60:
            return 'on'  # 高浓度时强制开启
        elif gas_level < 20:
            return 'off'  # 低浓度时关闭
        else:
            # 中等浓度时随机切换
            return 'on' if random.random() > 0.3 else 'off'
    
    def update_well_data(self, well):
        """更新机井数据"""
        # 模拟GPS漂移
        well['lat'], well['lon'] = self.simulate_gps_drift(
            well['base_lat'], well['base_lon']
        )
        
        # 模拟气体浓度变化
        well['gas_level'] = self.simulate_gas_level(well['gas_level'])
        
        # 模拟送风机状态
        well['fan_status'] = self.simulate_fan_status(
            well['fan_status'], well['gas_level']
        )
        
        # 更新状态
        if well['gas_level'] > 80:
            well['status'] = 'error'
        elif well['gas_level'] > 50:
            well['status'] = 'warning'
        else:
            well['status'] = 'active'
        
        return well
    
    def send_well_data(self, well):
        """发送机井数据到服务器"""
        try:
            # 更新GPS坐标
            gps_response = requests.post(
                f"{self.base_url}/api/wells/{well['id']}/gps",
                json={
                    'lat': well['lat'],
                    'lon': well['lon']
                },
                timeout=5
            )
            
            if gps_response.status_code == 200:
                print(f"✅ {well['name']} GPS更新成功: {well['lat']:.6f}, {well['lon']:.6f}")
            else:
                print(f"❌ {well['name']} GPS更新失败: {gps_response.status_code}")
            
            # 更新机井状态
            update_response = requests.put(
                f"{self.base_url}/api/wells/{well['id']}",
                json={
                    'status': well['status'],
                    'gas_level': well['gas_level'],
                    'fan_status': well['fan_status']
                },
                timeout=5
            )
            
            if update_response.status_code == 200:
                print(f"✅ {well['name']} 状态更新成功: 气体{well['gas_level']:.1f}% 送风机{well['fan_status']}")
            else:
                print(f"❌ {well['name']} 状态更新失败: {update_response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {well['name']} 数据发送失败: {e}")
    
    def run_simulation(self, duration=300, interval=10):
        """运行模拟"""
        print("🔧 GPS数据模拟器启动")
        print("=" * 50)
        print(f"📡 模拟 {len(self.wells)} 个机井设备")
        print(f"⏱️  更新间隔: {interval}秒")
        print(f"🕐 运行时长: {duration}秒")
        print("=" * 50)
        
        start_time = time.time()
        update_count = 0
        
        try:
            while time.time() - start_time < duration:
                print(f"\n🔄 第 {update_count + 1} 次数据更新 - {datetime.now().strftime('%H:%M:%S')}")
                
                for well in self.wells:
                    # 更新数据
                    self.update_well_data(well)
                    
                    # 发送数据
                    self.send_well_data(well)
                    
                    # 短暂延迟避免请求过快
                    time.sleep(0.5)
                
                update_count += 1
                print(f"⏳ 等待 {interval} 秒后进行下次更新...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 模拟器被用户中断")
        
        print(f"\n📊 模拟完成:")
        print(f"  - 总更新次数: {update_count}")
        print(f"  - 运行时长: {time.time() - start_time:.1f}秒")
        print(f"  - 平均更新间隔: {(time.time() - start_time) / update_count:.1f}秒")

def main():
    """主函数"""
    simulator = GPSDataSimulator()
    
    print("🎯 GPS数据模拟器")
    print("=" * 40)
    print("1. 快速模拟 (5分钟)")
    print("2. 标准模拟 (30分钟)")
    print("3. 长时间模拟 (2小时)")
    print("4. 自定义模拟")
    print("=" * 40)
    
    try:
        choice = input("请选择模拟模式 (1-4): ").strip()
        
        if choice == '1':
            simulator.run_simulation(duration=300, interval=10)
        elif choice == '2':
            simulator.run_simulation(duration=1800, interval=30)
        elif choice == '3':
            simulator.run_simulation(duration=7200, interval=60)
        elif choice == '4':
            duration = int(input("请输入运行时长(秒): "))
            interval = int(input("请输入更新间隔(秒): "))
            simulator.run_simulation(duration=duration, interval=interval)
        else:
            print("❌ 无效选择，使用默认模式")
            simulator.run_simulation(duration=300, interval=10)
            
    except KeyboardInterrupt:
        print("\n👋 程序退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
