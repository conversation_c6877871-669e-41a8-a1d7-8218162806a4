@echo off
chcp 65001 >nul
title 野外深机井监控系统

echo.
echo ========================================
echo    野外深机井监控系统启动器
echo ========================================
echo.

:menu
echo 请选择启动模式:
echo.
echo [1] 正常模式 - 连接实际设备
echo [2] 测试模式 - 使用模拟数据  
echo [3] 演示模式 - 功能演示
echo [4] 系统信息 - 查看系统状态
echo [5] 安装依赖 - 安装Python包
echo [0] 退出
echo.

set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto normal
if "%choice%"=="2" goto test  
if "%choice%"=="3" goto demo
if "%choice%"=="4" goto info
if "%choice%"=="5" goto install
if "%choice%"=="0" goto exit
goto menu

:normal
echo.
echo 启动正常模式...
python start_system.py normal
pause
goto menu

:test
echo.
echo 启动测试模式...
python start_system.py test
pause
goto menu

:demo
echo.
echo 运行演示模式...
python start_system.py demo
pause
goto menu

:info
echo.
echo 显示系统信息...
python start_system.py info
pause
goto menu

:install
echo.
echo 安装Python依赖包...
pip install -r requirements.txt
echo.
echo 安装完成!
pause
goto menu

:exit
echo.
echo 感谢使用野外深机井监控系统!
pause
exit