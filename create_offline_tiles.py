#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线瓦片创建工具
直接创建基础瓦片，无需网络连接
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import json

def create_basic_tiles():
    """创建基础的离线瓦片"""
    print("🗺️  创建基础离线瓦片 (无需网络)")
    print("=" * 40)
    
    base_dir = Path(__file__).parent
    tiles_dir = base_dir / "static" / "tiles"
    
    # 创建基础瓦片结构
    zoom_levels = [8, 10, 12, 15]
    
    # 北京地区的瓦片坐标范围
    beijing_tiles = {
        8: {"x_range": (209, 211), "y_range": (96, 98)},
        10: {"x_range": (838, 842), "y_range": (387, 390)},
        12: {"x_range": (3352, 3358), "y_range": (1548, 1554)},
        15: {"x_range": (26819, 26830), "y_range": (12392, 12403)}
    }
    
    total_created = 0
    
    for zoom in zoom_levels:
        print(f"\n📍 创建缩放级别 {zoom} 的瓦片...")
        
        if zoom in beijing_tiles:
            x_min, x_max = beijing_tiles[zoom]["x_range"]
            y_min, y_max = beijing_tiles[zoom]["y_range"]
            
            for x in range(x_min, x_max + 1):
                for y in range(y_min, y_max + 1):
                    tile_created = create_single_tile(tiles_dir, zoom, x, y)
                    if tile_created:
                        total_created += 1
    
    print(f"\n✅ 总共创建了 {total_created} 个基础瓦片")
    return total_created > 0

def create_single_tile(tiles_dir, z, x, y):
    """创建单个瓦片"""
    tile_dir = tiles_dir / str(z) / str(x)
    tile_dir.mkdir(parents=True, exist_ok=True)
    tile_file = tile_dir / f"{y}.png"
    
    # 如果文件已存在，跳过
    if tile_file.exists():
        return True
    
    try:
        # 创建256x256的瓦片图像
        img = Image.new('RGB', (256, 256), color='#f0f8ff')  # 浅蓝色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制网格线
        for i in range(0, 256, 32):
            draw.line([(i, 0), (i, 256)], fill='#e0e0e0', width=1)
            draw.line([(0, i), (256, i)], fill='#e0e0e0', width=1)
        
        # 绘制瓦片信息
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            # 如果没有字体文件，使用默认字体
            font = ImageFont.load_default()
        
        # 添加瓦片坐标信息
        text = f"Z:{z} X:{x} Y:{y}"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = (256 - text_width) // 2
        text_y = (256 - text_height) // 2
        
        # 绘制文本背景
        draw.rectangle([text_x-5, text_y-5, text_x+text_width+5, text_y+text_height+5], 
                      fill='white', outline='black')
        
        # 绘制文本
        draw.text((text_x, text_y), text, fill='black', font=font)
        
        # 添加一些地理特征模拟
        if z >= 10:
            # 模拟道路
            draw.line([(50, 50), (200, 200)], fill='#ffff00', width=3)
            draw.line([(200, 50), (50, 200)], fill='#ffff00', width=2)
        
        if z >= 12:
            # 模拟建筑物
            draw.rectangle([80, 80, 120, 120], fill='#d3d3d3', outline='black')
            draw.rectangle([140, 140, 180, 180], fill='#d3d3d3', outline='black')
        
        if z >= 15:
            # 模拟详细特征
            draw.ellipse([100, 100, 130, 130], fill='#90ee90', outline='green')  # 绿地
            draw.rectangle([160, 100, 190, 130], fill='#87ceeb', outline='blue')  # 水体
        
        # 保存瓦片
        img.save(tile_file, 'PNG')
        print(f"  ✅ 创建瓦片: {z}/{x}/{y}.png")
        return True
        
    except Exception as e:
        print(f"  ❌ 创建失败: {z}/{x}/{y}.png - {e}")
        return False

def create_map_config():
    """创建地图配置"""
    print("\n🔧 创建地图配置...")
    
    config = {
        "name": "野外深机井监控地图",
        "description": "基础离线地图瓦片",
        "center": [116.4074, 39.9042],
        "zoom": 12,
        "minZoom": 8,
        "maxZoom": 15,
        "attribution": "基础离线地图瓦片"
    }
    
    config_file = Path(__file__).parent / "map_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")

def get_tile_stats():
    """获取瓦片统计"""
    tiles_dir = Path(__file__).parent / "static" / "tiles"
    
    if not tiles_dir.exists():
        return {"tiles": 0, "size_mb": 0, "zoom_levels": []}
    
    total_tiles = 0
    total_size = 0
    zoom_levels = set()
    
    for tile_file in tiles_dir.rglob("*.png"):
        total_tiles += 1
        total_size += tile_file.stat().st_size
        
        # 提取缩放级别
        parts = tile_file.parts
        if len(parts) >= 3:
            try:
                zoom = int(parts[-3])
                zoom_levels.add(zoom)
            except ValueError:
                pass
    
    return {
        "tiles": total_tiles,
        "size_mb": round(total_size / 1024 / 1024, 2),
        "zoom_levels": sorted(list(zoom_levels))
    }

def main():
    print("🗺️  离线瓦片创建工具")
    print("=" * 40)
    
    # 检查当前状态
    stats = get_tile_stats()
    print(f"📊 当前瓦片: {stats['tiles']} 个")
    print(f"📊 占用空间: {stats['size_mb']} MB")
    print(f"📊 缩放级别: {stats['zoom_levels']}")
    
    if stats['tiles'] > 0:
        choice = input("\n检测到已有瓦片，是否重新创建? (y/N): ").lower()
        if choice != 'y':
            print("保持现有瓦片")
            return True
    
    try:
        # 安装PIL依赖
        try:
            from PIL import Image, ImageDraw, ImageFont
        except ImportError:
            print("📦 安装PIL依赖...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
            from PIL import Image, ImageDraw, ImageFont
        
        # 创建瓦片
        if create_basic_tiles():
            create_map_config()
            
            # 显示最终统计
            final_stats = get_tile_stats()
            print(f"\n📊 创建完成:")
            print(f"   瓦片数量: {final_stats['tiles']}")
            print(f"   占用空间: {final_stats['size_mb']} MB")
            print(f"   缩放级别: {final_stats['zoom_levels']}")
            
            print(f"\n🚀 下一步:")
            print(f"   1. 测试地图: python start_system.py map")
            print(f"   2. 启动系统: python start_system.py test")
            print(f"   3. 访问界面: http://localhost:5000")
            
            return True
        else:
            print("❌ 瓦片创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)