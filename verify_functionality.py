#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证功能是否正常
"""

import requests
import webbrowser
import time

def verify_functionality():
    """验证功能是否正常"""
    print("🔍 验证功能是否正常")
    print("=" * 50)
    
    # 检查主系统
    print("1. 检查主系统...")
    try:
        response = requests.get("http://127.0.0.1:5000/api/statistics", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 主系统正常: {stats['total_regions']} 区域, {stats['famous_places_count']} 地标")
        else:
            print(f"   ❌ 主系统异常: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 主系统连接失败: {e}")
        return
    
    # 测试搜索功能
    print("\n2. 测试搜索功能...")
    test_queries = ["四川", "九寨沟", "北京", "天安门"]
    
    for query in test_queries:
        try:
            response = requests.get(f"http://127.0.0.1:5000/api/search-poi?q={query}")
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ '{query}': {len(results)} 个结果")
            else:
                print(f"   ❌ '{query}': HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ '{query}': {e}")
    
    # 检查测试服务器
    print("\n3. 检查测试服务器...")
    try:
        response = requests.get("http://localhost:8081/test_complete_functionality.html", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ 测试服务器正常")
        else:
            print(f"   ❌ 测试服务器异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ❌ 测试服务器连接失败: {e}")
    
    # 提供访问链接
    print(f"\n🌐 访问链接:")
    print(f"   🏠 主系统: http://127.0.0.1:5000")
    print(f"   🧪 测试页面: http://localhost:8081/test_complete_functionality.html")
    
    # 自动打开测试页面
    print(f"\n🚀 自动打开测试页面...")
    try:
        webbrowser.open('http://localhost:8081/test_complete_functionality.html')
        print(f"   ✅ 已自动打开测试页面")
    except Exception as e:
        print(f"   ❌ 无法自动打开浏览器: {e}")
    
    print(f"\n📋 使用说明:")
    print(f"   1. 在测试页面的搜索框输入 '四川'")
    print(f"   2. 点击搜索结果中的 '四川 (区域)'")
    print(f"   3. 观察地图是否聚焦到四川区域")
    print(f"   4. 检查是否显示脉冲标记和圆形高亮")
    print(f"   5. 尝试点击区域选择器中的其他省份")
    
    print(f"\n🎯 预期效果:")
    print(f"   - 搜索 '四川' 应该找到 9 个结果")
    print(f"   - 点击搜索结果应该聚焦到四川区域")
    print(f"   - 显示脉冲动画标记")
    print(f"   - 显示圆形高亮区域")
    print(f"   - 弹出详细信息窗口")

if __name__ == "__main__":
    verify_functionality()
