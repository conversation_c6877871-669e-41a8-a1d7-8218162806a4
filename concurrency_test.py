#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发性能测试工具
测试不同并发数下的下载性能
"""

import time
import psutil
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class ConcurrencyTester:
    def __init__(self):
        self.test_url = "https://overpass-api.de/api/interpreter"
        self.test_query = """
        [out:xml][timeout:60];
        (
          way["highway"="primary"](39.9,116.4,40.0,116.5);
        );
        out geom;
        """
        self.results = []
        self.lock = threading.Lock()
    
    def single_download_test(self):
        """单线程下载测试"""
        print("🔍 单线程下载测试...")
        
        start_time = time.time()
        try:
            response = requests.post(
                self.test_url,
                data={"data": self.test_query},
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                duration = end_time - start_time
                size = len(response.content)
                print(f"✅ 单线程下载成功: {duration:.2f}秒, {size}字节")
                return {"success": True, "duration": duration, "size": size}
            else:
                print(f"❌ 单线程下载失败: HTTP {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ 单线程下载异常: {e}")
            return {"success": False, "error": str(e), "duration": duration}
    
    def concurrent_download_test(self, max_workers):
        """并发下载测试"""
        print(f"🔍 {max_workers}线程并发下载测试...")
        
        def download_worker():
            start_time = time.time()
            try:
                response = requests.post(
                    self.test_url,
                    data={"data": self.test_query},
                    timeout=60
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    duration = end_time - start_time
                    size = len(response.content)
                    return {"success": True, "duration": duration, "size": size}
                else:
                    return {"success": False, "error": f"HTTP {response.status_code}"}
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                return {"success": False, "error": str(e), "duration": duration}
        
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(download_worker) for _ in range(max_workers)]
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 统计结果
        successful = sum(1 for r in results if r.get("success", False))
        failed = len(results) - successful
        
        if successful > 0:
            avg_duration = sum(r.get("duration", 0) for r in results if r.get("success", False)) / successful
            total_size = sum(r.get("size", 0) for r in results if r.get("success", False))
            throughput = total_size / total_duration if total_duration > 0 else 0
            
            print(f"✅ {max_workers}线程测试完成:")
            print(f"   总耗时: {total_duration:.2f}秒")
            print(f"   成功: {successful}/{max_workers}")
            print(f"   失败: {failed}")
            print(f"   平均耗时: {avg_duration:.2f}秒")
            print(f"   总数据量: {total_size}字节")
            print(f"   吞吐量: {throughput:.2f}字节/秒")
            
            return {
                "workers": max_workers,
                "total_duration": total_duration,
                "successful": successful,
                "failed": failed,
                "avg_duration": avg_duration,
                "total_size": total_size,
                "throughput": throughput
            }
        else:
            print(f"❌ {max_workers}线程测试全部失败")
            return {
                "workers": max_workers,
                "total_duration": total_duration,
                "successful": 0,
                "failed": max_workers,
                "avg_duration": 0,
                "total_size": 0,
                "throughput": 0
            }
    
    def system_info(self):
        """获取系统信息"""
        print("💻 系统信息:")
        print(f"   CPU核心数: {psutil.cpu_count()}")
        print(f"   CPU使用率: {psutil.cpu_percent()}%")
        print(f"   内存总量: {psutil.virtual_memory().total / (1024**3):.1f}GB")
        print(f"   内存使用率: {psutil.virtual_memory().percent}%")
        print(f"   磁盘使用率: {psutil.disk_usage('/').percent}%")
    
    def run_performance_test(self):
        """运行性能测试"""
        print("🚀 并发性能测试开始")
        print("=" * 50)
        
        # 显示系统信息
        self.system_info()
        print()
        
        # 测试不同并发数
        test_workers = [1, 2, 4, 8, 12, 16]
        results = []
        
        for workers in test_workers:
            print(f"\n📊 测试 {workers} 个并发线程...")
            
            if workers == 1:
                result = self.single_download_test()
                if result.get("success"):
                    results.append({
                        "workers": 1,
                        "total_duration": result["duration"],
                        "successful": 1,
                        "failed": 0,
                        "avg_duration": result["duration"],
                        "total_size": result["size"],
                        "throughput": result["size"] / result["duration"] if result["duration"] > 0 else 0
                    })
            else:
                result = self.concurrent_download_test(workers)
                results.append(result)
            
            # 等待一下，避免请求过快
            time.sleep(2)
        
        # 显示测试结果汇总
        self.show_results_summary(results)
        
        # 推荐最优并发数
        self.recommend_optimal_concurrency(results)
    
    def show_results_summary(self, results):
        """显示测试结果汇总"""
        print("\n📊 测试结果汇总:")
        print("=" * 80)
        print(f"{'并发数':<8} {'总耗时':<10} {'成功率':<10} {'平均耗时':<10} {'吞吐量':<15}")
        print("-" * 80)
        
        for result in results:
            success_rate = (result["successful"] / result["workers"]) * 100 if result["workers"] > 0 else 0
            throughput_mb = result["throughput"] / (1024 * 1024) if result["throughput"] > 0 else 0
            
            print(f"{result['workers']:<8} "
                  f"{result['total_duration']:<10.2f} "
                  f"{success_rate:<10.1f}% "
                  f"{result['avg_duration']:<10.2f} "
                  f"{throughput_mb:<15.2f}MB/s")
    
    def recommend_optimal_concurrency(self, results):
        """推荐最优并发数"""
        print("\n🎯 并发数推荐:")
        
        # 找到成功率最高的配置
        best_success = max(results, key=lambda x: x["successful"] / x["workers"] if x["workers"] > 0 else 0)
        
        # 找到吞吐量最高的配置
        best_throughput = max(results, key=lambda x: x["throughput"])
        
        # 找到平衡点（成功率>80%且吞吐量较高）
        balanced_options = [r for r in results if (r["successful"] / r["workers"]) >= 0.8 if r["workers"] > 0 else False]
        if balanced_options:
            best_balanced = max(balanced_options, key=lambda x: x["throughput"])
        else:
            best_balanced = best_success
        
        print(f"✅ 最高成功率: {best_success['workers']}线程 "
              f"({(best_success['successful']/best_success['workers']*100):.1f}%)")
        print(f"⚡ 最高吞吐量: {best_throughput['workers']}线程 "
              f"({best_throughput['throughput']/(1024*1024):.2f}MB/s)")
        print(f"🎯 推荐配置: {best_balanced['workers']}线程 "
              f"(平衡性能和稳定性)")
        
        # 根据系统配置给出建议
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        print(f"\n💡 系统建议:")
        if memory_gb >= 16 and cpu_count >= 8:
            print(f"   高性能系统: 建议使用 8-16 个并发线程")
        elif memory_gb >= 8 and cpu_count >= 4:
            print(f"   中等性能系统: 建议使用 4-8 个并发线程")
        else:
            print(f"   基础性能系统: 建议使用 2-4 个并发线程")

def main():
    """主函数"""
    tester = ConcurrencyTester()
    
    print("🔧 并发性能测试工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择测试类型:")
        print("1. 完整性能测试 (推荐)")
        print("2. 快速测试 (2, 4, 8线程)")
        print("3. 自定义测试")
        print("4. 系统信息")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            tester.run_performance_test()
        
        elif choice == "2":
            print("🚀 快速性能测试")
            test_workers = [2, 4, 8]
            results = []
            
            for workers in test_workers:
                result = tester.concurrent_download_test(workers)
                results.append(result)
                time.sleep(1)
            
            tester.show_results_summary(results)
            tester.recommend_optimal_concurrency(results)
        
        elif choice == "3":
            try:
                workers = int(input("请输入要测试的并发数: "))
                if workers > 0:
                    result = tester.concurrent_download_test(workers)
                    tester.show_results_summary([result])
                else:
                    print("❌ 并发数必须大于0")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == "4":
            tester.system_info()
        
        elif choice == "5":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
