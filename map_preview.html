<!DOCTYPE html>
<html>
<head>
    <title>离线地图预览</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <style>
        #map { height: 600px; width: 800px; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        const map = L.map('map').setView([30.17, 109.13], 12);
        <PERSON><PERSON>tileLayer('static/tiles/{z}/{x}/{y}.png', {
            attribution: '离线地图',
            maxZoom: 18,
            minZoom: 10
        }).addTo(map);
    </script>
</body>
</html>