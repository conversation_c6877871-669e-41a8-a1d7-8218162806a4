#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复地图显示问题
"""

import requests
import time

def fix_map_display():
    """修复地图显示问题"""
    print("🔧 修复地图显示问题")
    print("=" * 50)
    
    # 检查系统状态
    print("1. 检查GPS监控系统...")
    try:
        response = requests.get('http://127.0.0.1:5000/api/map-config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ GPS监控系统正常")
            print(f"   📍 离线模式: {config['offline_mode']}")
            print(f"   🗺️  缩放级别: {config['min_zoom']}-{config['max_zoom']}")
            print(f"   🌐 瓦片服务器: {config['tile_server_url']}")
        else:
            print(f"   ❌ GPS监控系统异常: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ GPS监控系统连接失败: {e}")
        return
    
    # 检查瓦片服务器
    print("2. 检查瓦片服务器...")
    try:
        response = requests.get('http://127.0.0.1:8080/config', timeout=5)
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ 瓦片服务器正常")
            print(f"   📁 瓦片目录: {config['tiles_dir']}")
            print(f"   🗺️  缩放级别: {config['min_zoom']}-{config['max_zoom']}")
        else:
            print(f"   ❌ 瓦片服务器异常: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 瓦片服务器连接失败: {e}")
        return
    
    # 测试瓦片访问
    print("3. 测试瓦片访问...")
    test_tiles = [
        "http://127.0.0.1:8080/tiles/18/215828/99324.png",
        "http://127.0.0.1:8080/tiles/18/215829/99325.png"
    ]
    
    for tile_url in test_tiles:
        try:
            response = requests.head(tile_url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 瓦片访问正常: {tile_url}")
            else:
                print(f"   ❌ 瓦片访问失败: {tile_url} (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ 瓦片访问错误: {tile_url} - {e}")
    
    print("\n🎯 修复建议:")
    print("=" * 50)
    print("1. 确保浏览器访问: http://127.0.0.1:5000")
    print("2. 检查浏览器控制台是否有JavaScript错误")
    print("3. 确认地图配置已更新为18级缩放")
    print("4. 如果地图仍不显示，请尝试:")
    print("   - 清除浏览器缓存")
    print("   - 刷新页面")
    print("   - 检查网络连接")
    print("5. 测试离线地图: http://127.0.0.1:8081/test_offline_map.html")
    
    print("\n📱 系统访问地址:")
    print("   - GPS监控系统: http://127.0.0.1:5000")
    print("   - 瓦片服务器: http://127.0.0.1:8080")
    print("   - 地图测试页: http://127.0.0.1:8081/test_offline_map.html")

if __name__ == "__main__":
    fix_map_display()
