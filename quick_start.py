#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动矢量地图系统
"""

import os
import sys
from pathlib import Path

def check_data():
    """检查数据状态"""
    vector_dir = Path("static/vector_data")
    
    if not vector_dir.exists():
        print("❌ 矢量数据目录不存在")
        return False
    
    regions = [d for d in vector_dir.iterdir() if d.is_dir()]
    
    if not regions:
        print("❌ 没有找到矢量数据")
        return False
    
    print(f"✅ 找到 {len(regions)} 个区域的矢量数据:")
    for region in regions:
        files = list(region.glob("*.xml"))
        print(f"   - {region.name}: {len(files)} 个文件")
    
    return True

def main():
    """主函数"""
    print("🗺️  矢量地图系统快速启动")
    print("=" * 50)
    
    # 检查数据状态
    if check_data():
        print("\n🚀 启动矢量地图查看器...")
        print("   访问地址: http://localhost:5000")
        print("   按 Ctrl+C 停止服务")
        
        try:
            # 直接启动地图查看器
            import vector_map_viewer
            vector_map_viewer.app.run(debug=False, host='0.0.0.0', port=5000)
        except KeyboardInterrupt:
            print("\n👋 服务已停止")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    else:
        print("\n📥 请先下载矢量数据:")
        print("   python simple_vector_downloader.py")

if __name__ == "__main__":
    main()
