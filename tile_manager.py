#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瓦片管理器
监控、管理和优化瓦片生成过程
"""

import os
import sys
import json
import time
from pathlib import Path
from flask import Flask, render_template, jsonify, request

class TileManager:
    """瓦片管理器"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.base_dir = Path(__file__).parent
        
        # 瓦片目录
        self.tile_dirs = {
            "fast_precision": self.base_dir / "static" / "fast_precision_tiles",
            "optimized": self.base_dir / "static" / "optimized_tiles",
            "precision": self.base_dir / "static" / "precision_tiles"
        }
        
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/')
        def index():
            return render_template('tile_manager.html')
        
        @self.app.route('/api/tile-stats')
        def get_tile_stats():
            """获取瓦片统计信息"""
            stats = {}
            
            for name, tile_dir in self.tile_dirs.items():
                stats[name] = self.analyze_tile_directory(tile_dir)
            
            return jsonify(stats)
        
        @self.app.route('/api/tile-coverage/<system>')
        def get_tile_coverage(system):
            """获取瓦片覆盖情况"""
            if system not in self.tile_dirs:
                return jsonify({'error': '系统不存在'}), 404
            
            coverage = self.analyze_tile_coverage(self.tile_dirs[system])
            return jsonify(coverage)
        
        @self.app.route('/api/system-status')
        def get_system_status():
            """获取系统状态"""
            status = {
                'systems': [],
                'osm_file': self.check_osm_file(),
                'timestamp': time.time()
            }
            
            # 检查各个系统
            systems_to_check = [
                {'name': '混合精度系统', 'port': 5005, 'url': 'http://127.0.0.1:5005'},
                {'name': '快速精度系统', 'port': 5004, 'url': 'http://127.0.0.1:5004'},
                {'name': '真正精度系统', 'port': 5003, 'url': 'http://127.0.0.1:5003'},
                {'name': '超高精度系统', 'port': 5002, 'url': 'http://127.0.0.1:5002'}
            ]
            
            for system in systems_to_check:
                system['status'] = self.check_system_status(system['port'])
                status['systems'].append(system)
            
            return jsonify(status)
    
    def analyze_tile_directory(self, tile_dir):
        """分析瓦片目录"""
        if not tile_dir.exists():
            return {
                'exists': False,
                'total_tiles': 0,
                'total_size_mb': 0,
                'zoom_levels': [],
                'coverage': {}
            }
        
        total_tiles = 0
        total_size = 0
        zoom_levels = set()
        coverage = {}
        
        try:
            for tile_file in tile_dir.rglob("*.png"):
                total_tiles += 1
                total_size += tile_file.stat().st_size
                
                # 解析路径获取缩放级别
                parts = tile_file.parts
                if len(parts) >= 3:
                    try:
                        zoom = int(parts[-3])
                        zoom_levels.add(zoom)
                        
                        if zoom not in coverage:
                            coverage[zoom] = 0
                        coverage[zoom] += 1
                    except ValueError:
                        pass
        
        except Exception as e:
            print(f"分析瓦片目录失败: {e}")
        
        return {
            'exists': True,
            'total_tiles': total_tiles,
            'total_size_mb': round(total_size / 1024 / 1024, 2),
            'zoom_levels': sorted(list(zoom_levels)),
            'coverage': coverage
        }
    
    def analyze_tile_coverage(self, tile_dir):
        """分析瓦片覆盖情况"""
        coverage = {
            'zoom_levels': {},
            'geographic_coverage': [],
            'missing_areas': []
        }
        
        if not tile_dir.exists():
            return coverage
        
        try:
            # 按缩放级别分析
            for zoom_dir in tile_dir.iterdir():
                if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                    zoom = int(zoom_dir.name)
                    
                    x_dirs = []
                    total_tiles = 0
                    
                    for x_dir in zoom_dir.iterdir():
                        if x_dir.is_dir() and x_dir.name.isdigit():
                            x = int(x_dir.name)
                            x_dirs.append(x)
                            
                            y_files = [f for f in x_dir.iterdir() if f.suffix == '.png']
                            total_tiles += len(y_files)
                    
                    if x_dirs:
                        coverage['zoom_levels'][zoom] = {
                            'total_tiles': total_tiles,
                            'x_range': [min(x_dirs), max(x_dirs)],
                            'coverage_area': self.calculate_coverage_area(zoom, x_dirs)
                        }
        
        except Exception as e:
            print(f"分析瓦片覆盖失败: {e}")
        
        return coverage
    
    def calculate_coverage_area(self, zoom, x_dirs):
        """计算覆盖面积"""
        if not x_dirs:
            return "未知"
        
        # 简化计算：基于X坐标范围估算
        x_range = max(x_dirs) - min(x_dirs) + 1
        
        # 每个瓦片在不同缩放级别的地面距离（公里）
        tile_size_km = 40075 / (2 ** zoom)  # 地球周长除以瓦片数
        
        estimated_width = x_range * tile_size_km
        
        if estimated_width < 1:
            return f"{estimated_width*1000:.0f}米"
        elif estimated_width < 100:
            return f"{estimated_width:.1f}公里"
        else:
            return f"{estimated_width:.0f}公里"
    
    def check_osm_file(self):
        """检查OSM文件状态"""
        osm_file = self.base_dir / "china-latest.osm.pbf"
        
        if osm_file.exists():
            size_gb = osm_file.stat().st_size / (1024**3)
            return {
                'exists': True,
                'size_gb': round(size_gb, 2),
                'path': str(osm_file)
            }
        else:
            return {
                'exists': False,
                'size_gb': 0,
                'path': str(osm_file)
            }
    
    def check_system_status(self, port):
        """检查系统状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def run(self, host='127.0.0.1', port=5006, debug=False):
        """运行瓦片管理器"""
        print("🎛️ 瓦片管理器")
        print("=" * 50)
        print(f"🌐 管理界面: http://{host}:{port}")
        print("📊 功能:")
        print("  - 瓦片统计分析")
        print("  - 系统状态监控")
        print("  - 覆盖范围分析")
        print("  - 性能监控")
        print("=" * 50)
        
        self.app.run(host=host, port=port, debug=debug)

# 创建管理界面模板
def create_tile_manager_template():
    """创建瓦片管理器模板"""
    template_dir = Path(__file__).parent / "templates"
    template_dir.mkdir(exist_ok=True)
    
    template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瓦片管理器</title>
    <style>
        body { margin: 0; padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        .header { background: #333; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { margin: 0 0 15px 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .stat { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .stat-label { font-weight: bold; }
        .stat-value { color: #007bff; }
        .system-status { display: flex; align-items: center; margin-bottom: 10px; }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; margin-right: 10px; }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #007bff; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎛️ 瓦片管理器</h1>
        <p>20米精度离线地图系统 - 瓦片监控与管理</p>
    </div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 瓦片统计</h3>
            <div id="tile-stats">加载中...</div>
            <button class="btn" onclick="refreshStats()">刷新统计</button>
        </div>
        
        <div class="card">
            <h3>🌐 系统状态</h3>
            <div id="system-status">检查中...</div>
            <button class="btn" onclick="refreshStatus()">刷新状态</button>
        </div>
        
        <div class="card">
            <h3>📁 OSM文件状态</h3>
            <div id="osm-status">检查中...</div>
        </div>
        
        <div class="card">
            <h3>🗺️ 覆盖范围</h3>
            <div id="coverage-info">分析中...</div>
            <button class="btn" onclick="analyzeCoverage()">分析覆盖</button>
        </div>
    </div>

    <script>
        function refreshStats() {
            document.getElementById('tile-stats').innerHTML = '加载中...';
            fetch('/api/tile-stats')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    for (const [system, stats] of Object.entries(data)) {
                        html += `<div style="margin-bottom: 15px; border-left: 3px solid #007bff; padding-left: 10px;">`;
                        html += `<strong>${system}</strong><br>`;
                        if (stats.exists) {
                            html += `<div class="stat"><span class="stat-label">瓦片数量:</span><span class="stat-value">${stats.total_tiles}</span></div>`;
                            html += `<div class="stat"><span class="stat-label">占用空间:</span><span class="stat-value">${stats.total_size_mb} MB</span></div>`;
                            html += `<div class="stat"><span class="stat-label">缩放级别:</span><span class="stat-value">${stats.zoom_levels.join(', ')}</span></div>`;
                        } else {
                            html += `<div style="color: #dc3545;">目录不存在</div>`;
                        }
                        html += `</div>`;
                    }
                    document.getElementById('tile-stats').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('tile-stats').innerHTML = '加载失败: ' + error.message;
                });
        }
        
        function refreshStatus() {
            document.getElementById('system-status').innerHTML = '检查中...';
            fetch('/api/system-status')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    
                    // OSM文件状态
                    if (data.osm_file.exists) {
                        html += `<div class="system-status"><div class="status-indicator status-online"></div>OSM文件: ${data.osm_file.size_gb}GB</div>`;
                    } else {
                        html += `<div class="system-status"><div class="status-indicator status-offline"></div>OSM文件: 不存在</div>`;
                    }
                    
                    // 系统状态
                    data.systems.forEach(system => {
                        const statusClass = system.status ? 'status-online' : 'status-offline';
                        const statusText = system.status ? '运行中' : '离线';
                        html += `<div class="system-status">`;
                        html += `<div class="status-indicator ${statusClass}"></div>`;
                        html += `${system.name}: ${statusText}`;
                        if (system.status) {
                            html += ` <a href="${system.url}" target="_blank" style="margin-left: 10px; color: #007bff;">访问</a>`;
                        }
                        html += `</div>`;
                    });
                    
                    document.getElementById('system-status').innerHTML = html;
                    document.getElementById('osm-status').innerHTML = data.osm_file.exists ? 
                        `✅ 文件存在<br>📁 大小: ${data.osm_file.size_gb}GB<br>📍 路径: ${data.osm_file.path}` :
                        `❌ 文件不存在<br>📍 期望路径: ${data.osm_file.path}`;
                })
                .catch(error => {
                    document.getElementById('system-status').innerHTML = '检查失败: ' + error.message;
                });
        }
        
        function analyzeCoverage() {
            document.getElementById('coverage-info').innerHTML = '分析中...';
            // 这里可以添加覆盖范围分析的具体实现
            setTimeout(() => {
                document.getElementById('coverage-info').innerHTML = '覆盖范围分析功能开发中...';
            }, 1000);
        }
        
        // 自动刷新
        setInterval(() => {
            refreshStats();
            refreshStatus();
        }, 30000); // 30秒刷新一次
        
        // 初始加载
        refreshStats();
        refreshStatus();
    </script>
</body>
</html>'''
    
    template_file = template_dir / "tile_manager.html"
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(template_content)

if __name__ == "__main__":
    # 创建模板
    create_tile_manager_template()
    
    # 启动管理器
    manager = TileManager()
    manager.run()
