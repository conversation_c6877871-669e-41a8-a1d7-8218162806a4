#!/usr/bin/env python3
"""
简单修复重复瓦片问题
"""

import os
import math
from PIL import Image, ImageDraw, ImageFont
import random

def deg2num(lat_deg, lon_deg, zoom):
    """经纬度转瓦片坐标"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def create_unique_tile(x, y, zoom):
    """为特定坐标创建唯一的瓦片"""
    # 使用坐标作为随机种子
    random.seed(x * 1000000 + y)
    
    # 创建基础图像，使用坐标决定颜色
    colors = ['#e8f4f8', '#f0f8e8', '#f8f0e8', '#f8e8f0', '#e8e8f8']
    base_color = colors[(x + y) % len(colors)]
    
    img = Image.new('RGB', (256, 256), color=base_color)
    draw = ImageDraw.Draw(img)
    
    # 绘制网格
    for i in range(0, 256, 32):
        draw.line([(i, 0), (i, 256)], fill='#cccccc', width=1)
        draw.line([(0, i), (256, i)], fill='#cccccc', width=1)
    
    # 添加一些基于坐标的随机形状
    for i in range(random.randint(2, 5)):
        shape_type = random.choice(['circle', 'rect'])
        color = random.choice(['#4a90e2', '#7ed321', '#f5a623', '#d0021b'])
        
        if shape_type == 'circle':
            cx, cy = random.randint(30, 226), random.randint(30, 226)
            radius = random.randint(10, 25)
            draw.ellipse([cx-radius, cy-radius, cx+radius, cy+radius], 
                        fill=color, outline='black')
        else:
            x1, y1 = random.randint(20, 200), random.randint(20, 200)
            w, h = random.randint(20, 40), random.randint(20, 40)
            draw.rectangle([x1, y1, x1+w, y1+h], fill=color, outline='black')
    
    # 添加坐标信息
    try:
        font = ImageFont.load_default()
        draw.text((5, 5), f"X:{x} Y:{y}", fill='black', font=font)
        draw.text((5, 20), f"Z:{zoom}", fill='black', font=font)
        
        # 添加唯一ID
        unique_id = (x * 1000000 + y) % 999999
        draw.text((5, 235), f"ID:{unique_id:06d}", fill='red', font=font)
    except:
        draw.text((5, 5), f"{x},{y}", fill='black')
    
    return img

def main():
    """主函数"""
    print("🔧 修复20级瓦片重复问题")
    
    # 北京地区的瓦片坐标范围
    north = 40.1
    south = 39.8
    west = 116.2
    east = 116.6
    
    x_min, y_max = deg2num(north, west, 20)
    x_max, y_min = deg2num(south, east, 20)
    
    print(f"📍 目标范围: x:{x_min}-{x_max}, y:{y_min}-{y_max}")
    
    fixed_count = 0
    total_count = 0
    
    # 重新生成所有20级瓦片
    for x in range(x_min, x_max + 1):
        tile_dir = f"static/tiles/20/{x}"
        os.makedirs(tile_dir, exist_ok=True)
        
        for y in range(y_min, y_max + 1):
            tile_path = f"{tile_dir}/{y}.png"
            total_count += 1
            
            try:
                # 创建唯一的瓦片内容
                img = create_unique_tile(x, y, 20)
                img.save(tile_path, 'PNG')
                fixed_count += 1
                
                if fixed_count % 100 == 0:
                    print(f"✅ 已修复 {fixed_count}/{total_count} 个瓦片...")
                    
            except Exception as e:
                print(f"❌ 修复失败 {tile_path}: {e}")
    
    print(f"\n🎉 修复完成!")
    print(f"📊 总瓦片数: {total_count}")
    print(f"✅ 修复数量: {fixed_count}")
    print(f"💡 现在每个瓦片都有唯一的内容和标识符")

if __name__ == "__main__":
    main()
