<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整功能测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: Arial, sans-serif; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .search-box {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        .search-box input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .search-box button {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .search-box button:hover {
            background: #5a6fd8;
        }
        .results {
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }
        .result-item:hover {
            background: #f0f0f0;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-name {
            font-weight: bold;
            color: #333;
        }
        .result-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #map { 
            height: 400px; 
            width: 100%; 
            border: 1px solid #ccc; 
            border-radius: 8px;
            margin: 15px 0;
        }
        .region-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .region-item {
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
        }
        .region-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        .region-item.complete {
            background: #d4edda;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ 完整功能测试</h1>
            <p>测试搜索、聚焦和定位功能</p>
        </div>
        
        <div class="content">
            <!-- 系统状态 -->
            <div class="test-section">
                <h3>📊 系统状态</h3>
                <div id="systemStatus" class="status info">正在检查系统状态...</div>
            </div>
            
            <!-- 搜索功能测试 -->
            <div class="test-section">
                <h3>🔍 搜索功能测试</h3>
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="输入搜索关键词，如：四川、北京、天安门、九寨沟..." />
                    <button onclick="performSearch()">搜索</button>
                </div>
                <div id="searchResults" class="results"></div>
            </div>
            
            <!-- 区域选择测试 -->
            <div class="test-section">
                <h3>🗺️ 区域选择测试</h3>
                <p>点击下方区域按钮测试聚焦功能：</p>
                <div id="regionGrid" class="region-grid"></div>
            </div>
            
            <!-- 地图显示 -->
            <div class="test-section">
                <h3>🗺️ 地图显示</h3>
                <div id="map"></div>
                <div id="mapStatus" class="status info">地图已初始化</div>
            </div>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let regions = {};
        let famousPlaces = {};
        let currentMarkers = [];
        
        // 初始化地图
        function initMap() {
            map = L.map('map', {
                maxZoom: 18,
                minZoom: 3
            }).setView([35.0, 105.0], 5);
            
            // 添加底图
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);
            
            updateMapStatus('地图初始化完成');
        }
        
        // 更新地图状态
        function updateMapStatus(message, type = 'info') {
            const statusDiv = document.getElementById('mapStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('http://127.0.0.1:5000/api/statistics');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('systemStatus').innerHTML = `
                        ✅ 系统运行正常<br>
                        📊 区域数: ${stats.total_regions} | 著名地标: ${stats.famous_places_count} | 完成率: ${stats.completion_rate}%
                    `;
                    document.getElementById('systemStatus').className = 'status success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = `❌ 系统连接失败: ${error.message}`;
                document.getElementById('systemStatus').className = 'status error';
            }
        }
        
        // 加载区域数据
        async function loadRegions() {
            try {
                const response = await fetch('http://127.0.0.1:5000/api/regions');
                if (response.ok) {
                    regions = await response.json();
                    displayRegions();
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('加载区域数据失败:', error);
            }
        }
        
        // 显示区域
        function displayRegions() {
            const grid = document.getElementById('regionGrid');
            grid.innerHTML = '';
            
            for (const [regionName, region] of Object.entries(regions)) {
                const item = document.createElement('div');
                item.className = `region-item ${region.status}`;
                item.textContent = regionName;
                item.title = `${regionName} - ${region.status}`;
                item.onclick = () => focusOnRegion(regionName, region);
                grid.appendChild(item);
            }
        }
        
        // 聚焦到区域
        function focusOnRegion(regionName, region) {
            // 清除之前的标记
            currentMarkers.forEach(marker => map.removeLayer(marker));
            currentMarkers = [];
            
            // 平滑动画聚焦
            map.flyTo(region.center, 9, {
                animate: true,
                duration: 2.0,
                easeLinearity: 0.1
            });
            
            // 添加区域边界
            const bbox = region.bbox;
            const bounds = [[bbox[1], bbox[0]], [bbox[3], bbox[2]]];
            const rectangle = L.rectangle(bounds, {
                color: '#667eea',
                weight: 2,
                fillColor: '#667eea',
                fillOpacity: 0.1
            }).addTo(map);
            currentMarkers.push(rectangle);
            
            // 添加中心标记
            const marker = L.marker(region.center).addTo(map);
            marker.bindPopup(`<strong>${regionName}</strong><br>区域中心`).openPopup();
            currentMarkers.push(marker);
            
            updateMapStatus(`已聚焦到: ${regionName}`, 'success');
        }
        
        // 执行搜索
        async function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                alert('请输入搜索关键词');
                return;
            }
            
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div style="padding: 10px; text-align: center;">搜索中...</div>';
            
            try {
                const response = await fetch(`http://127.0.0.1:5000/api/search-poi?q=${encodeURIComponent(query)}`);
                if (response.ok) {
                    const results = await response.json();
                    displaySearchResults(results);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div style="padding: 10px; color: red;">搜索失败: ${error.message}</div>`;
            }
        }
        
        // 显示搜索结果
        function displaySearchResults(results) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div style="padding: 10px; text-align: center; color: #666;">未找到相关结果</div>';
                return;
            }
            
            resultsDiv.innerHTML = results.map(result => {
                let typeText = '';
                if (result.type === 'famous_place') {
                    typeText = '著名地标';
                } else if (result.type === 'region') {
                    typeText = '区域';
                } else if (result.type === 'poi') {
                    typeText = '兴趣点';
                }
                
                return `
                    <div class="result-item" onclick="focusOnResult('${result.name}', ${result.lat}, ${result.lon})">
                        <div class="result-name">${result.name}</div>
                        <div class="result-info">
                            ${result.region} - ${typeText}
                            ${result.amenity ? ` (${result.amenity})` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 聚焦到搜索结果
        function focusOnResult(name, lat, lon) {
            // 清除之前的标记
            currentMarkers.forEach(marker => map.removeLayer(marker));
            currentMarkers = [];
            
            // 平滑动画聚焦
            map.flyTo([lat, lon], 16, {
                animate: true,
                duration: 1.5,
                easeLinearity: 0.1
            });
            
            // 添加高亮标记
            const marker = L.marker([lat, lon], {
                icon: L.divIcon({
                    className: 'custom-marker',
                    html: '<div style="width: 20px; height: 20px; background: #ff6b6b; border: 3px solid #fff; border-radius: 50%; box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); animation: pulse 2s infinite;"></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                })
            }).addTo(map);
            
            // 添加圆形高亮区域
            const circle = L.circle([lat, lon], {
                color: '#ff6b6b',
                fillColor: '#ff6b6b',
                fillOpacity: 0.2,
                radius: 200
            }).addTo(map);
            
            // 绑定弹出窗口
            marker.bindPopup(`
                <div style="text-align: center; min-width: 200px;">
                    <h4 style="margin: 0 0 8px 0; color: #333;">📍 ${name}</h4>
                    <p style="margin: 0; color: #666; font-size: 12px;">坐标: ${lat.toFixed(6)}, ${lon.toFixed(6)}</p>
                </div>
            `).openPopup();
            
            currentMarkers = [marker, circle];
            updateMapStatus(`已定位到: ${name}`, 'success');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            checkSystemStatus();
            loadRegions();
            
            // 添加搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
    </script>
    
    <style>
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
            }
        }
    </style>
</body>
</html>
