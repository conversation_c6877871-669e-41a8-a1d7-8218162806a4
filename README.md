# 野外深机井监控系统

这是一个用于监控野外深机井现场的实时监控系统，支持GPS定位和PLC数据采集。

## 功能特性

- **实时GPS定位**: 接收NMEA格式的GPS数据并在离线地图上显示位置
- **PLC数据监控**: 通过MODBUS协议读取气体检测仪器和送风机状态
- **离线地图支持**: 使用OpenMapTiles实现内网环境下的地图显示
- **实时数据更新**: 通过WebSocket实现数据的实时推送和显示
- **响应式界面**: 支持多种设备屏幕尺寸

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GPS设备       │    │   PLC设备       │    │   网管路由器    │
│   (NMEA协议)    │────│   (MODBUS协议)  │────│   (4G网络)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        │
┌─────────────────────────────────────────────────────────────────┐
│                    监控中心服务器                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │ 数据接收模块 │  │ Web服务器   │  │    离线地图服务         │  │
│  │ (串口通信)  │  │ (Flask)     │  │  (OpenMapTiles)        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ WebSocket
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      Web监控界面                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────┐  │
│  │   GPS地图   │  │  气体监测   │  │      设备状态           │  │
│  │   显示区域  │  │   数据区    │  │      监控区             │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 安装和配置

### 1. 环境要求

- Python 3.7+
- Windows 10/11 (支持串口通信)
- 网络浏览器 (Chrome/Firefox/Edge)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置参数

编辑 `config.py` 文件，根据实际硬件配置修改以下参数：

```python
# GPS串口配置
GPS_PORT = 'COM3'          # GPS设备串口号
GPS_BAUDRATE = 9600        # GPS波特率

# MODBUS串口配置  
MODBUS_PORT = 'COM4'       # PLC设备串口号
MODBUS_BAUDRATE = 9600     # MODBUS波特率
MODBUS_SLAVE_ID = 1        # PLC从站ID

# MODBUS寄存器地址配置
MODBUS_GAS_START_ADDR = 0      # 气体浓度数据起始地址
MODBUS_FAN_START_ADDR = 0      # 风机状态起始地址
MODBUS_ENV_START_ADDR = 100    # 环境数据起始地址
```

### 4. 离线地图配置

#### 方法1: 使用OpenMapTiles

1. 下载OpenMapTiles瓦片数据
2. 将瓦片文件按照 `{z}/{x}/{y}.png` 格式放置在 `static/tiles/` 目录下

#### 方法2: 生成自定义瓦片

```bash
# 使用TileServer GL生成瓦片
npm install -g tileserver-gl
tileserver-gl your-map-data.mbtiles --port 8080
```

### 5. 地图加载补全

系统提供了完整的地图完整性检查和修复工具：

```bash
# 检查瓦片完整性
python verify_tile_integrity.py

# 修复缺失的瓦片
python fix_missing_tiles.py
```

系统支持三级地图加载策略：
1. 优先使用本地离线瓦片 (`/static/tiles/{z}/{x}/{y}.png`)
2. 失败时切换到本地瓦片服务器 (`http://localhost:8080/tiles/{z}/{x}/{y}.png`)
3. 最后备选在线地图 (`https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png`)

### 5. 启动系统

```bash
python app.py
```

系统将在 `http://localhost:5000` 启动Web界面。

## 数据格式说明

### GPS数据格式 (NMEA)

系统支持以下NMEA语句：
- `$GPGGA` - 全球定位系统定位数据
- `$GPRMC` - 推荐最小定位信息

### PLC数据格式 (MODBUS)

#### 气体浓度数据 (保持寄存器)
- 地址 0-1: CO浓度 (ppm × 100)
- 地址 2-3: H₂S浓度 (ppm × 100)  
- 地址 4-5: CH₄浓度 (%LEL × 100)
- 地址 6-7: O₂浓度 (% × 100)

#### 风机状态 (线圈)
- 地址 0: 风机1状态 (0=停止, 1=运行)
- 地址 1: 风机2状态 (0=停止, 1=运行)

#### 环境数据 (输入寄存器)
- 地址 100: 温度 (°C × 10)
- 地址 101: 湿度 (%RH × 10)

## 安全阈值配置

系统内置以下气体安全阈值：

| 气体 | 警告阈值 | 危险阈值 | 单位 |
|------|----------|----------|------|
| CO   | 25       | 50       | ppm  |
| H₂S  | 10       | 20       | ppm  |
| CH₄  | 10       | 25       | %LEL |
| O₂   | 19       | 16       | %    |

## 故障排除

### 常见问题

1. **串口连接失败**
   - 检查串口号是否正确
   - 确认设备已正确连接
   - 检查串口是否被其他程序占用

2. **MODBUS通信失败**
   - 验证波特率、数据位、停止位配置
   - 检查从站ID是否正确
   - 确认PLC设备正常工作

3. **GPS数据无效**
   - 确认GPS设备已获得卫星信号
   - 检查NMEA数据格式是否正确
   - 验证串口通信参数

4. **地图无法显示**
   - 检查离线瓦片文件是否正确放置
   - 验证瓦片文件路径和命名格式
   - 确认Web服务器可以访问静态文件

### 日志查看

系统运行时会在控制台输出详细的日志信息，包括：
- 串口连接状态
- 数据接收情况
- 错误信息和异常

## 扩展功能

### 添加新的气体传感器

1. 在 `config.py` 中添加新的寄存器地址
2. 修改 `data_receiver.py` 中的数据解析逻辑
3. 更新前端界面显示新的气体数据

### 数据存储

可以添加数据库支持来存储历史数据：

```python
# 添加SQLite数据库支持
import sqlite3
from datetime import datetime

def save_data_to_db(gps_data, plc_data):
    conn = sqlite3.connect('monitoring.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO monitoring_data 
        (timestamp, lat, lng, co, h2s, ch4, o2, temperature, humidity)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        datetime.now(),
        gps_data.get('lat'),
        gps_data.get('lng'),
        plc_data['gas_levels']['CO'],
        plc_data['gas_levels']['H2S'],
        plc_data['gas_levels']['CH4'],
        plc_data['gas_levels']['O2'],
        plc_data['temperature'],
        plc_data['humidity']
    ))
    
    conn.commit()
    conn.close()
```

## 技术支持

如有技术问题，请检查：
1. 系统日志输出
2. 硬件连接状态
3. 配置参数是否正确
4. 网络连接是否正常

## 许可证

本项目采用MIT许可证，详见LICENSE文件。